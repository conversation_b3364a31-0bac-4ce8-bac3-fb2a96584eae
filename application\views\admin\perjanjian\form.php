<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Per<PERSON><PERSON><PERSON></div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item">
                    <a href="<?= base_url('admin/Perjanjian/index') ?>">Perjanjian Operasi</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page"><?= isset($id_perjanjian) ? 'Ubah' : 'Buat' ?></li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai form -->
<form id="formBuatPerjanjianOperasi">
    <!-- mulai kartu form -->
    <div class="card mb-3">
        <div class="card-body">
            <h5 class="card-title"><?= isset($id_perjanjian) ? 'Ubah' : 'Buat' ?> Perjanjian Operasi</h5>
            <hr>
            <input type="hidden" name="id" id="idPerjanjianOperasi" value="<?= $id_perjanjian ?? null ?>">
            <input type="hidden" name="sore" id="sorePerjanjianOperasi" value="0">
            <input type="hidden" name="id_jk" id="jkPerjanjianOperasi" value="<?= $detail['id_jk'] ?? null ?>">
            <input type="hidden" name="tgl_lahir" id="tglLahirPerjanjianOperasi" value="<?= $detail['id_reservasi'] ?? null ?>">
            <input type="hidden" name="norm" id="normPerjanjianOperasi" value="<?= $detail['norm'] ?? null ?>">
            <input type="hidden" name="id_pendaftaran_operasi" id="idPendaftaranPerjanjianOperasi" value="<?= $detail['ID_PENDAFTARAN_OPERASI'] ?? null ?>">
            <div class="row">
                <!-- mulai kolom 1 -->
                <div class="col">
                    <!-- mulai mr -->
                    <div class="row mb-3 align-items-center">
                        <label for="mrPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Nomor RM</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="mrPerjanjianOperasi" id="mrPerjanjianOperasi" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($ambil_norm as $an): ?>
                                    <option id="mrPerjanjianOperasi<?= $an['NORM'] ?>" value="<?= $an['NORM'] ?>">
                                        <?= $an['NORM'] . ' - ' . $an['NAMA_PASIEN'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir mr -->
                    <!-- mulai nama -->
                    <div class="row mb-3 align-items-center">
                        <label for="namaPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Nama</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="nama" id="namaPerjanjianOperasi" class="form-control form-control-sm" placeholder="Nama pasien" readonly>
                        </div>
                    </div>
                    <!-- akhir nama -->
                    <!-- mulai telepon -->
                    <div class="row mb-3 align-items-center">
                        <label for="teleponPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Nomor telepon</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="tel" name="no_telp" id="teleponPerjanjianOperasi" class="form-control form-control-sm" placeholder="Telepon pasien" readonly>
                        </div>
                    </div>
                    <!-- akhir telepon -->
                    <!-- mulai daftar tunggu -->
                    <div class="row mb-3 align-items-center">
                        <label for="daftarTungguPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Daftar tunggu</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="id_waiting_list" id="daftarTungguPerjanjianOperasi" class="form-select single-select">
                                <option value=" ">Pilih daftar tunggu</option>
                            </select>
                        </div>
                    </div>
                    <!-- akhir daftar tunggu -->
                    <!-- mulai dokter -->
                    <div class="row mb-3 align-items-center">
                        <label for="dokterPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Dokter</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="dokter" id="dokterPerjanjianOperasi" class="form-select single-select">
                                <option value="0">Pilih dokter</option>
                                <?php foreach ($dokter as $d): ?>
                                    <option id="dokterPerjanjianOperasi<?= $d['id_dokter'] ?>" value="<?= $d['id_dokter'] ?>" data-smf="<?= $d['id_smf'] ?>">
                                        <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir dokter -->
                    <!-- mulai rencana -->
                    <div class="row mb-3 align-items-center">
                        <label for="rencanaPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Rencana</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="rencana" id="rencanaPerjanjianOperasi" class="form-control form-control-sm" placeholder="Rencana" value="Operasi" disabled>
                            <input type="hidden" name="id_rencana" id="idRencanaPerjanjianOperasi" value="11">
                        </div>
                    </div>
                    <!-- akhir rencana -->
                    <!-- mulai tujuan operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanOperasiPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Tujuan operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="tujuan_operasi" id="tujuanOperasiPerjanjianOperasi" class="form-select single-select">
                                <!-- <option value="0">Pilih tujuan operasi</option> -->
                                <?php foreach ($tujuan_operasi as $to): ?>
                                    <option id="tujuanOperasiPerjanjianOperasi<?= $to['id_variabel'] ?>" value="<?= $to['id_variabel'] ?>">
                                        <?= $to['variabel'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir tujuan operasi -->
                    <!-- mulai tujuan masuk -->
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanMasukPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Tujuan masuk RS</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="rencana_operasi" id="tujuanMasukPerjanjianOperasi" class="form-select single-select">
                                <!-- <option value=" ">Pilih tujuan masuk</option> -->
                                <?php foreach ($tujuan_masuk as $tm): ?>
                                    <option id="tujuanMasukPerjanjianOperasi<?= $tm['ID'] ?>" value="<?= $tm['ID'] ?>" <?= (isset($detail['ruang_operasi']) && $tm['ID'] == $detail['ruang_operasi']) ? 'selected' : '' ?>>
                                        <?= $tm['DESKRIPSI'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir tujuan masuk -->
                    <!-- mulai tanggal operasi -->
                    <div class="row align-items-center">
                        <label for="tanggalOperasiPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Tanggal operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <div class="input-group input-group-sm">
                                <input type="date" name="tgl_operasi" id="tanggalOperasiPerjanjianOperasi" class="form-control form-control-sm" placeholder="Tanggal operasi" value="<?= $detail['tgl_operasi'] ?? null ?>" readonly>
                                <button class="btn btn-outline-secondary" type="button" id="pilihTanggalOperasiPerjanjianOperasi">Pilih</button>
                            </div>
                        </div>
                    </div>
                    <!-- akhir tanggal operasi -->
                </div>
                <!-- akhir kolom 1 -->
                <!-- mulai kolom 2 -->
                <div class="col">
                    <!-- mulai cara bayar -->
                    <div class="row mb-3 align-items-center">
                        <label for="caraBayarPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Cara bayar</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="id_cara_bayar" id="caraBayarPerjanjianOperasi" class="form-select single-select">
                                <option value="0">Pilih cara bayar</option>
                            </select>
                        </div>
                    </div>
                    <!-- akhir cara bayar -->
                    <!-- mulai kelas rawat -->
                    <div class="row mb-3 align-items-center">
                        <label for="kelasRawatPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Kelas rawat</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="id_kelas" id="kelasRawatPerjanjianOperasi" class="form-select single-select">
                                <option value="0">Pilih kelas rawat</option>
                                <?php foreach ($kelas_rawat as $kr): ?>
                                    <option id="kelasRawatPerjanjianOperasi<?= $kr['ID'] ?>" value="<?= $kr['ID'] ?>">
                                        <?= $kr['DESKRIPSI'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir kelas rawat -->
                    <!-- mulai booking -->
                    <div class="row mb-3 align-items-center">
                        <label for="bookingPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Kode <em>booking</em></label>
                        <div class="col-sm-12 col-md-8">
                            <div class="input-group input-group-sm">
                                <input type="text" name="kode_booking" id="bookingPerjanjianOperasi" class="form-control" placeholder="Masukkan kode booking" value="<?= $detail['kode_booking'] ?? null ?>" readonly>
                                <button class="btn btn-outline-secondary" type="button" id="cariBookingPerjanjianOperasi">
                                    Reservasi
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- akhir booking -->
                    <!-- mulai diagnosis -->
                    <div class="row mb-3 align-items-center">
                        <label for="diagnosisPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Diagnosis</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="diagnosis" id="diagnosisPerjanjianOperasi" class="form-control form-control-sm" placeholder="Diagnosis pasien" value="<?= $detail['diagnosis'] ?? null ?>">
                        </div>
                    </div>
                    <!-- akhir diagnosis -->
                    <!-- mulai tindakan -->
                    <div class="row mb-3 align-items-center">
                        <label for="tindakanPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Tindakan operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="tindakan" id="tindakanPerjanjianOperasi" class="form-control form-control-sm" placeholder="Tindakan operasi" value="<?= $detail['tindakan'] ?? null ?>">
                        </div>
                    </div>
                    <!-- akhir tindakan -->
                    <!-- mulai tanggal rencana masuk -->
                    <div class="row mb-3 align-items-center">
                        <label for="tanggalRencanaMasukPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Tanggal rencana masuk</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="date" name="tgl_rencanaMasuk" id="tanggalRencanaMasukPerjanjianOperasi" class="form-control form-control-sm" placeholder="Tanggal rencana masuk" value="<?= $detail['tgl_rencanaMasuk'] ?? null ?>">
                        </div>
                    </div>
                    <!-- akhir tanggal rencana masuk -->
                    <!-- mulai ruang -->
                    <div class="row mb-3 align-items-center">
                        <label for="ruangPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Ruang</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="ruang" id="ruangPerjanjianOperasi" class="form-control form-control-sm" placeholder="Nama ruang"  readonly>
                            <input type="hidden" name="id_ruang" id="idRuangPerjanjianOperasi" >
                        </div>
                    </div>
                    <!-- akhir ruang -->
                    <!-- mulai persetujuan -->
                    <div class="row mb-3 align-items-center">
                        <label for="persetujuanPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Persetujuan instalasi</label>
                        <div class="col-sm-12 col-md-8">
                            <div class="form-check">
                                <input type="checkbox" name="persetujuan_instalasi" id="persetujuanPerjanjianOperasi1" class="form-check-input persetujuanPerjanjianOperasi" value="1" <?= isset($detail['PERSETUJUAN']) ? ($detail['PERSETUJUAN'] == 1 ? 'checked' : null) : null ?>>
                                <label for="persetujuanPerjanjianOperasi1" class="form-check-label">Setuju</label>
                            </div>
                        </div>
                    </div>
                    <!-- akhir persetujuan -->
                    <!-- mulai join operasi -->
                    <div class="row align-items-center">
                        <label for="joinOperasiPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label"><em>Join</em> operasi</label>
                        <div class="col-sm-12 col-md-8 px-4">
                            <div class="row">
                                <?php foreach ($join_operasi as $jo): ?>
                                    <div class="col form-check form-check-inline">
                                        <input type="radio" name="join_operasi" id="joinOperasiPerjanjianOperasi<?= $jo['id_variabel'] ?>" class="form-check-input join-operasi-perjanjian-operasi" value="<?= $jo['id_variabel'] ?>" <?= isset($detail['join_operasi']) ? ($jo['id_variabel'] == $detail['join_operasi'] ? 'checked' : null) : null ?>>
                                        <label for="joinOperasiPerjanjianOperasi<?= $jo['id_variabel'] ?>" class="form-check-label">
                                            <?= $jo['variabel'] ?>
                                        </label>
                                    </div>
                                <?php endforeach ?>
                            </div>
                        </div>
                    </div>
                    <!-- akhir join operasi -->
                </div>
                <!-- akhir kolom 2 -->
            </div>
        </div>
    </div>
    <!-- akhir kartu form -->
    <!-- mulai kartu dokter lain -->
    <div class="card mb-3 d-none" id="cardDokterLainPerjanjianOperasi">
        <div class="card-body">
            <h5 class="card-title">Dokter Bedah Lain dan Tindakannya</h5>
            <hr>
            <table class="table table-striped table-bordered table-hover w-100" id="tabelDokterLainPerjanjianOperasi">
                <thead>
                    <tr>
                        <th class="text-center" scope="col">No.</th>
                        <th class="text-center" scope="col">Dokter</th>
                        <th class="text-center" scope="col">Tindakan</th>
                    </tr>
                <tbody id="isiDokterLainPerjanjianOperasi"></tbody>
                <tfoot>
                    <tr>
                        <th class="text-center" scope="col">No.</th>
                        <th class="text-center" scope="col">Dokter</th>
                        <th class="text-center" scope="col">Tindakan</th>
                    </tr>
                </tfoot>
                </thead>
            </table>
        </div>
    </div>
    <!-- akhir kartu dokter lain -->
    <!-- mulai aksi -->
    <div class="row">
        <div class="col-1">
            <button type="button" class="btn btn-primary" id="simpanPerjanjianOperasi">Simpan</button>
        </div>
    </div>
    <!-- akhir aksi -->
</form>
<!-- akhir form -->

<!-- mulai modal pilih tanggal -->
<div class="modal fade" id="modalTanggalPerjanjianOperasi" aria-labelledby="pilihTanggalPerjanjianOperasi" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pilihTanggalPerjanjianOperasi">Pilih Tanggal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-sm-1">
                        <button type="button" class="btn btn-primary btn-block" id="tambahJadwalPerjanjianOperasi">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </div>
                    <div class="col-sm-1 text-center">
                        <i class="fa-solid fa-angle-left fa-3x" id="prevPerjanjianOperasi" style="cursor: pointer;opacity: 0.6"></i>
                    </div>
                    <div class="col-sm-9">
                        <input class="form-control bg-warning text-dark text-center fw-bold shadow-none" type="month" name="bulan" id="bulanPerjanjianOperasi" readonly>
                    </div>
                    <div class="col-sm-1 text-center">
                        <i class="fa-solid fa-angle-right fa-3x" id="nextPerjanjianOperasi" style="cursor: pointer;opacity: 0.6"></i>
                    </div>
                </div>
                <div class="row" id="jadwalPerjanjianOperasi"></div>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal pilih tanggal -->

<!-- mulai modal sudah ada -->
<div class="modal fade" id="modalSudahAdaPerjanjianOperasi" aria-labelledby="judulModalSudahAdaPerjanjianOperasi" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalSudahAdaPerjanjianOperasi">Peringatan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="msgSudahAdaPerjanjianOperasi"></p>
                <!--Perjanjian operasi sudah pernah didaftarkan.-->
                <input type="hidden" id="idSudahAdaPerjanjianOperasi" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal" id="batalSudahAdaPerjanjianOperasi">Batal</button>
                <button type="button" class="btn btn-secondary" id="cetakSudahAdaPerjanjianOperasi">Cetak</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal sudah ada -->

<!-- mulai modal persetujuan -->
<div class="modal fade" id="modalPersetujuanPerjanjianOperasi" aria-labelledby="judulModalPersetujuanPerjanjianOperasi" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalPersetujuanPerjanjianOperasi">Konfirmasi Persetujuan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning border-0 bg-warning alert-dismissible fade show py-2 d-none" id="notifKuotaSorePerjanjianOperasi">
                    <div class="d-flex align-items-center">
                        <div class="font-35 text-dark">
                            <i class='bx bx-info-circle'></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-0 text-dark">Anda akan dimasukkan ke kuota sore, apakah Anda yakin?</h6>
                            <div class="text-dark">Pastikan data yang Anda masukkan benar, lalu klik 'Setuju' jika ingin melanjutkan</div>
                        </div>
                    </div>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="tidak_cetak_sore" id="tidakCetakKuotaSorePerjanjianOperasi" class="form-check-input tidakCetakKuotaSorePerjanjianOperasi" value="1">
                    <label for="persetujuanPerjanjianOperasi1" class="form-check-label">Tidak ingin mencetak</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="batalKuotaSorePerjanjianOperasi" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="setujuKuotaSorePerjanjianOperasi">Setuju</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal persetujuan -->

<!-- mulai modal tambah jadwal dokter -->
<div class="modal fade " id="modalJadwalPerjanjianOperasi" aria-labelledby="judulModalJadwalPerjanjianOperasi" aria-hidden="true">
    <form id="formJadwalPerjanjianOperasi" autocomplete="off">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mt3">Form Tambah Jadwal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- mulai ruang -->
                    <div class="row mb-3 align-items-center">
                        <label class="col-sm-3 col-form-label">Ruangan</label>
                        <div class="col-sm-9">
                            <select class="form-control form-control-sm select2" name="jruangan" id="valJruangan" required>
                                <!-- <option value="">Pilih Ruangan</option> -->
                            </select>
                        </div>
                    </div>
                    <!-- <div class="row mb-3 align-items-center">
                        <label class="col-sm-3 col-form-label">Ruang</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control form-control-sm" name="jruangan" id="valJruangan" value="Instalasi Bedah Pusat" readonly>
                        </div>
                    </div> -->
                    <!-- akhir ruang -->
                    <!-- mulai tanggal -->
                    <div class="row mb-3 align-items-center">
                        <label class="col-sm-3 col-form-label">Tanggal</label>
                        <div class="col-sm-9">
                            <input type="date" class="form-control form-control-sm" name="jtanggal" id="valjTanggal" required>
                        </div>
                    </div>
                    <!-- akhir tanggal -->
                    <!-- mulai waktu -->
                    <div class="row mb-3 align-items-center">
                        <label class="col-sm-3 col-form-label">Waktu</label>
                        <div class="col-sm-9">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">dari</span>
                                <input type="number" class="form-control form-control-sm" name="awal" id="valAwal" placeholder="Waktu mulai" min="0" max="23">
                                <span class="input-group-text">sampai</span>
                                <input type="number" class="form-control form-control-sm" name="akhir" id="valAkhir" placeholder="Waktu selesai" min="0" max="23">
                            </div>
                        </div>
                    </div>
                    <!-- akhir waktu -->
                    <!-- mulai kuota -->
                    <div class="row align-items-center">
                        <label class="col-sm-3 col-form-label">Kuota</label>
                        <div class="col-sm-9">
                            <input type="number" class="form-control form-control-sm" name="kuota" id="valKuota" max="999" placeholder="Jumlah pasien maksimal">
                        </div>
                    </div>
                    <!-- akhir kuota -->
                    <input type="hidden" name="jdokter" id="valJdokter">
                    <input type="hidden" name="jid" id="valJid">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="simpanJadwalPerjanjianOperasi">Simpan</button>
                </div>
            </div>
        </div>
    </form>
</div>
<!-- akhir modal tambah jadwal dokter -->

<script>
    $(document).ready(function () {
        $('#valJruangan').select2({
            placeholder: 'Pilih Ruangan',
            allowClear: true,
            dropdownParent: $('#modalJadwalPerjanjianOperasi'),
            ajax: {
                url: '<?= base_url('admin/perjanjian/get_ruangan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });
        const modalSudahAdaPerjanjianOperasi = new bootstrap.Modal(document.getElementById('modalSudahAdaPerjanjianOperasi'));
        const modalPersetujuanPerjanjianOperasi = new bootstrap.Modal(document.getElementById('modalPersetujuanPerjanjianOperasi'));

        // mulai cari data pasien
        $('#mrPerjanjianOperasi').change(function () {
            let norm = $(this).val();
            $('#daftarTungguPerjanjianOperasi, #dokterPerjanjianOperasi, #tujuanOperasiPerjanjianOperasi, #tujuanMasukPerjanjianOperasi, #caraBayarPerjanjianOperasi, #kelasRawatPerjanjianOperasi').val(0).trigger('change');

            // mulai hapus input
            if ($('#idPerjanjianOperasi').val() === null) {
                $('#sorePerjanjianOperasi').val(0);
                $("#jkPerjanjianOperasi, #tglLahirPerjanjianOperasi, #normPerjanjianOperasi, #idPendaftaranPerjanjianOperasi, :text, input[type='date'], input[type='tel']").val(null);
                $('#idRencanaPerjanjianOperasi').val(11);
                $('#rencanaPerjanjianOperasi').val('Operasi');
                $('#persetujuanPerjanjianOperasi1, .join-operasi-perjanjian-operasi').prop('checked', false);
                $('#isiDokterLainPerjanjianOperasi').html(null);
                $('#cardDokterLainPerjanjianOperasi').addClass('d-none');
            }
            // akhir hapus input

            if (norm !== null) {
                $.ajax({
                    type: 'POST',
                    url: "<?= base_url('admin/Perjanjian/ambil_data_pasien_daftar_tunggu') ?>",
                    data: {
                        norm: norm,
                    },
                    success: function (data) {
                        if (data === null || data === '' || data === 'null') {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: 'Data tidak ditemukan',
                                sound: false,
                                title: 'Peringatan'
                            });
                        } else {
                            Lobibox.notify('success', {
                                icon: 'bx bx-check-circle',
                                msg: 'Data ditemukan',
                                sound: false,
                                title: 'Berhasil'
                            });

                            // mulai ambil data
                            let json = JSON.parse(data);
                            $('#normPerjanjianOperasi').val(norm);
                            $('#namaPerjanjianOperasi').val(json['nama']);
                            $('#teleponPerjanjianOperasi').val(json['telepon']);
                            $('#jkPerjanjianOperasi').val(json['id_jk']);
                            $('#tglLahirPerjanjianOperasi').val(json['tgl_lahir']);

                            // mulai ambil daftar tunggu
                            $('#daftarTungguPerjanjianOperasi').select2({
                                placeholder: 'Pilih daftar tunggu',
                                ajax: {
                                    url: "<?= base_url('admin/Daftar_tunggu/pilih_daftar_tunggu/') ?>" + norm,
                                    dataType: 'json',
                                    delay: 250,
                                    processResults: function (data) {
                                        return {
                                            results: data
                                        };
                                    },
                                    cache: true
                                }
                            });
                            // akhir ambil daftar tunggu
                            // akhir ambil data
                        }
                    }
                });
            }
        }).select2({
            placeholder: 'Pilih nomor rekam medis'
        }).val("<?= $detail['norm'] ?? 0 ?>").trigger('change');
        // akhir cari data pasien

        // mulai dokter
        $('#dokterPerjanjianOperasi').select2({
            placeholder: 'Pilih dokter'
        }).val(<?= $detail['id_dokter'] ?? 0 ?>).trigger('change');
        // akhir dokter

        // mulai daftar tunggu
        $('#daftarTungguPerjanjianOperasi').select2().change(function () {
            let id = $(this).val();
            if ($.trim(id)) {
                $.ajax({
                    type: 'POST',
                    url: "<?= base_url('admin/Daftar_tunggu/ambil_pilihan') ?>",
                    data: {
                        id: id,
                    },
                    success: function (data) {
                        if ($.trim(data)) {
                            // mulai ambil data
                            let json = JSON.parse(data);
                            let detail = json['detail'];

                            // mulai detail
                            $('#idPendaftaranPerjanjianOperasi').val(detail['id_pendaftaran_operasi']);
                            $('#dokterPerjanjianOperasi').val(detail['id_dokter']).trigger('change');
                            $('#diagnosisPerjanjianOperasi').val(detail['diagnosis']);
                            $('#tindakanPerjanjianOperasi').val(detail['tindakan']);
                            $('#tujuanOperasiPerjanjianOperasi').val(detail['tujuan_operasi']).trigger('change');
                            $('#kelasRawatPerjanjianOperasi').val(detail['kelas']).trigger('change');
                            

                            // mulai tanggal rencana operasi baru
                            let tanggal_rencana = null;
                            if (detail['tanggal_operasi'] === '0000-00-00') {
                                tanggal_rencana = null;
                                // $('#idRuangPerjanjianOperasi').val(null);
                                // $('#ruangPerjanjianOperasi').val(null);
                            } else {
                                tanggal_rencana = detail['tanggal_operasi'];
                                // $('#idRuangPerjanjianOperasi').val(detail['ruang_operasi']);
                                // $('#ruangPerjanjianOperasi').val(detail['ruangan']);
                                $('#tujuanMasukPerjanjianOperasi').val(detail['ruang_operasi']).trigger('change');
                            }
                            $('#tanggalOperasiPerjanjianOperasi').val(tanggal_rencana);
                            // akhir tanggal rencana operasi baru

                            // mulai cara bayar
                            if (detail['id_penjamin'] !== null && detail['id_penjamin'] !== '') {
                                $('#caraBayarPerjanjianOperasi').select2('trigger', 'select', {
                                    data: {
                                        id: detail['id_penjamin'],
                                        text: detail['penjamin']
                                    }
                                });
                            }
                            // akhir cara bayar

                            // mulai join operasi
                            if (detail['join_operasi'] !== null && detail['join_operasi'] !== '') {
                                $('#joinOperasiPerjanjianOperasi' + detail['join_operasi']).prop('checked', true);
                            } else {
                                $('.join-operasi-perjanjian-operasi').prop('checked', false);
                            }
                            // akhir join operasi
                            // akhir detail

                            // mulai dokter lain
                            let tindakan = json['tindakan'];
                            let isi = null;
                            let i = 0;
                            let rencana_tindakan = null;

                            $('#isiDokterLainPerjanjianOperasi').html(null);
                            if (tindakan.length) {
                                $.each(tindakan, function (j) {
                                    // mulai rencana tindakan
                                    if (tindakan[j].rencana_tindakan !== null) {
                                        rencana_tindakan = tindakan[j].rencana_tindakan;
                                    } else {
                                        rencana_tindakan = '-';
                                    }
                                    // akhir rencana tindakan

                                    // mulai isi
                                    isi = '<tr>' +
                                        '<td>' + ++i + ".<input type='hidden' class='isiIdDokterLainEditPerjanjianOperasi' name='dokter_bedah[]' value='" + tindakan[j].id_dokter_bedah_lain + "'></td>" +
                                        '<td>' + tindakan[j].dokter_bedah_lain + '</td>' +
                                        '<td>' + rencana_tindakan + '</td>' +
                                        '</tr>';
                                    $(isi).hide().appendTo('#isiDokterLainPerjanjianOperasi').fadeIn(1000);
                                    // akhir isi
                                });
                                $('#cardDokterLainPerjanjianOperasi').removeClass('d-none');
                            } else {
                                $('#cardDokterLainPerjanjianOperasi').addClass('d-none');
                            }
                            // akhir dokter lain
                            // akhir ambil data
                        }
                    }
                });
            }
        });
        <?php if (isset($detail['id_waiting_list_operasi'])): ?>
            $('#daftarTungguPerjanjianOperasi').append("<option id='<?= $detail['id_waiting_list_operasi'] ?>' value='<?= $detail['id_waiting_list_operasi'] ?>'><?= $detail['nokun'] . ' - ' . $detail['tgl_dibuat'] ?></option>").trigger('change');
        <?php endif ?>
        // akhir daftar tunggu

        // mulai tujuan operasi
        $('#tujuanOperasiPerjanjianOperasi').select2({
            placeholder: 'Pilih Tujuan Operasi'
        }).val("<?= $detail['TUJUANOPERASI'] ?? 0 ?>").trigger('change');
        // akhir tujuan operasi

        // mulai tujuan masuk
        $('#tujuanMasukPerjanjianOperasi').select2({
            placeholder: 'Pilih Tujuan Masuk'
        }).val("<?= $detail['id_tujuan_dirawat'] ?? 0 ?>").trigger('change');
        // akhir tujuan masuk

        // mulai pilih tanggal operasi
        $('#pilihTanggalOperasiPerjanjianOperasi').click(function () {
            // $('.rencana').css('display', 'none');
            // $('.operasi').css('display', 'none');
            if ($('#mrPerjanjianOperasi').val() === null || $('#dokterPerjanjianOperasi').val() === null || $('#namaPerjanjianOperasi').val() === null || $('#teleponPerjanjianOperasi').val() === null) { // Periksa form
                if ($('#mrPerjanjianOperasi').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Nomor RM wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
                if ($('#dokterPerjanjianOperasi').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Dokter wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
                if ($('#namaPerjanjianOperasi').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Nama wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
                if ($('#teleponPerjanjianOperasi').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Nomor telepon wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
            } else {
                jadwal();
            }
        });
        // akhir pilih tanggal operasi

        // mulai cara bayar
        $('#caraBayarPerjanjianOperasi').select2({
            placeholder: 'Pilih cara bayar',
            ajax: {
                url: "<?= base_url('admin/Perjanjian/cara_bayar') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        // akhir cara bayar

        // mulai kelas rawat
        $('#kelasRawatPerjanjianOperasi').select2({
            placeholder: 'Pilih kelas rawat'
        }).val("<?= $detail['id_kelas'] ?? 0 ?>").trigger('change');
        // akhir kelas rawat

        // mulai cari kode booking
        $('#cariBookingPerjanjianOperasi').click(function () {
            $.ajax({
                url: "<?= base_url('admin/Perjanjian/kode_booking') ?>",
                success: function (data) {
                    $('#bookingPerjanjianOperasi').val(data);
                }
            });
        });
        // akhir cari kode booking

        // mulai bulan
        let tanggal = new Date();
        let bulan = ('0' + (tanggal.getMonth() + 1)).slice(-2);
        let tahun = tanggal.getFullYear();
        $('#bulanPerjanjianOperasi').val(tahun + '-' + bulan);
        // akhir bulan

        // mulai ganti bulan operasi
        // mulai bulan sebelumnya
        $('#prevPerjanjianOperasi').hover(function () {
            $(this).css('opacity', 1);
        }, function () {
            $(this).css('opacity', 0.6);
        });
        $('#prevPerjanjianOperasi').click(function () {
            if (tahun + '-' + bulan != $('#bulanPerjanjianOperasi').val()) {
                document.getElementById('bulanPerjanjianOperasi').stepDown();
                jadwal();
            } else {
                Lobibox.notify('warning', {
                    icon: 'bx bx-error',
                    msg: 'Tidak bisa mengambil data sebelum bulan ini',
                    sound: false,
                    title: 'Peringatan'
                });
            }
        });
        // akhir bulan sebelumnya

        // mulai bulan seleanjutnya
        $('#nextPerjanjianOperasi').hover(function () {
            $(this).css('opacity', 1);
        }, function () {
            $(this).css('opacity', 0.6);
        });
        $('#nextPerjanjianOperasi').click(function () {
            document.getElementById('bulanPerjanjianOperasi').stepUp();
            jadwal();
        });
        // akhir bulan selanjutnya
        // akhir ganti bulan operasi

        // mulai fungsi jadwal
        function jadwal() {
            let dokter = $('#dokterPerjanjianOperasi').val();
            let norm = $('#mrPerjanjianOperasi').val();
            let smf = $('#dokterPerjanjianOperasi option:selected').data('smf');
            let txtdokter = $('#dokterPerjanjianOperasi').text();
            let bulan = $('#bulanPerjanjianOperasi').val();
            let poli = 0;

            $.ajax({
                url: "<?= base_url('admin/Perjanjian/jadwal') ?>",
                type: 'POST',
                data: {
                    dokter: dokter,
                    bulan: bulan,
                    tahun: tahun,
                    smf: smf,
                    norm: norm,
                    poli: poli
                },
                dataType: 'JSON',
                success: function (data) {
                    $('#jadwalPerjanjianOperasi').html(null);
                    $('#modalTanggalPerjanjianOperasi').modal('show');

                    if (data.length) {
                        $.each(data, function (index, element) {
                            let bg = 'bg-info';
                            if (element.id_ruangan == 105020201 || element.id_ruangan == 105020202) {
                                bg = 'bg-success';
                            }

                            let cls = 'tanggal';
                            if (parseInt(element.jumlah) >= parseInt(element.kuota)) {
                                bg = 'bg-danger';
                                // cls = "penuh";
                            }

                            let kuota = element.kuota;
                            if (kuota == -1) {
                                kuota = '~';
                            }

                            let pagi = '';
                            let sore = '';
                            let ikon_sore = '';
                            let ikon_pagi = '';

                            if (element.kuota !== null) {
                                ikon_pagi = '<a class="link-light changeSchedule" data-status="2" data-id="' + element.id + '" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>'
                            }

                            if (element.kuota_sore !== null) {
                                sore = '<div class="card-body p-0 d-none" id="areaSore' + element.id + '">' +
                                    '<div class="d-flex justify-content-between">' +
                                    '<h4><span class="badge text-bg-dark rounded-start-0 rounded-end-pill"><i class="fas fa-clock fa-sm"></i> ' + element.waktu_sore + '</span></h4>' +
                                    '<h6 class="mt-2">' + ikon_pagi + '</h6>' +
                                    '<h4 class="card-title text-right"><span class="badge text-bg-dark rounded-start-pill rounded-end-0">' + element.jumlah_sore + '/' + element.kuota_sore + '</span></h4>' +
                                    '</div>' +
                                    '<div class="card-body p-0 m-0 ' + cls + ' " tanggal="' + element.tanggal + '" ruangan="' + element.ruangan + '" idr="' + element.id_ruangan + '" sore="1" style="cursor: pointer;">' +
                                    '<p class="card-text text-center text-warning fw-bold fs-1"><u>' + element.day + '</u></p>' +
                                    '</div>' +
                                    '</div>';
                                ikon_sore = '<a class="link-light changeSchedule" data-status="1" data-id="' + element.id + '" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>';
                            }

                            if (element.kuota !== null) {
                                pagi = '<div class="card-body p-0" id="areaPagi' + element.id + '">' +
                                    '<div class="d-flex justify-content-between">' +
                                    '<h4><span class="badge text-bg-primary rounded-start-0 rounded-end-pill"><i class="fas fa-clock" style="font-size: 14px;"></i> ' + element.waktu + '</span></h4>' +
                                    '<h6 class="mt-2">' + ikon_sore + '</h6>' +
                                    '<h4 class="card-title text-right"><span class="badge text-bg-warning rounded-start-pill rounded-end-0">' + element.jumlah + '/' + kuota + '</span></h4>' +
                                    '</div>' +
                                    '<div class="card-body p-0 m-0 ' + cls + ' " tanggal="' + element.tanggal + '" ruangan="' + element.ruangan + '" idr="' + element.id_ruangan + '" sore="0" style="cursor: pointer;">' +
                                    '<p class="card-text text-center text-warning fw-bold fs-1"><u>' + element.day + '</u></p>' +
                                    '</div>' +
                                    '</div>';
                            }

                            if (element.kuota !== null || element.kuota_sore !== null) {
                                $('#jadwalPerjanjianOperasi').append(
                                    '<div class="col-3 mb-3">' +
                                    '<div class="card text-white h-100" style="background-color:' + element.color + '">' +
                                    '<div class="card-header text-center text-warning" style="font-size: 12px;">' + element.ruangan + '</div>' +
                                    pagi +
                                    sore +
                                    '<div class="card-footer text-center text-warning">' + element.hari + '</div>' +
                                    '</div>');
                                if (element.kuota !== null) {
                                    if (element.jumlah == element.kuota && element.kuota_sore !== null) {
                                        $('#areaPagi' + element.id).addClass('d-none');
                                        $('#areaSore' + element.id).removeClass('d-none');
                                    } else {
                                        $('#areaPagi' + element.id).removeClass('d-none');
                                        $('#areaSore' + element.id).addClass('d-none');
                                    }
                                } else {
                                    $('#areaPagi' + element.id).addClass('d-none');
                                    $('#areaSore' + element.id).removeClass('d-none');
                                }
                            }
                        });

                        $('.changeSchedule').click(function () {
                            let id = $(this).attr('data-id');
                            let status = $(this).attr('data-status');
                            if (status == 1) {
                                $('#areaPagi' + id).addClass('d-none');
                                $('#areaSore' + id).removeClass('d-none');
                            } else {
                                $('#areaPagi' + id).removeClass('d-none');
                                $('#areaSore' + id).addClass('d-none');
                            }
                        });
                    } else {
                        // $('#mt1').html(txtdokter);
                        $('#jadwalPerjanjianOperasi').append(
                            "<div class='col'>" +
                            "<div class='alert alert-warning' role='alert'>" +
                            "Jadwal dokter belum tersedia" +
                            "</div>" +
                            "</div>"
                        );
                    }
                },
                error: function () {
                    Lobibox.error('Ada Kesalahan!');
                }
            });
        }
        // akhir fungsi jadwal

        // mulai klik tanggal
        $(document).on('click', '.tanggal', function () {
            let tanggal = $(this).attr('tanggal');
            let ruangan = $(this).attr('ruangan');
            let idr = $(this).attr('idr');
            let sore = $(this).attr('sore');

            $('#btnSelesai').attr('disabled', false);
            $('#tanggalOperasiPerjanjianOperasi').val(tanggal);
            $('#sorePerjanjianOperasi').val(sore);
            $('#idRuangPerjanjianOperasi').val(idr);
            $('#ruangPerjanjianOperasi').val(ruangan);
            $('#modalTanggalPerjanjianOperasi').modal('hide');
        });
        // akhir klik tanggal

        // mulai simpan
        $('#simpanPerjanjianOperasi').click(function () {
            let form = $('#formBuatPerjanjianOperasi').serialize();

            $.ajax({
                url: "<?= base_url('admin/Perjanjian/cek') ?>",
                dataType: 'json',
                type: 'POST',
                data: form,
                success: function (data) {
                    if (data.status === 503) { // default
                        $('#idSudahAdaPerjanjianOperasi').val(data.id);
                        modalSudahAdaPerjanjianOperasi.show();
                        $('#msgSudahAdaPerjanjianOperasi').html('');
                        $('#msgSudahAdaPerjanjianOperasi').html(data.message);
                    } else if (data.status === 406) { // sudah penuh
                        modalPersetujuanPerjanjianOperasi.hide();
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: data.message,
                            sound: false,
                            title: 'Peringatan'
                        });
                    } else if (data.status === 'failed') { //rules
                        $.each(data.errors, function (index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    } else { // kuota sore
                        modalPersetujuanPerjanjianOperasi.show();
                        $('#notifKuotaSorePerjanjianOperasi').addClass('d-none');
                    }
                }
            });
        });
        // akhir simpan

        // mulai cetak
        $('#cetakSudahAdaPerjanjianOperasi').click(function () {
            window.requestPrint({
                NAME: 'aplikasi.perjanjian.CetakPerjanjian_1',
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    ID: $('#idSudahAdaPerjanjianOperasi').val(),
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: 'CetakPerjanjian',
                CONNECTION_NUMBER: 0,
                COPIES: 1,
                id: 'data.model.RequestReport-1'
            });
        });
        // akhir cetak

        // mulai batal
        $('#batalSudahAdaPerjanjianOperasi').click(function () {
            $.ajax({
                url: "<?= base_url('admin/Perjanjian/batal') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id: $('#idSudahAdaPerjanjianOperasi').val(),
                },
                success: function (data) {
                    if (data.status == 200) {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Perjanjian dibatalkan',
                            sound: false,
                            title: 'Berhasil'
                        });
                        window.location.href = "<?= base_url('admin/Perjanjian/index') ?>";
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: data.message,
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir batal

        // mulai setuju kuota sore
        $('#setujuKuotaSorePerjanjianOperasi').click(function () {
            let form = $('#formBuatPerjanjianOperasi').serialize();
            let tidak_cetak = $('.tidakCetakKuotaSorePerjanjianOperasi').length;

            let type;
            let ext;
            let print;
            if (tidak_cetak === 1) {
                let type = 'PDF';
                let ext = 'pdf';
                let print = false;
            } else {
                let type = 'Word';
                let ext = 'docs';
                let print = true;
            }

            $.ajax({
                url: "<?= base_url('admin/Perjanjian/aksi/tambah') ?>",
                dataType: 'json',
                type: 'post',
                data: form,
                success: function (data) {
                    modalPersetujuanPerjanjianOperasi.hide();
                    if (data.status === 200) {
                        if (data.status_prosedur === 1) {
                            prosedur_perjanjian();
                        } else {
                            // mulai cetak
                            window.requestPrint({
                                NAME: 'aplikasi.perjanjian.CetakPerjanjian',
                                TYPE: 'Pdf', //Word
                                EXT: 'pdf', //docs
                                PARAMETER: {
                                    ID: data.id,
                                },
                                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                                PRINT_NAME: 'CetakPerjanjian',
                                CONNECTION_NUMBER: 0,
                                COPIES: 1,
                                id: 'data.model.RequestReport-1'
                            });
                            // akhir cetak

                            Lobibox.notify('success', {
                                icon: 'bx bx-check-circle',
                                msg: 'Berhasil menyimpan perjanjian',
                                sound: false,
                                title: 'Berhasil'
                            });
                        }
                    } else if (data.status === 503) {
                        $('#idSudahAdaPerjanjianOperasi').val(data.id);
                        modalSudahAdaPerjanjianOperasi.show();
                    } else if (data.status === 406) {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Mohon pilih hari lain',
                            sound: false,
                            title: 'Peringatan'
                        });
                    } else {
                        $.each(data.errors, function (index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                }
            });
        });
        // akhir setuju kuota sore

        // mulai prosedur perjanjian
        function prosedur_perjanjian() {
            $.ajax({
                url: "<?= base_url('admin/Perjanjian/prosedur') ?>",
                dataType: 'json',
                type: 'post',
                data: form,
                success: function (data) {
                    if (data.status === 200) {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Perjanjian prosedur sudah dibuat',
                            sound: false,
                            title: 'Peringatan'
                        });
                        location.reload();
                    } else if (data.status === 503) {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Perjanjian prosedur sudah ada',
                            sound: false,
                            title: 'Peringatan'
                        });
                    } else if (data.status === 406) {
                        Lobibox.notify('error', {
                            icon: 'bx bx-x-circle',
                            msg: 'Gagal membuat perjanjian prosedur',
                            sound: false,
                            title: 'Gagal'
                        });
                    }
                }
            });
        }
        // akhir prosedur perjanjian

        // mulai tambah jadwal dokter
        $('#tambahJadwalPerjanjianOperasi').click(function () {
            $('#modalJadwalPerjanjianOperasi').modal('show');
            $('#formJadwalPerjanjianOperasi')[0].reset();
            $('#valJdokter').val($('#dokterPerjanjianOperasi').val());
        });
        // akhir tambah jadwal dokter

        // mulai simpan jadwal dokter
        $('#simpanJadwalPerjanjianOperasi').click(function () {
            let data = $('#formJadwalPerjanjianOperasi').serialize();
            $.ajax({
                url: "<?= base_url('admin/perjanjian/tambah_jadwal') ?>",
                dataType: 'json',
                type: 'POST',
                data: data,
                success: function (data) {
                    if (data.status == '200') {
                        $('#modalJadwalPerjanjianOperasi').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Berhasil menambah jadwal',
                            sound: false,
                            title: 'Berhasil'
                        });
                        jadwal();
                    } else {
                        $.each(data.errors, function (index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                },
                error: function (e) {
                    toastr.error('Ada kesalahan');
                }
            });
        });
        // akhir simpan jadwal dokter
    });
</script>