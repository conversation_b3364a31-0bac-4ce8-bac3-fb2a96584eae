<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pasien_model extends CI_Model
{
    protected $_table = 'master.pasien mp';
    protected $_primary_key = 'mp.NORM';

    public function __construct()
    {
        parent::__construct();
    }

    public function ambil_data($nomr)
    {
        $this->db->select(
            "mp.NORM, master.getNamaLengkap(mp.NORM) nama, mp.JENIS_KELAMIN id_jk,
            DATE_FORMAT(mp.TANGGAL_LAHIR, '%Y-%m-%d') tgl_lahir,
            TIMESTAMPDIFF(YEAR, mp.TANGGAL_LAHIR, CURDATE()) AS umur"
        );
        $this->db->from($this->_table);
        $this->db->where('mp.NORM', $nomr);
        $query = $this->db->get();
        return $query->row();
    }

    public function ambil_data_daftar_tunggu($nomr)
    {
        $this->db->select(
            "master.getNamaLengkap(mp.NORM) nama, IF (mkp.NOMOR IS NULL, 0, mkp.NOMOR) telepon,
            mp.JENIS_KELAMIN id_jk, DATE_FORMAT(mp.TANGGAL_LAHIR, '%Y-%m-%d') tgl_lahir"
        );
        $this->db->from($this->_table);
        $this->db->join('master.kontak_pasien mkp', 'mkp.NORM = mp. NORM', 'left');
        $this->db->where('mp.NORM', $nomr);
        $query = $this->db->get();
        return $query->row();
    }
}

// End of File Pasien_model.php
// Location: ./application/models/Pasien_model.php