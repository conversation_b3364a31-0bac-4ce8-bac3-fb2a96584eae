<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Dokter_model extends CI_Model
{
    protected $_table = 'master.dokter d';
    protected $_primary_key = 'd.id';

    public function dokter()
    {
        $this->db->select(
            'd.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from($this->_table);
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function dokter_operasi()
    {
        $this->db->select(
            'd.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, p.SMF id_smf, smf.DESKRIPSI smf,
            (
                SELECT COUNT(rmp.ID)
                FROM remun_medis.perjanjian rmp
                WHERE rmp.ID_RUANGAN = 105090101
                    AND rmp.STATUS = 1
                    AND rmp.ID_DOKTER = d.ID
                    AND rmp.TANGGAL >= CURDATE()
            ) jml_perjanjian'
        );
        $this->db->from($this->_table);
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->join('master.dokter_ruangan mdr', 'mdr.DOKTER = d.ID', 'left');
        $this->db->where('mdr.RUANGAN', '105090101'); // Instalasi Bedah Pusat
        $this->db->where('mdr.STATUS', 1);
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function dokter_daftar_tunggu()
    {
        $this->db->select(
            "d.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, (COUNT(wlo.id) + COUNT(tapo.id)) jml_wl"
        );
        $this->db->from($this->_table);
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id_dokter = d.ID', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
        $this->db->join(
            'medis.tb_asisten_pendaftaran_operasi tapo',
            'tapo.id_pendaftaran = tpo.id AND tapo.asisten_bedah = d.ID AND tapo.asisten_bedah = d.ID AND tapo.status = 1 OR tapo.status IS NULL',
            'left'
        );
        $this->db->join('medis.tb_laporan_operasi lpo', 'lpo.id_waiting_list = wlo.id', 'left');
        $this->db->where('ps.status !=', 2); // meninggal
        $this->db->where('lpo.id_waiting_list IS NULL'); // belum ada laporan operasi
        $this->db->where_in('wlo.status', [1, 2]);
        $this->db->group_by('d.ID');
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function dokter_anestesi()
    {
        $this->db->select(
            'd.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from($this->_table);
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where_in('smf.ID', [6, 46, 55]);
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }
}

// End of File Dokter_model.php
// Location: ./application/models/operasi/Dokter_model.php