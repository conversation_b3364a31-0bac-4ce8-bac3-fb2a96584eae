<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Manajemen Pengguna</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item">Manajemen Pengguna</li>
                <li class="breadcrumb-item">
                    <a href="<?= base_url('admin/manajemen_pengguna/Pengguna/index') ?>">Per Pengguna</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Form</li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Atur Manajemen Pengguna</h5>
        <hr>
        <form id="formManajemenPengguna">
            <!-- mulai nama pegawai -->
            <div class="row mb-3 align-items-center">
                <label for="pegawaiManajemenPengguna" class="col-sm-12 col-md-4 col-form-label">Nama pegawai</label>
                <div class="col-sm-12 col-md-8">
                    <select name="pengguna" id="pegawaiManajemenPengguna" class="form-select single-select">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- akhir nama pegawai -->
            <!-- mulai nama pengguna -->
            <div class="row mb-3 align-items-center">
                <label for="usernameManajemenPengguna" class="col-sm-12 col-md-4 col-form-label">Nama pengguna</label>
                <div class="col-sm-12 col-md-8">
                    <input type="text" name="username" id="usernameManajemenPengguna" class="form-control form-control-sm" placeholder="Nama pengguna" value="" readonly>
                </div>
            </div>
            <!-- akhir nama pengguna -->
            <!-- mulai akses -->
            <div class="row mb-3 align-items-center">
                <label for="aksesManajemenPengguna" class="col-sm-12 col-md-4 col-form-label">Akses</label>
                <div class="col-sm-12 col-md-8">
                    <div class="row mx-0">
                        <?php foreach ($akses as $a) : ?>
                            <div class="form-check form-check-inline col-sm">
                                <input type="checkbox" name="akses[]" class="form-check-input akses-manajemen-pengguna" id="aksesManajemenPengguna<?= $a['id'] ?>" value="<?= $a['id'] ?>">
                                <label for="aksesManajemenPengguna<?= $a['id'] ?>"><?= $a['nama'] ?></label>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- akhir akses -->
            <!-- mulai aksi -->
            <div class="row">
                <div class="col-1">
                    <button type="button" class="btn btn-primary" id="simpanManajemenPengguna">Simpan</button>
                </div>
            </div>
            <!-- akhir aksi -->
        </form>
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function() {
        // mulai pengguna
        $('#pegawaiManajemenPengguna').select2({
            placeholder: 'Pilih pengguna',
            ajax: {
                url: "<?= base_url('Akun/Pengguna') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        }).change(function() {
            // mulai ambil data
            let id = $(this).val();
            $('.akses-manajemen-pengguna').prop('checked', false);
            $.ajax({
                url: "<?= base_url('admin/manajemen_pengguna/Pengguna/ambil_isi_akses') ?>",
                type: 'post',
                data: {
                    id_pengguna: id
                },
                success: function(data) {
                    let json = JSON.parse(data);

                    if (json !== null) {
                        $('#usernameManajemenPengguna').val(json['username']); // isi username

                        // mulai akses
                        let akses_sippo = json['akses_sippo'];
                        if (akses_sippo !== null) {
                            let akses_tersimpan = JSON.parse(json['akses_sippo']);
                            jQuery.each(JSON.parse(json['akses_sippo']), function(index, value) {
                                $('#aksesManajemenPengguna' + value).prop('checked', true);
                            });
                        } else {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: 'Akses pengguna ini belum diatur',
                                sound: false,
                                title: 'Peringatan'
                            });
                        }
                        // akhir akses
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Pengguna ini tidak ditemukan',
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
            // akhir ambil data
        });
        // akhir pengguna

        <?php if (isset($id_pengguna)) : ?>
            // mulai ambil data
            $('#pegawaiManajemenPengguna').append("<option value='<?= $id_pengguna ?>' selected><?= $detail['nama'] ?></option>").trigger('change');
            $('#usernameManajemenPengguna').val("<?= $detail['username'] ?? null ?>");

            // mulai akses
            $('.akses-manajemen-pengguna').prop('checked', false);
            let akses_tersimpan = JSON.parse('<?= $detail["akses_sippo"] ?>');
            jQuery.each(akses_tersimpan, function(index, value) {
                $('#aksesManajemenPengguna' + value).prop('checked', true);
            });
            // akhir akses
            // akhir ambil data
        <?php endif ?>

        // mulai simpan
        $('#simpanManajemenPengguna').click(function() {
            $.ajax({
                url: "<?= base_url('admin/manajemen_pengguna/Pengguna/simpan') ?>",
                dataType: 'json',
                type: 'post',
                data: $('#formManajemenPengguna').serialize(),
                success: function(data) {
                    if (data.status === 'success') {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Data berhasil disimpan',
                            sound: false,
                            title: 'Berhasil',
                        });
                        window.location.href = "<?= base_url('admin/manajemen_pengguna/Pengguna/index') ?>";
                    } else {
                        $.each(data.errors, function(index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                },
            });
        });
        // akhir simpan
    });
</script>