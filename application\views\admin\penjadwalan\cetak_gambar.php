<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cetak Gambar - Daftar Operasi Elektif</title>
    
    <!-- Bootstrap CSS -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"> -->
    
    <!-- html2canvas -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script> -->
    <script src="<?= base_url('assets/saveimg/html2canvas.min.js') ?>"></script>

    
    <!-- JSZip untuk membuat ZIP file -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script> -->
    <script src="<?= base_url('assets/saveimg/jszip.min.js') ?>"></script>
    
    <!-- FileSaver untuk download -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script> -->
    <script src="<?= base_url('assets/saveimg/FileSaver.min.js') ?>"></script>
    
    <style>
        /* Print styles mirip dengan iReport */
        .print-page {
            width: 842px; /* A4 Landscape width */
            min-height: 595px; /* A4 Landscape height */
            margin: 20px auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            font-family: Arial, sans-serif;
            page-break-after: always;
        }
        
        .print-page:last-child {
            page-break-after: auto;
        }
        
        .header-title {
            text-align: center;
            font-size: 7pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .header-subtitle {
            text-align: center;
            font-size: 7pt;
            margin-bottom: 15px;
        }
        
        .tujuan-rs {
            font-size: 7pt;
            margin-bottom: 20px;
        }
        
        .table-operasi {
            width: 100%;
            border-collapse: collapse;
            font-size: 7pt;
            table-layout: auto; /* PERBAIKAN: auto untuk responsif subreport */
            word-break: break-word; /* PERBAIKAN: Break long words */
        }

        .table-operasi th,
        .table-operasi td {
            border: 0.5px solid #000;
            padding: 3px;
            vertical-align: top;
            word-wrap: break-word; /* Memungkinkan text wrapping */
            overflow-wrap: break-word;
            /* PERBAIKAN: Pastikan cell dapat menyesuaikan tinggi konten */
            height: auto;
            max-height: none;
            overflow: visible;
        }

        /* Memastikan rowspan cells sejajar dengan baik */
        .table-operasi td[rowspan] {
            vertical-align: middle;
            text-align: center;
        }

        .table-operasi td[rowspan].text-left {
            text-align: left;
        }

        /* PERBAIKAN: Kolom TINDAKAN dan OPERATOR - responsif untuk subreport */
        .table-operasi th:nth-child(8), /* TINDAKAN */
        .table-operasi td:nth-child(8) {
            min-width: 120px; /* Lebih lebar untuk tindakan subreport */
            width: auto; /* Auto width sesuai konten */
        }

        .table-operasi th:nth-child(9), /* OPERATOR */
        .table-operasi td:nth-child(9) {
            min-width: 100px; /* Lebih lebar untuk operator subreport */
            width: auto; /* Auto width sesuai konten */
        }

        /* PERBAIKAN: Styling khusus untuk cell subreport agar rapi */
        .subreport-cell {
            font-size: 7pt !important;
            line-height: 1.2 !important;
            padding: 3px 4px !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            white-space: normal !important;
            vertical-align: top !important;
            border: 0.5px solid #000 !important;
            /* Pastikan konten tidak keluar dari kolom */
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .table-operasi th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }
        
        .group-header-date {
            background-color: #FFCCCC !important;
            font-weight: bold;
            font-size: 7pt;
            padding: 3px 5px;
        }
        
        .group-header-room {
            background-color: #CCCCCC !important;
            font-weight: bold;
            font-size: 8pt;
            text-align: center;
            padding: 3px;
        }
        
        .text-center { text-align: center; }
        .text-left { text-align: left; }

        /* Styling untuk subreport data */
        .subreport-item {
            margin-bottom: 2px;
            line-height: 1.1;
        }

        .subreport-separator {
            border-bottom: 1px dotted #ccc;
            margin: 1px 0;
        }
        
        .footer-notes {
            font-size: 7pt;
            margin-top: 20px;
            line-height: 1.2;
        }
        
        .control-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        @media print {
            .control-buttons {
                display: none;
            }
            .print-page {
                box-shadow: none;
                margin: 0;
            }
        }
        
        .loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2000;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- Control Buttons -->
    <div class="control-buttons">
        <button id="btnCetakImg" class="btn btn-primary mb-2">
            <i class="fas fa-download"></i> Cetak IMG
        </button>
        <button id="btnKembali" class="btn btn-secondary" onclick="window.history.back()">
            <i class="fas fa-arrow-left"></i> Kembali
        </button>
        <div class="mt-2">
            <small class="text-muted">Total Data: <?= count($data_operasi) ?></small>
        </div>
    </div>
    
    <!-- Loading Indicator -->
    <div id="loading" class="loading">
        <div class="text-center">
            <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2">Sedang membuat gambar...</div>
        </div>
    </div>
    
    <!-- Print Pages Container -->
    <div id="printContainer">
        <?php
        // Function to convert date to Indonesian format
        function formatTanggalIndonesia($date) {
            $hari = array(
                'Sunday' => 'Minggu',
                'Monday' => 'Senin',
                'Tuesday' => 'Selasa',
                'Wednesday' => 'Rabu',
                'Thursday' => 'Kamis',
                'Friday' => 'Jumat',
                'Saturday' => 'Sabtu'
            );

            $bulan = array(
                'January' => 'Januari',
                'February' => 'Februari',
                'March' => 'Maret',
                'April' => 'April',
                'May' => 'Mei',
                'June' => 'Juni',
                'July' => 'Juli',
                'August' => 'Agustus',
                'September' => 'September',
                'October' => 'Oktober',
                'November' => 'November',
                'December' => 'Desember'
            );

            $day_name = $hari[date('l', strtotime($date))];
            $day = date('d', strtotime($date));
            $month_name = $bulan[date('F', strtotime($date))];
            $year = date('Y', strtotime($date));

            return $day_name . ', ' . $day . ' ' . $month_name . ' ' . $year;
        }

        // Group data by date and room
        $grouped_data = [];
        foreach ($data_operasi as $item) {
            $date = $item->tgl_operasi;
            $room = $item->kamar_operasi_full;
            $grouped_data[$date][$room][] = $item;
        }

        $items_per_page = 15;
        $current_page_items = 0;
        $page_number = 1;
        $total_items = 0;

        // Check if there's data
        if (empty($data_operasi)) {
            // Show empty data message
            echo '<div class="print-page" id="page-' . $page_number . '">';
            include 'cetak_gambar_header.php';
            echo '<table class="table-operasi">';
            include 'cetak_gambar_table_header.php';
            echo '<tr><td colspan="12" class="text-center" style="padding: 20px;">Tidak ada data operasi untuk periode yang dipilih</td></tr>';
            echo '</table>';
            include 'cetak_gambar_footer.php';
            echo '</div>';
        } else {
            // Start first page
            echo '<div class="print-page" id="page-' . $page_number . '">';
            include 'cetak_gambar_header.php';
            echo '<table class="table-operasi">';
            include 'cetak_gambar_table_header.php';
        
        foreach ($grouped_data as $date => $rooms) {
            // Date group header
            echo '<tr><td colspan="12" class="group-header-date">';
            echo formatTanggalIndonesia($date);
            echo '</td></tr>';

            foreach ($rooms as $room => $items) {
                // Room group header
                echo '<tr><td colspan="12" class="group-header-room">' . $room . '</td></tr>';
                
                $row_number = 1;
                foreach ($items as $item) {
                    // Check if we need a new page
                    if ($current_page_items >= $items_per_page) {
                        // Close current page
                        echo '</table>';
                        include 'cetak_gambar_footer.php';
                        echo '</div>';
                        
                        // Start new page
                        $page_number++;
                        $current_page_items = 0;
                        echo '<div class="print-page" id="page-' . $page_number . '">';
                        include 'cetak_gambar_header.php';
                        echo '<table class="table-operasi">';
                        include 'cetak_gambar_table_header.php';
                        
                        // Repeat date and room headers on new page
                        echo '<tr><td colspan="12" class="group-header-date">';
                        echo formatTanggalIndonesia($date);
                        echo '</td></tr>';
                        echo '<tr><td colspan="12" class="group-header-room">' . $room . '</td></tr>';
                        $row_number = 1;
                    }
                    
                    // Data row dengan subreport integration - setiap subreport item dalam baris terpisah
                    if (!empty($item->subreport_data) && is_array($item->subreport_data) && count($item->subreport_data) > 0) {
                        // Tampilkan setiap subreport item dalam baris terpisah
                        foreach ($item->subreport_data as $sub_index => $sub_item) {
                            echo '<tr>';

                            // Kolom NO - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . $row_number . '</td>';
                            }

                            // Kolom JAM - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->jam_operasi ?: '') . '</td>';
                            }

                            // Kolom NAMA PASIEN - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-left" style="padding-left: 5px;" rowspan="' . count($item->subreport_data) . '">' . ($item->nama_pasien ?: '') . '</td>';
                            }

                            // Kolom MR - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->norm ?: '') . '</td>';
                            }

                            // Kolom UMUR - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->umur ?: '') . '</td>';
                            }

                            // Kolom KELAS - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->kamar_rawat_real ?: 'MKR') . '</td>';
                            }

                            // Kolom DIAGNOSA - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-left" style="padding-left: 3px;" rowspan="' . count($item->subreport_data) . '">' . ($item->diagnosis ?: '') . '</td>';
                            }

                            // Kolom TINDAKAN - setiap subreport item dalam baris terpisah - STYLING RAPI
                            echo '<td class="text-left subreport-cell" style="padding: 3px 4px; word-wrap: break-word; overflow-wrap: break-word; max-width: 120px; white-space: normal; vertical-align: top; line-height: 1.2; font-size: 7pt;">' . ($sub_item->tindakan ?: '') . '</td>';

                            // Kolom OPERATOR - setiap subreport item dalam baris terpisah - STYLING RAPI
                            echo '<td class="text-left subreport-cell" style="padding: 3px 4px; word-wrap: break-word; overflow-wrap: break-word; max-width: 100px; white-space: normal; vertical-align: top; line-height: 1.2; font-size: 7pt;">' . ($sub_item->dokter_operator ?: '') . '</td>';

                            // Kolom DOKTER ANESTESI - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-left" style="padding-left: 3px;" rowspan="' . count($item->subreport_data) . '">' . ($item->dokter_anestesi ?: '') . '</td>';
                            }

                            // Kolom JENIS ANESTESI - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->nama_anestesi ?: '') . '</td>';
                            }

                            // Kolom PTGS - hanya tampilkan di baris pertama
                            if ($sub_index === 0) {
                                echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '"></td>';
                            }

                            echo '</tr>';
                        }
                    } else {
                        // Fallback: tampilkan data utama jika subreport kosong
                        echo '<tr>';
                        echo '<td class="text-center">' . $row_number . '</td>';
                        echo '<td class="text-center">' . ($item->jam_operasi ?: '') . '</td>';
                        echo '<td class="text-left" style="padding-left: 5px;">' . ($item->nama_pasien ?: '') . '</td>';
                        echo '<td class="text-center">' . ($item->norm ?: '') . '</td>';
                        echo '<td class="text-center">' . ($item->umur ?: '') . '</td>';
                        echo '<td class="text-center">' . ($item->kamar_rawat_real ?: 'MKR') . '</td>';
                        echo '<td class="text-left" style="padding-left: 3px;">' . ($item->diagnosis ?: '') . '</td>';
                        echo '<td class="text-left" style="padding-left: 3px;">' . ($item->tindakan ?: '') . '</td>';
                        echo '<td class="text-left" style="padding-left: 3px;">' . ($item->dokter_operator ?: '') . '</td>';
                        echo '<td class="text-left" style="padding-left: 3px;">' . ($item->dokter_anestesi ?: '') . '</td>';
                        echo '<td class="text-center">' . ($item->nama_anestesi ?: '') . '</td>';
                        echo '<td class="text-center"></td>'; // PTGS column (empty)
                        echo '</tr>';
                    }
                    
                    $row_number++;
                    $current_page_items++;
                    $total_items++;
                }
            }
        }
        
            // Close last page
            echo '</table>';
            include 'cetak_gambar_footer.php';
            echo '</div>';
        } // End of else block
        ?>
    </div>

    <script>
        document.getElementById('btnCetakImg').addEventListener('click', function() {
            const loading = document.getElementById('loading');
            const pages = document.querySelectorAll('.print-page');
            const zip = new JSZip();
            let processedPages = 0;
            
            loading.style.display = 'block';
            
            // Process each page
            pages.forEach((page, index) => {
                html2canvas(page, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    width: 842,
                    height: 595
                }).then(canvas => {
                    // Convert canvas to blob
                    canvas.toBlob(function(blob) {
                        const fileName = `laporan_operasi_halaman_${index + 1}.png`;
                        zip.file(fileName, blob);
                        
                        processedPages++;
                        
                        // If all pages processed, create and download ZIP
                        if (processedPages === pages.length) {
                            zip.generateAsync({type: 'blob'}).then(function(content) {
                                const currentDate = new Date().toISOString().slice(0, 10);
                                saveAs(content, `laporan_operasi_${currentDate}.zip`);
                                loading.style.display = 'none';
                            });
                        }
                    }, 'image/png');
                }).catch(error => {
                    console.error('Error generating image:', error);
                    loading.style.display = 'none';
                    alert('Terjadi kesalahan saat membuat gambar. Silakan coba lagi.');
                });
            });
        });
    </script>
</body>
</html>
