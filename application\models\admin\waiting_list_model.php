<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Waiting_list_model extends CI_Model
{
    protected $_table = 'medis.tb_waiting_list_operasi wlo';
    protected $_primary_key = 'wlo.id';
    protected $_order_by = 'wlo.id';
    protected $_order_by_type = 'asc';

    protected $_urutan_kolom = [
        null,
        'nama',
        'norm',
        'jk',
        'tgl_lahir',
        'nokun',
        'diagnosis',
        'tindakan',
        'sifat_operasi',
        'tgl_daftar',
        'tgl_rencana',
        'created_at',
        'keterangan',
        'catatan_khusus',
        null,
        null,
    ];

    protected $_pencarian_kolom = [
        'master.getNamaLengkap(wlo.norm)',
        'wlo.norm',
        "IF(ps.JENIS_KELAMIN = 1, 'Laki-laki', 'Perempuan')",
        'ps.TANGGAL_LAHIR',
        'wlo.nokun',
        'wlo.diagnosis',
        'wlo.tindakan',
        'so.variabel',
        'wlo.tanggal',
        'wlo.created_at',
        'wlor.keterangan',
        'tpo.catatan_khusus'
    ];

    function __construct()
    {
        parent::__construct();
    }

    function rules()
    {
        return [
            [
                'field' => 'norm',
                'label' => 'Nomor RM',
                'rules' => 'trim|required|max_length[6]|numeric',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 6 karakter',
                    'numeric' => '%s wajib berupa angka',
                ],
            ],
            [
                'field' => 'diagnosis',
                'label' => 'Diagnosis',
                'rules' => 'trim|required|max_length[65535]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 65535 karakter',
                ],
            ],
            [
                'field' => 'tindakan',
                'label' => 'Tindakan',
                'rules' => 'trim|required|max_length[65535]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 65535 karakter',
                ],
            ],
            [
                'field' => 'sifat_operasi',
                'label' => 'Sifat operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
            [
                'field' => 'tanggal',
                'label' => 'Tanggal',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
        ];
    }

    function simpan($data)
    {
        $this->db->insert('medis.tb_waiting_list_operasi', $data);
        return $this->db->insert_id();
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_waiting_list_operasi.id', $id);
        $this->db->update('medis.tb_waiting_list_operasi', $data);
    }

    function tabel($id_dokter, $status, $id_ruang = null, $tujuan = null)
    {
        $this->db->select(
            "wlo.id, master.getNamaLengkap(wlo.norm) nama, wlo.norm, wlo.nokun, lpo.id id_laporan_operasi,
            IF(ps.JENIS_KELAMIN = 1, 'Laki-laki', 'Perempuan') jk, ps.TANGGAL_LAHIR tgl_lahir, wlo.diagnosis,
            wlo.tindakan, so.variabel sifat_operasi, wlo.tanggal tgl_daftar,
            CONCAT('[', GROUP_CONCAT( CONCAT('`', wlor.tanggal_rencana, '`')), ']') tgl_rencana, wlo.created_at,
            wlor.keterangan, tpo.catatan_khusus, wlo.status, ps.status status_pasien, mr.DESKRIPSI ruangan,
            CASE 
                WHEN tpo.ruang_operasi IS NOT NULL THEN ref.DESKRIPSI
                ELSE ''
            END AS tujuan_rs"
        );
        $this->db->from($this->_table);
        $this->db->join('db_master.variabel so', 'so.id_variabel = wlo.sifat_operasi', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join('medis.tb_waiting_list_operasi_rencana wlor', 'wlor.id_wlo = wlo.id', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
        $this->db->join('medis.tb_laporan_operasi lpo', 'lpo.id_waiting_list = wlo.id', 'left');
        $this->db->join('master.referensi ref', 'ref.ID = tpo.ruang_operasi AND ref.jenis = 81', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
        $this->db->join('master.ruangan mr', 'mr.ID = rmp.ID_RUANGAN', 'left');
        $this->db->where('wlo.id_dokter', $id_dokter);
        $this->db->group_by('wlo.id');

        // mulai periksa status
        if ($status == -1) {
            $this->db->order_by('FIELD(wlo.status, 1, 2, 3, 0)');
        } else {
            $this->db->where('wlo.status', $status);
        }
        // akhir periksa status
        if (!empty($tujuan)) {
            $this->db->where('tpo.ruang_operasi', $tujuan); 
        }
        // mulai filter ruangan
        if (empty($id_ruang)) {
            $this->db->where_in('rmp.ID_RUANGAN', ['105090101', '105090104']);
        } else {
            $this->db->where('rmp.ID_RUANGAN', $id_ruang);
        }
        // akhir filter ruangan

        // mulai filter tujuan

        //akhir filter tujuan

        $this->db->order_by('FIELD(wlo.sifat_operasi, 6125, 2129, 6080, 2131)');

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil($id_dokter, $status, $id_ruang = null, $tujuan = null)
    {
        $this->tabel($id_dokter, $status, $id_ruang, $tujuan);
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring($id_dokter, $status, $id_ruang = null, $tujuan = null)
    {
        $this->tabel($id_dokter, $status, $id_ruang, $tujuan);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua($id_dokter, $status, $id_ruang = null, $tujuan = null)

    {
        $this->tabel($id_dokter, $status, $id_ruang, $tujuan);
        return $this->db->count_all_results();
    }

    function detail($id)
    {
        $this->db->select(
            "wlo.id, wlo.norm, master.getNamaLengkap(mp.NORM) nama, mp.TANGGAL_LAHIR tgl_lahir,
            TIMESTAMPDIFF(YEAR, mp.TANGGAL_LAHIR, CURDATE()) AS umur, wlo.nokun, wlo.id_pendaftaran_operasi,
            wlo.id_dokter, CONCAT('[', GROUP_CONCAT(tapo.asisten_bedah), ']') dokter_bedah_lain, wlo.diagnosis,
            tpo.diagnosa_medis, wlo.tindakan, tpo.rencana_tindakan_operasi, wlo.tujuan_operasi, wlo.sifat_operasi,
            wlo.tanggal, tpo.tanggal_operasi, wlor.tanggal_rencana tgl_rencana, LEFT(tpo.jam_operasi, 5) jam_operasi,
            tpo.perkiraan_lama_operasi, tpo.rencana_jenis_pembiusan, tpo.rencana_jenis_pembiusan_lain,
            tpo.ruang_tujuan, tpo.potong_beku, tpo.join_operasi, pp.JENIS id_penjamin, penjamin.DESKRIPSI penjamin,
            pp.KELAS kelas,tpo.ruang_operasi"
        );
        $this->db->from($this->_table);
        $this->db->join('master.pasien mp', 'mp.NORM = wlo.norm', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
        $this->db->join('medis.tb_waiting_list_operasi_rencana wlor', 'wlor.id_wlo = wlo.id', 'left');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = wlo.nokun', 'left');
        $this->db->join('pendaftaran.penjamin pp', 'pp.NOPEN = pk.NOPEN', 'left');
        $this->db->join('master.referensi penjamin', 'penjamin.ID = pp.JENIS AND penjamin.JENIS = 10', 'left');
        $this->db->join(
            'medis.tb_asisten_pendaftaran_operasi tapo',
            'tapo.id_pendaftaran = tpo.id AND tapo.status = 1',
            'left'
        );
        // $this->db->join('master.ruangan mr', 'mr.ID = tpo.ruang_operasi', 'left');
        $this->db->where(
            'wlor.id = (
                SELECT MAX(wlor.id)
                FROM medis.tb_waiting_list_operasi_rencana wlor
                WHERE wlor.id_wlo = wlo.id
            )'
        );
        $this->db->where('wlo.id', $id);
        $query = $this->db->get();
        return $query->row_array();
    }

    public function ambil_tindakan($id)
    {
        $this->db->select(
            'tapo.asisten_bedah id_dokter_bedah_lain, master.getNamaLengkapPegawai(d.NIP) dokter_bedah_lain,
            tapo.rencana_tindakan'
        );
        $this->db->from($this->_table);
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
        $this->db->join(
            'medis.tb_asisten_pendaftaran_operasi tapo',
            'tapo.id_pendaftaran = tpo.id AND tpo.dokter_bedah != tapo.asisten_bedah AND tapo.status = 1',
            'left'
        );
        $this->db->join('master.dokter d', 'd.ID = tapo.asisten_bedah AND d.STATUS = 1', 'left');
        $this->db->where('tapo.status', 1);
        $this->db->where('wlo.id', $id);
        $this->db->order_by('tapo.id', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function ambil_norm()
    {
        $this->db->select('p.NORM, p.NAMA NAMA_PASIEN');
        $this->db->distinct();
        $this->db->from($this->_table);
        $this->db->join('master.pasien p', 'p.NORM = wlo.norm AND p.STATUS = 1', 'left');

        // mulai pencarian
        if ($this->input->get('q')) {
            $this->db->like('wlo.norm', $this->input->get('q'));
            $this->db->like('master.getNamaLengkapPegawai(d.NIP)', $this->input->get('q'));
        }
        // akhir pencarian

        $this->db->where_in('wlo.status', [1, 2]);
        $this->db->order_by('p.NORM', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    function pilih($norm)
    {
        $q = $this->input->get('q');
        // echo '<pre>';print_r($q);exit();
        $this->db->select("id, nokun, DATE_FORMAT(created_at, '%d/%m/%Y, %H.%i.%s') tgl_dibuat");
        $this->db->from($this->_table);
        $this->db->where('norm', $norm);
        $this->db->order_by('id', 'DESC');

        // mulai pencarian
        if ($q) {
            $this->db->like('nokun', $q);
            $this->db->or_like('created_at', $q);
        }
        // akhir pencarian

        $query = $this->db->get();
        return $query->result_array();
    }
}

// End of File Waiting_list_model.php
// Location: ./application/models/admin/Waiting_list_model.php