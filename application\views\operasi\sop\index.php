<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3"><em>Standard Operating Procedure</em></div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item"><em>Dashboard</em></li>
                <li class="breadcrumb-item active" aria-current="page"><em>Standard Operating Procedure</em></li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Tabel SOP</h5>
        <hr>
        <!-- mulai dokter operasi -->
        <div class="row mb-3 align-items-center">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover w-100" id="tableSOP">
                    <thead>
                        <tr>
                            <th class="text-center" scope="col">No. Dokumen</th>
                            <th class="text-center" scope="col">No. Revisi</th>
                            <th class="text-center" scope="col">Nama Dokumen</th>
                            <th class="text-center" scope="col">Tanggal Terbit</th>
                            <th class="text-center" scope="col">Aksi</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th class="text-center" scope="col">No. Dokumen</th>
                            <th class="text-center" scope="col">No. Revisi</th>
                            <th class="text-center" scope="col">Nama Dokumen</th>
                            <th class="text-center" scope="col">Tanggal Terbit</th>
                            <th class="text-center" scope="col">Aksi</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function () {
        // mulai tabel
        $('#tableSOP').DataTable({
            autoWidth: true,
            order: [0, 'asc'],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('operasi/Sop/isi_tabel') ?>",
                type: 'POST',
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [4],
                orderable: false,
            }],
            // akhir pendefinisian kolom
        });
        // akhir tabel

        $('#tableSOP').on('click', '.lihatSOP', function () {
            let berkas = $(this).attr('data');
            let tipe = $(this).attr('tipe');
            let url = 'http://************/surat/edoc/main/file_dokumen/';
            let file = url + berkas;
            if (tipe == 'pdf' || tipe == 'application/pdf') {
                let x = window.open(file, '_blank');
                x.focus();
            } else {
                alert('file bukan pdf harap hubungi SIMRS');
            }

        });
    });
</script>