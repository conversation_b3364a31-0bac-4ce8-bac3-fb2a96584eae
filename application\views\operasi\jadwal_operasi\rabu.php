<table class="table table-striped table-bordered table-hover w-100" id="tabrabu">
    <thead>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col"><PERSON><PERSON>si</th>
            <th class="text-center" scope="col">Jam</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col">Tgl. Lahir</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col">DPJP <PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON> (Menit)</th>
        </tr>
    </thead>
    <tbody></tbody>
    <tfoot>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Ruang <PERSON>si</th>
            <th class="text-center" scope="col">Jam</th>
            <th class="text-center" scope="col">Nama Pasien</th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col">Tgl. Lahir</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col">DPJP Anestesi</th>
            <th class="text-center" scope="col">Jenis Anestesi</th>
            <th class="text-center" scope="col">Durasi (Menit)</th>
        </tr>
    </tfoot>
</table>

<script>
    $(document).ready(function() {
        $('#tabrabu').DataTable({
            autoWidth: true,
            order: [1, 'asc'],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('operasi/Jadwal_operasi/isi_tabel') ?>",
                type: 'POST',
                data: function(d) {
                    d.hari = 'Wednesday';
                    d.PTUJUAN_RS = window.tujuanRsFilter || '';
                    return d;
                }
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [0],
                orderable: false,
            }],
            // akhir pendefinisian kolom
        });
    });
</script>