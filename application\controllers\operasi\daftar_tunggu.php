<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Daftar_tunggu extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'operasi/Dokter_model',
                'operasi/Waiting_list_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Daftar Tunggu Operasi',
            'isi' => 'operasi/daftar_tunggu/index',
            'session' => $this->session->get_userdata(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function get_dokter()
    {
        $search = $this->input->get('q');
        $dokter = $this->Dokter_model->dokter_daftar_tunggu();

        $data = [];
        foreach ($dokter as $d) {
            if (empty($search) || stripos($d['dokter'], $search) !== false) {
                $data[] = [
                    'id' => $d['id_dokter'],
                    'text' => $d['dokter'] . ' - ' . $d['jml_wl'] . ' pasien'
                ];
            }
        }

        echo json_encode($data);
    }

    function get_tujuan()
    {
        $search = $this->input->get('q');
        $this->load->model('Referensi_model');
        $tujuan = $this->Referensi_model->get_tujuan_rs(); // Menggunakan method yang sudah ada

        $data = [];
        foreach ($tujuan as $t) {
            if (empty($search) || stripos($t['DESKRIPSI'], $search) !== false) {
                $data[] = [
                    'id' => $t['ID'],
                    'text' => $t['DESKRIPSI']
                ];
            }
        }

        echo json_encode($data);
    }

    function tabel()
    {
        $tujuan = $this->input->post('tujuan');
        $data = [
            'PDOKTER' => $this->input->post('id_dokter'),
            'PTUJUAN_RS' => !empty($tujuan) ? $tujuan : ''
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('operasi/daftar_tunggu/tabel', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        // Validasi parameter yang diperlukan
        if (!isset($post['PDOKTER']) || empty($post['PDOKTER'])) {
            echo json_encode([
                'draw' => isset($post['draw']) ? intval($post['draw']) : 0,
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => 'Parameter PDOKTER tidak ditemukan'
            ]);
            return;
        }

        $dokter_lain = null;
        $PDOKTER = $post['PDOKTER'];
        $PTUJUAN_RS = isset($post['PTUJUAN_RS']) && !empty($post['PTUJUAN_RS']) ? $post['PTUJUAN_RS'] : '';
        $draw = isset($post['draw']) ? intval($post['draw']) : 0;
        $start = isset($post['start']) ? intval($post['start']) : 0;

        $tabel = $this->Waiting_list_model->ambil($PDOKTER, $PTUJUAN_RS);
        $no = $start;
        $warna = null;

        // Pastikan $tabel adalah array yang valid untuk foreach
        if (!is_array($tabel)) {
            $tabel = [];
        }

        foreach ($tabel as $t) {
            // mulai dokter bedah lain
            if (isset($t->dokter_bedah_lain) && !empty($t->dokter_bedah_lain) && $t->dokter_bedah_lain !== '[]') {
                $isi_dokter_lain = "<ul class='ps-3'>";
                $dokter_lain = str_replace("`", '"', $t->dokter_bedah_lain);
                $decoded_dokter = json_decode($dokter_lain);

                // Pastikan json_decode berhasil dan mengembalikan array
                if (is_array($decoded_dokter) && !empty($decoded_dokter)) {
                    foreach ($decoded_dokter as $a) {
                        // Pastikan $a adalah object dengan property yang diperlukan
                        if (is_object($a) && isset($a->id) && isset($a->nama)) {
                            $warna = $PDOKTER == $a->id ? 'text-info' : null; // periksa id
                            $isi_dokter_lain .= "<li class='$warna' data-id='$a->id'>$a->nama</li>";
                        }
                    }
                }
                $isi_dokter_lain .= '</ul>';
            } else {
                $isi_dokter_lain = '-';
            }
            // akhir dokter bedah lain

            // mulai status
            $status_value = isset($t->status) ? (int)$t->status : 1;
            switch ($status_value) {
                case 0:
                    $status = "<span class='badge bg-primary'>BATAL</span>";
                    break;
                case 1:
                    $status = "<span class='badge bg-danger'>MENUNGGU JADWAL</span>";
                    break;
                case 2:
                    $status = "<span class='badge bg-warning'>PRE OP</span>";
                    break;
                case 3:
                    $status = "<span class='badge bg-success'>SELESAI</span>";
                    break;
                default:
                    $status = "<span class='badge bg-secondary'>UNKNOWN</span>";
                    break;
            }
            // akhir status

            $data[] = [
                ++$no . '.',
                isset($t->tujuan_rs) ? $t->tujuan_rs : '-',
                $t->nama_pasien ?? '',
                $t->norm ?? '',
                $t->jk ?? '',
                !empty($t->tgl_lahir) ? date('d/m/Y', strtotime($t->tgl_lahir)) . " <p class='fw-medium'>(" . ($t->umur ?? '') . ")</p>" : '-',
                $t->diagnosis ?? '',
                $t->ruangan ?? '-',
                $t->rencana_tindakan ?? '',
                !empty($t->tgl_daftar) ? date('d/m/Y', strtotime($t->tgl_daftar)) : '-',
                $t->sifat_operasi ?? '',
                $isi_dokter_lain,
                isset($t->catatan_khusus) && $t->catatan_khusus != '' ? $t->catatan_khusus : '-',
                $status
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Waiting_list_model->hitung_semua($PDOKTER, $PTUJUAN_RS),
            'recordsFiltered' => $this->Waiting_list_model->hitung_tersaring($PDOKTER, $PTUJUAN_RS),
            'data' => $data
        ];

        echo json_encode($output);
    }
}

// End of File Daftar_tunggu.php
// Location: ./application/controllers/operasi/Daftar_tunggu.php