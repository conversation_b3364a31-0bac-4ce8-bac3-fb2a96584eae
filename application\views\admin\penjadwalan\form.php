<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Penjadwalan Operasi</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item">
                    <a href="<?= base_url('admin/Penjadwalan/index') ?>">Penjadwalan Operasi</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page"><?= isset($detail['id_penjadwalan']) ? 'Ubah' : 'Buat' ?></li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai form -->
<form id="formBuatPenjadwalan">
    <!-- mulai kartu form -->
    <div class="card mb-3">
        <div class="card-body">
            <h5 class="card-title"><?= isset($detail['id_penjadwalan']) ? 'Ubah' : 'Buat' ?> Penjadwalan Operasi</h5>
            <hr>
            <input type="hidden" name="mulai" value="<?= $mulai ?>" />
            <input type="hidden" name="akhir" value="<?= $akhir ?>" />
            <input type="hidden" name="id_ruangan" value="<?= $id_ruangan?>">
            <input type="hidden" name="hari" value="<?= $hari?>">

            <input type="hidden" name="id_penjadwalan" value="<?= $detail['id_penjadwalan'] ?? null ?>">
            <input type="hidden" name="id_perjanjian" value="<?= $detail['id_perjanjian'] ?? null ?>">
            <input type="hidden" name="id_reservasi" value="<?= $detail['id_reservasi'] ?? null ?>">
            <input type="hidden" name="id_pendaftaran_operasi" id="idPendaftaranPenjadwalan" value="<?= $detail['id_tpo'] ?? null ?>">
            <input type="hidden" name="id_jk" id="jkPenjadwalan" value="<?= $detail['id_jk'] ?? null ?>">
            <div class="row">
                <!-- mulai kolom 1 -->
                <div class="col">
                    <?php if (isset($detail['norm'])): ?>
                        <!-- mulai mr -->
                        <div class="row mb-3 align-items-center">
                            <label for="mrPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Nomor RM</label>
                            <div class="col-sm-12 col-md-8">
                                <input type="text" name="norm" id="mrPenjadwalan" class="form-control" placeholder="Masukkan nomor RM" maxlength="6" value="<?= $detail['norm'] ?? null ?>" readonly>
                            </div>
                        </div>
                        <!-- akhir mr -->
                    <?php else: ?>
                        <!-- mulai pilih mr -->
                        <div class="row mb-3 align-items-center">
                            <label for="mrPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Nomor RM</label>
                            <div class="col-sm-12 col-md-8">
                                <select name="norm" id="mrPenjadwalan" class="form-select single-select">
                                    <option value=""></option>
                                    <?php foreach ($ambil_norm as $an): ?>
                                        <option id="mrPenjadwalan<?= $an['NORM'] ?>" value="<?= $an['NORM'] ?>">
                                            <?= $an['NORM'] . ' - ' . $an['NAMA_PASIEN'] ?>
                                        </option>
                                    <?php endforeach ?>
                                </select>
                            </div>
                        </div>
                        <!-- akhir pilih mr -->
                    <?php endif ?>
                    <!-- mulai nama -->
                    <div class="row mb-3 align-items-center">
                        <label for="namaPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Nama</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="nama" id="namaPenjadwalan" class="form-control form-control-sm" placeholder="Nama pasien" value="<?= $detail['nama'] ?? null ?>" readonly>
                        </div>
                    </div>
                    <!-- akhir nama -->
                    <!-- mulai telepon -->
                    <div class="row mb-3 align-items-center">
                        <label for="teleponPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Nomor telepon</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="tel" name="no_telp" id="teleponPenjadwalan" class="form-control form-control-sm" placeholder="Telepon pasien" value="<?= $detail['no_telp'] ?? null ?>" readonly>
                        </div>
                    </div>
                    <!-- akhir telepon -->
                    <?php if (isset($detail['norm'])): ?>
                        <!-- mulai pendaftaran pra operasi -->
                        <div class="row mb-3 align-items-center">
                            <label for="tpoPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Pendaftaran pra operasi</label>
                            <div class="col-sm-12 col-md-8">
                                <select name="tpo" id="tpoPenjadwalan" class="form-select single-select"></select>
                            </div>
                        </div>
                        <!-- akhir pendaftaran pra operasi -->
                        <input type="hidden" name="id_waiting_list" value="<?= $detail['id_waiting_list_operasi'] ?>">
                    <?php else: ?>
                        <!-- mulai daftar tunggu -->
                        <div class="row mb-3 align-items-center">
                            <label for="daftarTungguPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Daftar tunggu</label>
                            <div class="col-sm-12 col-md-8">
                                <select name="id_waiting_list" id="daftarTungguPenjadwalan" class="form-select single-select"></select>
                            </div>
                        </div>
                        <!-- akhir daftar tunggu -->
                    <?php endif ?>
                    <!-- mulai dokter -->
                    <div class="row mb-3 align-items-center">
                        <label for="dokterPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Dokter</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="dokter" id="dokterPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($dokter as $d): ?>
                                    <option id="dokterPenjadwalan<?= $d['id_dokter'] ?>" class="isiDokterPenjadwalan" value="<?= $d['id_dokter'] ?>" data-smf="<?= $d['id_smf'] ?>" <?= isset($detail['id_dokter']) && $detail['id_dokter'] == $d['id_dokter'] ? 'selected' : 'disabled' ?>>
                                        <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir dokter -->
                    <!-- mulai diagnosis -->
                    <div class="row mb-3 align-items-center">
                        <label for="diagnosisPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Diagnosis</label>
                        <div class="col-sm-12 col-md-8">
                            <textarea name="diagnosis" id="diagnosisPenjadwalan" class="form-control form-control-sm" placeholder="Tuliskan diagnosis" readonly><?= $detail['diagnosis'] ?? null ?></textarea>
                        </div>
                    </div>
                    <!-- akhir diagnosis -->
                    <!-- mulai tindakan operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="tindakanPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Tindakan operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <textarea name="tindakan" id="tindakanPenjadwalan" class="form-control form-control-sm" placeholder="Tuliskan tindakan operasi" readonly><?= $detail['tindakan'] ?? null ?></textarea>
                        </div>
                    </div>
                    <!-- akhir tindakan operasi -->
                    <!-- mulai tanggal rawat -->
                    <div class="row align-items-center">
                        <label for="tglRawatPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Tanggal rawat</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="date" name="tgl_rencanaMasuk" id="tglRawatPenjadwalan" class="form-control form-control-sm" placeholder="Pilih tanggal rawat" value="<?= $detail['tgl_rawat'] ?? null ?>" <?= isset($detail['tgl_rawat']) ? 'readonly' : null ?>>
                        </div>
                    </div>
                    <!-- akhir tanggal rawat -->
                </div>
                <!-- akhir kolom 1 -->
                <!-- mulai kolom 2 -->
                <div class="col">
                    <!-- mulai tanggal operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="tanggalOperasiPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Tanggal operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <div class="input-group input-group-sm">
                                <input type="date" name="tgl_operasi" id="tanggalOperasiPenjadwalan" class="form-control form-control-sm" placeholder="Tanggal operasi" value="<?= $detail['tgl_operasi'] ?? null ?>" readonly>
                                <button class="btn btn-outline-secondary" type="button" id="pilihTanggalOperasiPenjadwalan">Pilih</button>
                            </div>
                        </div>
                    </div>
                    <!-- akhir tanggal operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Tujuan</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="tujuan" id="tujuanPenjadwalan" class="form-control form-control-sm select2" required>
                                <!-- <option value="">Pilih Tujuan</option> -->
                            </select>
                        </div>
                    </div>
                    <!-- mulai kamar operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="kamarOperasiPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Kamar operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="kamar_operasi" id="kamarOperasiPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($kamar_operasi as $ko): ?>
                                    <option id="kamarOperasiPenjadwalan<?= $ko['id'] ?>" value="<?= $ko['id'] ?>"><?= $ko['nama'] ?></option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir kamar operasi -->
                    <!-- mulai waktu operasi -->
                     <!-- <pre><?= var_dump($detail['waktu_operasi'] ?? 'N/A') ?></pre> -->
                    <div class="row mb-3 align-items-center">
                        <label for="waktuOperasiPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Waktu operasi dimulai</label>
                        <div class="col-sm-12 col-md-8">
                        <input type="text" name="waktu_operasi" id="waktuOperasiPenjadwalan" class="form-control form-control-sm" placeholder="Waktu operasi" value="<?= isset($detail['waktu_operasi']) ? date('H:i', strtotime($detail['waktu_operasi'])) : '' ?>">
                        </div>
                    </div>
                    <!-- akhir waktu operasi -->
                    <!-- mulai durasi operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="durasiOperasiPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Durasi operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <div class="input-group input-group-sm">
                                <input type="number" name="durasi_operasi" id="durasiOperasiPenjadwalan" class="form-control form-control-sm" placeholder="Tuliskan durasi operasi (dalam menit)" value="<?= isset($detail['durasi_operasi']) ? $detail['durasi_operasi'] : (isset($detail['perkiraan_lama_operasi']) ? $detail['perkiraan_lama_operasi'] : null) ?>">
                                <span class="input-group-text">menit</span>
                            </div>
                        </div>
                    </div>
                    <!-- akhir durasi operasi -->
                    <!-- mulai dokter anestesi -->
                    <div class="row mb-3 align-items-center">
                        <label for="dokterAnestesiPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Dokter anestesi</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="dr_anestesi" id="dokterAnestesiPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($dokter_anestesi as $da): ?>
                                    <option id="dokterAnestesiPenjadwalan<?= $da['id_dokter'] ?>" value="<?= $da['id_dokter'] ?>" data-smf="<?= $da['id_smf'] ?>">
                                        <?= $da['dokter'] . ' - ' . $da['smf'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir dokter anestesi -->
                    <!-- mulai jenis anestesi -->
                    <div class="row mb-3 align-items-center">
                        <label for="jenisAnestesiPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Jenis anestesi</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="jenis_anestesi" id="jenisAnestesiPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($jenis_anestesi as $ja): ?>
                                    <option id="jenisAnestesiPenjadwalan<?= $ja['id_variabel'] ?>" value="<?= $ja['id_variabel'] ?>">
                                        <?= $ja['variabel'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir jenis anestesi -->
                    <!-- mulai keterangan jenis anestesi -->
                    <div class="row mb-3 align-items-center <?= isset($detail['jenis_anestesi']) && $detail['jenis_anestesi'] == '2138' ? null : 'd-none' ?>" id="formKeteranganJenisAnestesiPenjadwalan">
                        <label for="keteranganJenisAnestesiPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Keterangan jenis anestesi</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="keterangan_jenis_anestesi" id="keteranganJenisAnestesiPenjadwalan" class="form-control form-control-sm" placeholder="Tuliskan keterangan jenis anestesi" value="<?= $detail['rencana_jenis_pembiusan_lain'] ?? null ?>">
                        </div>
                    </div>
                    <!-- akhir keterangan jenis anestesi -->
                    <!-- mulai menunggu konfirmasi ruang -->
                    <div class="row mb-3 align-items-center">
                        <label for="menungguKonfirmasiRuangPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Menunggu konfirmasi ruang</label>
                        <div class="col-sm-12 col-md-8 px-4">
                            <div class="row">
                                <?php foreach ($menunggu_konfirmasi_ruang as $mkr): ?>
                                    <div class="col form-check form-check-inline">
                                        <input type="radio" name="menunggu_konfirmasi_ruang" id="menungguKonfirmasiRuangPenjadwalan<?= $mkr['id_variabel'] ?>" class="form-check-input menunggu-konfirmasi-ruang-penjadwalan" value="<?= $mkr['id_variabel'] ?>" <?= isset($detail['menunggu_konfirmasi_ruang']) ? ($mkr['id_variabel'] == $detail['menunggu_konfirmasi_ruang'] ? 'checked' : null) : null ?>>
                                        <label for="menungguKonfirmasiRuangPenjadwalan<?= $mkr['id_variabel'] ?>" class="form-check-label">
                                            <?= $mkr['variabel'] ?>
                                        </label>
                                    </div>
                                <?php endforeach ?>
                            </div>
                        </div>
                    </div>
                    <!-- akhir menunggu konfirmasi ruang -->
                    <!-- mulai ruang rawat -->
                    <div class="row mb-3 align-items-center d-none" id="formRuangRawatPenjadwalan">
                        <label for="ruangRawatPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Ruang rawat</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="ruang_rawat" id="ruangRawatPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($ruang_rawat as $rr): ?>
                                    <option id="ruangRawatPenjadwalan<?= $rr['ID'] ?>" value="<?= $rr['ID'] ?>">
                                        <?= $rr['DESKRIPSI'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir ruang rawat -->
                    <!-- mulai potong beku -->
                    <div class="row mb-3 align-items-center">
                        <label for="potongBekuPenjadwalan" class="col-sm-12 col-md-4 col-form-label">Potong beku</label>
                        <div class="col-sm-12 col-md-8 px-4">
                            <div class="row">
                                <?php foreach ($potong_beku as $pb): ?>
                                    <div class="col form-check form-check-inline">
                                        <input type="radio" name="potong_beku" id="potongBekuPenjadwalan<?= $pb['id_variabel'] ?>" class="form-check-input potong-beku-penjadwalan" value="<?= $pb['id_variabel'] ?>" <?= isset($detail['potong_beku']) ? ($pb['id_variabel'] == $detail['potong_beku'] ? 'checked' : null) : null ?>>
                                        <label for="potongBekuPenjadwalan<?= $pb['id_variabel'] ?>" class="form-check-label">
                                            <?= $pb['variabel'] ?>
                                        </label>
                                    </div>
                                <?php endforeach ?>
                            </div>
                        </div>
                    </div>
                    <!-- akhir potong beku -->
                    <!-- mulai join operasi -->
                    <div class="row align-items-center">
                        <label for="joinOperasiPenjadwalan" class="col-sm-12 col-md-4 col-form-label"><em>Join</em> operasi</label>
                        <div class="col-sm-12 col-md-8 px-4">
                            <div class="row">
                                <?php foreach ($join_operasi as $jo): ?>
                                    <div class="col form-check form-check-inline">
                                        <input type="radio" name="join_operasi" id="joinOperasiPenjadwalan<?= $jo['id_variabel'] ?>" class="form-check-input join-operasi-penjadwalan" value="<?= $jo['id_variabel'] ?>" <?= isset($detail['join_operasi']) ? ($jo['id_variabel'] == $detail['join_operasi'] ? 'checked' : null) : null ?>>
                                        <label for="joinOperasiPenjadwalan<?= $jo['id_variabel'] ?>" class="form-check-label">
                                            <?= $jo['variabel'] ?>
                                        </label>
                                    </div>
                                <?php endforeach ?>
                            </div>
                        </div>
                    </div>
                    <!-- akhir join operasi -->
                </div>
                <!-- akhir kolom 2 -->
            </div>
        </div>
    </div>
    <!-- akhir kartu form -->
    <!-- mulai kartu dokter lain -->
    <div class="card mb-3 <?= !empty($dokter_lain) ? null : 'd-none' ?>" id="cardDokterLainPenjadwalan">
        <div class="card-body">
            <h5 class="card-title">Dokter Bedah Lain dan Tindakannya</h5>
            <hr>
            <table class="table table-striped table-bordered table-hover w-100" id="tabelDokterLainPenjadwalan">
                <thead>
                    <tr>
                        <th class="text-center" scope="col">No.</th>
                        <th class="text-center" scope="col">Dokter</th>
                        <th class="text-center" scope="col">Tindakan</th>
                    </tr>
                </thead>
                <tbody id="isiDokterLainPenjadwalan">
                    <?php
                    $i = 1;
                    if (!empty($dokter_lain)) {
                        foreach ($dokter_lain as $dl) {
                            ?>
                            <tr>
                                <td><?= $i++ ?>.</td>
                                <td><?= $dl['dokter_bedah_lain'] ?></td>
                                <td><?= $dl['rencana_tindakan'] ?? '-' ?></td>
                            </tr>
                            <?php
                        }
                    }
                    ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th class="text-center" scope="col">No.</th>
                        <th class="text-center" scope="col">Dokter</th>
                        <th class="text-center" scope="col">Tindakan</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <!-- akhir kartu dokter lain -->
    <!-- mulai aksi -->
    <div class="row">
        <div class="col">
            <button type="button" class="btn btn-primary" id="simpanPenjadwalan">
                <?= isset($detail['id_penjadwalan']) ? 'Simpan perubahan' : 'Simpan' ?>
            </button>
        </div>
    </div>
    <!-- akhir aksi -->
</form>
<!-- akhir form -->

<!-- mulai modal pilih tanggal -->
<div class="modal fade" id="modalTanggalPenjadwalan" aria-labelledby="pilihTanggalPenjadwalan" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pilihTanggalPenjadwalan">Pilih Tanggal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-sm-1">
                        <button type="button" class="btn btn-primary btn-block" id="tambahJadwalPenjadwalan">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </div>
                    <div class="col-sm-1 text-center">
                        <i class="fa-solid fa-angle-left fa-3x" id="prevPenjadwalan" style="cursor: pointer;opacity: 0.6"></i>
                    </div>
                    <div class="col-sm-9">
                        <input class="form-control bg-warning text-dark text-center fw-bold shadow-none" type="month" name="bulan" id="bulanPenjadwalan" readonly>
                    </div>
                    <div class="col-sm-1 text-center">
                        <i class="fa-solid fa-angle-right fa-3x" id="nextPenjadwalan" style="cursor: pointer;opacity: 0.6"></i>
                    </div>
                </div>
                <div class="row" id="jadwalPenjadwalan"></div>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal pilih tanggal -->

<!-- mulai modal tambah jadwal dokter -->
<div class="modal fade " id="modalJadwalPenjadwalan" aria-labelledby="judulModalJadwalPenjadwalan" aria-hidden="true">
    <form id="formJadwalPenjadwalan" autocomplete="off">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mt3">Form Tambah Jadwal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- mulai ruang -->
                    <div class="row mb-3 align-items-center">
                        <label class="col-sm-3 col-form-label">Ruang</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control form-control-sm" name="jruangan" id="valJruangan" value="Instalasi Bedah Pusat" readonly>
                        </div>
                    </div>
                    <!-- akhir ruang -->
                    <!-- mulai tanggal -->
                    <div class="row mb-3 align-items-center">
                        <label class="col-sm-3 col-form-label">Tanggal</label>
                        <div class="col-sm-9">
                            <input type="date" class="form-control form-control-sm" name="jtanggal" id="valjTanggal" required>
                        </div>
                    </div>
                    <!-- akhir tanggal -->
                    <!-- mulai waktu -->
                    <div class="row mb-3 align-items-center">
                        <label class="col-sm-3 col-form-label">Waktu</label>
                        <div class="col-sm-9">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">dari</span>
                                <input type="number" class="form-control form-control-sm" name="awal" id="valAwal" placeholder="Waktu mulai" min="0" max="23">
                                <span class="input-group-text">sampai</span>
                                <input type="number" class="form-control form-control-sm" name="akhir" id="valAkhir" placeholder="Waktu selesai" min="0" max="23">
                            </div>
                        </div>
                    </div>
                    <!-- akhir waktu -->
                    <!-- mulai kuota -->
                    <div class="row align-items-center">
                        <label class="col-sm-3 col-form-label">Kuota</label>
                        <div class="col-sm-9">
                            <input type="number" class="form-control form-control-sm" name="kuota" id="valKuota" max="999" placeholder="Jumlah pasien maksimal">
                        </div>
                    </div>
                    <!-- akhir kuota -->
                    <input type="hidden" name="jdokter" id="valJdokter">
                    <input type="hidden" name="jid" id="valJid">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="simpanJadwalPenjadwalan">Simpan</button>
                </div>
            </div>
        </div>
    </form>
</div>
<!-- akhir modal tambah jadwal dokter -->

<script>
    $(document).ready(function () {
        $('#tujuanPenjadwalan').select2({
            placeholder: 'Pilih Tujuan',
            allowClear: true,
            ajax: {
                url: '<?= base_url('admin/penjadwalan/get_tujuan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });
        <?php if(isset($detail['tujuan_rs'])): ?>
            $.ajax({
                url: '<?= base_url('admin/penjadwalan/get_tujuan') ?>',
                dataType: 'json',
                success: function(data) {
                    if(data && data.length > 0) {
                        var defaultTujuan = data.find(function(item) {
                            return item.ID == '<?= $detail['tujuan_rs'] ?>';
                        });
                        if(defaultTujuan) {
                            var option = new Option(defaultTujuan.DESKRIPSI, defaultTujuan.ID, true, true);
                            $('#tujuanPenjadwalan').append(option).trigger('change');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        <?php endif; ?>
        // end tujuan_rs
        // mulai nomor rekam medis
        let norm;
        <?php if (isset($detail['norm'])): ?>
            norm = $('#mrPenjadwalan').val();

            // mulai ambil pendaftaran pra operasi
            $('#tpoPenjadwalan').select2({
                placeholder: 'Pilih pendaftaran pra operasi',
                ajax: {
                    url: "<?= base_url('operasi/pendaftaran_pasien/Pasien_baru/pilih_pendaftaran_operasi/') ?>" + norm,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            });
            <?php if (isset($detail['id_tpo'])): ?>
                $('#tpoPenjadwalan').append("<option value='<?= $detail['id_tpo'] ?>'><?= $detail['nokun'] . ' - ' . $detail['diagnosa_medis'] . ' - ' . date('d/m/Y', strtotime($detail['tanggal_operasi'])) ?></option>").trigger('change').prop('disabled', true);
            <?php endif ?>
            // akhir ambil pendaftaran pra operasi
        <?php else: ?>
            // mulai cari data pasien
            $('#mrPenjadwalan').select2({
                placeholder: 'Pilih data pasien'
            }).val(<?= $detail['norm'] ?? 0 ?>).trigger('change').change(function () {
                norm = $(this).val();

                // mulai hapus input
                $('#isiDokterLainPenjadwalan').html(null);
                $('#cardDokterLainPenjadwalan').addClass('d-none');
                $(":text, input[type='date'], input[type='tel'], input[type='time'], input[type='number'], textarea").val(null);
                $('#daftarTungguPenjadwalan, #dokterPenjadwalan, #dokterAnestesiPenjadwalan, #jenisAnestesiPenjadwalan, #ruangRawatPenjadwalan').val(0).trigger('change');
                // akhir hapus input

                $.ajax({
                    type: 'POST',
                    url: "<?= base_url('admin/Perjanjian/ambil_data_pasien_daftar_tunggu') ?>",
                    data: {
                        norm: norm,
                    },
                    success: function (data) {
                        if (data === null || data === '' || data === 'null') {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: 'Data tidak ditemukan',
                                sound: false,
                                title: 'Peringatan'
                            });
                        } else {
                            Lobibox.notify('success', {
                                icon: 'bx bx-check-circle',
                                msg: 'Data ditemukan',
                                sound: false,
                                title: 'Berhasil'
                            });

                            // mulai ambil data
                            let json = JSON.parse(data);
                            $('#namaPenjadwalan').val(json['nama']);
                            $('#teleponPenjadwalan').val(json['telepon']);

                            // mulai ambil daftar tunggu
                            $('#daftarTungguPenjadwalan').select2({
                                placeholder: 'Pilih daftar tunggu',
                                ajax: {
                                    url: "<?= base_url('admin/Daftar_tunggu/pilih_daftar_tunggu/') ?>" + norm,
                                    dataType: 'json',
                                    delay: 250,
                                    processResults: function (data) {
                                        return {
                                            results: data
                                        };
                                    },
                                    cache: true
                                }
                            }).val(0).trigger('change');
                            // akhir ambil daftar tunggu
                            // akhir ambil data
                        }
                    }
                });
            });
            // akhir cari data pasien

            // mulai daftar tunggu
            $('#daftarTungguPenjadwalan').select2().change(function () {
                let id = $(this).val();
                if ($.trim(id)) {
                    $.ajax({
                        type: 'POST',
                        url: "<?= base_url('admin/Daftar_tunggu/ambil_pilihan') ?>",
                        data: {
                            id: id,
                        },
                        success: function (data) {
                            if ($.trim(data)) {
                                // mulai ambil data
                                let json = $.parseJSON(data);
                                let detail = json['detail'];

                                $('#idPendaftaranPenjadwalan').val(detail['id_pendaftaran_operasi']);
                                $('#dokterPenjadwalan').val(detail['id_dokter']).trigger('change');
                                $('.isiDokterPenjadwalan').removeAttr('disabled', null);
                                $('#diagnosisPenjadwalan').val(detail['diagnosis']);
                                $('#tindakanPenjadwalan').val(detail['tindakan']);
                                if (detail['jam_operasi']) {
                                    fpWaktuOperasi.setDate(detail['jam_operasi'], false, "H:i");
                                } else {
                                    let now = new Date();
                                    let fallback = String(now.getHours()).padStart(2, '0') + ":" + String(now.getMinutes()).padStart(2, '0');
                                    fpWaktuOperasi.setDate(fallback, false, "H:i");
                                }
                                $('#durasiOperasiPenjadwalan').val(detail['perkiraan_lama_operasi']);
                                $('#jenisAnestesiPenjadwalan').val(detail['rencana_jenis_pembiusan']).trigger('change');
                                $('#keteranganJenisAnestesiPenjadwalan').val(detail['rencana_jenis_pembiusan_lain']);
                                $('#ruangRawatPenjadwalan').val(detail['ruang_tujuan']).trigger('change');
                                $('#potongBekuPenjadwalan' + detail['potong_beku']).prop('checked', true);
                            
                                // flatpickr("#waktuOperasiPenjadwalan", {
                                //     enableTime: true,    
                                //     noCalendar: true,    
                                //     dateFormat: "H:i",   
                                //     time_24hr: true,     
                                //     locale: "id"
                                // });

                                // mulai tanggal rencana operasi baru
                                let tanggal_rencana = null;
                                if (detail['tgl_rencana'] === '0000-00-00') {
                                    tanggal_rencana = null;
                                } else {
                                    tanggal_rencana = detail['tgl_rencana'];
                                }
                                $('#tanggalOperasiPenjadwalan').val(tanggal_rencana);
                                // akhir tanggal rencana operasi baru

                                // mulai dokter lain
                                let tindakan = json['tindakan'];
                                let isi = null;
                                let i = 0;
                                let rencana_tindakan = null;

                                $('#isiDokterLainPenjadwalan').html(null);
                                if (tindakan.length) {
                                    $.each(tindakan, function (j) {
                                        // mulai rencana tindakan
                                        if (tindakan[j].rencana_tindakan !== null) {
                                            rencana_tindakan = tindakan[j].rencana_tindakan;
                                        } else {
                                            rencana_tindakan = '-';
                                        }
                                        // akhir rencana tindakan

                                        // mulai isi
                                        isi = '<tr>' +
                                            '<td>' + ++i + ".<input type='hidden' class='isiIdDokterLainEditPenjadwalan' name='dokter_bedah[]' value='" + tindakan[j].id_dokter_bedah_lain + "'></td>" +
                                            '<td>' + tindakan[j].dokter_bedah_lain + '</td>' +
                                            '<td>' + rencana_tindakan + '</td>' +
                                            '</tr>';
                                        $(isi).hide().appendTo('#isiDokterLainPenjadwalan').fadeIn(1000);
                                        // akhir isi
                                    });
                                    $('#cardDokterLainPenjadwalan').removeClass('d-none');
                                } else {
                                    $('#cardDokterLainPenjadwalan').addClass('d-none');
                                }
                                // akhir dokter lain

                                // mulai join operasi
                                if (detail['join_operasi'] !== null && detail['join_operasi'] !== '') {
                                    $('#joinOperasiPenjadwalan' + detail['join_operasi']).prop('checked', true);
                                } else {
                                    $('.join-operasi-penjadwalan').prop('checked', false);
                                }
                                // akhir join operasi
                                // akhir ambil data
                            }
                        }
                    });
                }
            });
            // akhir daftar tunggu
        <?php endif ?>
        // akhir nomor rekam medis
        let initialTime = "<?= isset($detail['waktu_operasi']) ? date('H:i', strtotime($detail['waktu_operasi'])) : '' ?>";
        if (!initialTime) {
            let now = new Date();
            initialTime = String(now.getHours()).padStart(2, '0') + ":" + String(now.getMinutes()).padStart(2, '0');
        }

        const fpWaktuOperasi = flatpickr("#waktuOperasiPenjadwalan", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "H:i",
            time_24hr: true,
            locale: "id",
            defaultDate: initialTime
        });

        // mulai dokter
        $('#dokterPenjadwalan').select2({
            placeholder: 'Pilih dokter'
        }).val(<?= $detail['id_dokter'] ?? 0 ?>).trigger('change');
        // akhir dokter

        // mulai pilih tanggal operasi
        $('#pilihTanggalOperasiPenjadwalan').click(function () {
            $('.rencana').css('display', 'none');
            $('.operasi').css('display', 'none');
            if ($('#mrPenjadwalan').val() === null || $('#dokterPenjadwalan').val() === null || $('#namaPenjadwalan').val() === null || $('#teleponPenjadwalan').val() === null) { // Periksa form
                if ($('#mrPenjadwalan').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Nomor RM wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
                if ($('#dokterPenjadwalan').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Dokter wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
                if ($('#namaPenjadwalan').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Nama wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
                if ($('#teleponPenjadwalan').val() === null) {
                    Lobibox.notify('warning', {
                        icon: 'bx bx-error',
                        msg: 'Nomor telepon wajib diisi',
                        sound: false,
                        title: 'Peringatan'
                    });
                }
            } else {
                jadwal();
            }
        });
        // akhir pilih tanggal operasi

        // mulai bulan
        let tanggal = new Date();
        let bulan = ('0' + (tanggal.getMonth() + 1)).slice(-2);
        let tahun = tanggal.getFullYear();
        $('#bulanPenjadwalan').val(tahun + '-' + bulan);
        // akhir bulan

        // mulai ganti bulan operasi
        // mulai bulan sebelumnya
        $('#prevPenjadwalan').hover(function () {
            $(this).css('opacity', 1);
        }, function () {
            $(this).css('opacity', 0.6);
        });
        $('#prevPenjadwalan').click(function () {
            if (tahun + '-' + bulan != $('#bulanPenjadwalan').val()) {
                document.getElementById('bulanPenjadwalan').stepDown();
                jadwal();
            } else {
                Lobibox.notify('warning', {
                    icon: 'bx bx-error',
                    msg: 'Tidak bisa mengambil data sebelum bulan ini',
                    sound: false,
                    title: 'Peringatan'
                });
            }
        });
        // akhir bulan sebelumnya

        // mulai bulan seleanjutnya
        $('#nextPenjadwalan').hover(function () {
            $(this).css('opacity', 1);
        }, function () {
            $(this).css('opacity', 0.6);
        });
        $('#nextPenjadwalan').click(function () {
            document.getElementById('bulanPenjadwalan').stepUp();
            jadwal();
        });
        // akhir bulan selanjutnya
        // akhir ganti bulan operasi

        // mulai fungsi jadwal
        function jadwal() {
            let dokter = $('#dokterPenjadwalan').val();
            let norm = $('#mrPenjadwalan').val();
            let smf = $('#dokterPenjadwalan option:selected').data('smf');
            let txtdokter = $('#dokterPenjadwalan').text();
            let bulan = $('#bulanPenjadwalan').val();
            let poli = 0;

            $.ajax({
                url: "<?= base_url('admin/Perjanjian/jadwal') ?>",
                type: 'POST',
                data: {
                    dokter: dokter,
                    bulan: bulan,
                    tahun: tahun,
                    smf: smf,
                    norm: norm,
                    poli: poli
                },
                dataType: 'JSON',
                success: function (data) {
                    $('#jadwalPenjadwalan').html(null);
                    $('#modalTanggalPenjadwalan').modal('show');

                    if (data.length) {
                        $.each(data, function (index, element) {
                            let bg = 'bg-info';
                            if (element.id_ruangan == 105020201 || element.id_ruangan == 105020202) {
                                bg = 'bg-success';
                            }

                            let cls = 'tanggal';
                            if (parseInt(element.jumlah) >= parseInt(element.kuota)) {
                                bg = 'bg-danger';
                                // cls = "penuh";
                            }

                            let kuota = element.kuota;
                            if (kuota == -1) {
                                kuota = '~';
                            }

                            let pagi = '';
                            let sore = '';
                            let ikon_sore = '';
                            let ikon_pagi = '';

                            if (element.kuota !== null) {
                                ikon_pagi = '<a class="link-light changeSchedule" data-status="2" data-id="' + element.id + '" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>'
                            }

                            if (element.kuota_sore !== null) {
                                sore = '<div class="card-body p-0 d-none" id="areaSore' + element.id + '">' +
                                    '<div class="d-flex justify-content-between">' +
                                    '<h4><span class="badge text-bg-dark rounded-start-0 rounded-end-pill"><i class="fas fa-clock fa-sm"></i> ' + element.waktu_sore + '</span></h4>' +
                                    '<h6 class="mt-2">' + ikon_pagi + '</h6>' +
                                    '<h4 class="card-title text-right"><span class="badge text-bg-dark rounded-start-pill rounded-end-0">' + element.jumlah_sore + '/' + element.kuota_sore + '</span></h4>' +
                                    '</div>' +
                                    '<div class="card-body p-0 m-0 ' + cls + ' " tanggal="' + element.tanggal + '" ruangan="' + element.ruangan + '" idr="' + element.id_ruangan + '" sore="1" style="cursor: pointer;">' +
                                    '<p class="card-text text-center text-warning fw-bold fs-1"><u>' + element.day + '</u></p>' +
                                    '</div>' +
                                    '</div>';
                                ikon_sore = '<a class="link-light changeSchedule" data-status="1" data-id="' + element.id + '" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>';
                            }

                            if (element.kuota !== null) {
                                pagi = '<div class="card-body p-0" id="areaPagi' + element.id + '">' +
                                    '<div class="d-flex justify-content-between">' +
                                    '<h4><span class="badge text-bg-primary rounded-start-0 rounded-end-pill"><i class="fas fa-clock" style="font-size: 14px;"></i> ' + element.waktu + '</span></h4>' +
                                    '<h6 class="mt-2">' + ikon_sore + '</h6>' +
                                    '<h4 class="card-title text-right"><span class="badge text-bg-warning rounded-start-pill rounded-end-0">' + element.jumlah + '/' + kuota + '</span></h4>' +
                                    '</div>' +
                                    '<div class="card-body p-0 m-0 ' + cls + ' " tanggal="' + element.tanggal + '" ruangan="' + element.ruangan + '" idr="' + element.id_ruangan + '" sore="0" style="cursor: pointer;">' +
                                    '<p class="card-text text-center text-warning fw-bold fs-1"><u>' + element.day + '</u></p>' +
                                    '</div>' +
                                    '</div>';
                            }

                            if (element.kuota !== null || element.kuota_sore !== null) {
                                $('#jadwalPenjadwalan').append(
                                    '<div class="col-3 mb-3">' +
                                    '<div class="card text-white h-100" style="background-color:' + element.color + '">' +
                                    '<div class="card-header text-center text-warning" style="font-size: 12px;">' + element.ruangan + '</div>' +
                                    pagi +
                                    sore +
                                    '<div class="card-footer text-center text-warning">' + element.hari + '</div>' +
                                    '</div>');
                                if (element.kuota !== null) {
                                    if (element.jumlah == element.kuota && element.kuota_sore !== null) {
                                        $('#areaPagi' + element.id).addClass('d-none');
                                        $('#areaSore' + element.id).removeClass('d-none');
                                    } else {
                                        $('#areaPagi' + element.id).removeClass('d-none');
                                        $('#areaSore' + element.id).addClass('d-none');
                                    }
                                } else {
                                    $('#areaPagi' + element.id).addClass('d-none');
                                    $('#areaSore' + element.id).removeClass('d-none');
                                }
                            }
                        });

                        $('.changeSchedule').click(function () {
                            let id = $(this).attr('data-id');
                            let status = $(this).attr('data-status');
                            if (status == 1) {
                                $('#areaPagi' + id).addClass('d-none');
                                $('#areaSore' + id).removeClass('d-none');
                            } else {
                                $('#areaPagi' + id).removeClass('d-none');
                                $('#areaSore' + id).addClass('d-none');
                            }
                        });
                    } else {
                        // $('#mt1').html(txtdokter);
                        $('#jadwalPenjadwalan').append(
                            "<div class='col'>" +
                            "<div class='alert alert-warning' role='alert'>" +
                            "Jadwal dokter belum tersedia" +
                            "</div>" +
                            "</div>"
                        );
                    }
                },
                error: function () {
                    Lobibox.error('Ada Kesalahan!');
                }
            });
            // Close load jadwal
        }
        // akhir fungsi jadwal

        // mulai klik tanggal
        $(document).on('click', '.tanggal', function () {
            let tanggal = $(this).attr('tanggal');
            let ruangan = $(this).attr('ruangan');
            let idr = $(this).attr('idr');
            let sore = $(this).attr('sore');

            $('#btnSelesai').attr('disabled', false);
            if (idr == '105020704' || idr == '105020705' || idr == '105060101' || idr == '105120101' || idr == '105110101' || idr == '105090101' || idr == '105020708') {
                $('.rencana').css('display', 'block');
            } else if (idr == '105090101') {
                $('.operasi').css('display', 'block');
            }

            $('#tanggalOperasiPenjadwalan').val(tanggal);
            $('#ruangPenjadwalan').val(ruangan);
            $('#idRuangPenjadwalan').val(idr);
            $('#sorePenjadwalan').val(sore);
            $('#modalTanggalPenjadwalan').modal('hide');
        });
        // akhir klik tanggal

        // mulai kamar operasi
        $('#kamarOperasiPenjadwalan').select2({
            placeholder: 'Pilih kamar'
        }).val(<?= $detail['kamar_operasi'] ?? 0 ?>).trigger('change');
        // akhir kamar operasi

        // mulai waktu operasi
        // $('#waktuOperasiPenjadwalan').timepicker();
        // akhir waktu operasi

        // mulai dokter anestesi
        $('#dokterAnestesiPenjadwalan').select2({
            placeholder: 'Pilih dokter anestesi'
        }).val(<?= $detail['dr_anestesi'] ?? 0 ?>).trigger('change');
        // akhir dokter anestesi

        // mulai jenis anestesi
        $('#jenisAnestesiPenjadwalan').select2({
            placeholder: 'Pilih jenis anestesi'
        }).val(<?= $detail['jenis_anestesi'] ?? 0 ?>).trigger('change');

        $('#jenisAnestesiPenjadwalan').change(function () {
            let id = $(this).val();
            if (id === '2138') {
                $('#formKeteranganJenisAnestesiPenjadwalan').removeClass('d-none');
            } else {
                $('#formKeteranganJenisAnestesiPenjadwalan').addClass('d-none');
                $('#keteranganJenisAnestesiPenjadwalan').val(null);
            }
        });
        // akhir jenis anestesi

        // mulai menunggu konfirmasi ruang
        $('.menunggu-konfirmasi-ruang-penjadwalan').click(function () {
            if ($(this).val() === '6233') {
                $('#formRuangRawatPenjadwalan').addClass('d-none');
            } else if ($(this).val() === '6234') {
                $('#formRuangRawatPenjadwalan').removeClass('d-none');
            }
        });
        if ($('#menungguKonfirmasiRuangPenjadwalan6233').prop('checked')) {
            $('#formRuangRawatPenjadwalan').addClass('d-none');
        } else if ($('#menungguKonfirmasiRuangPenjadwalan6234').prop('checked')) {
            $('#formRuangRawatPenjadwalan').removeClass('d-none');
        }
        // akhir menunggu konfirmasi ruang

        // mulai ruang rawat
        $('#ruangRawatPenjadwalan').select2({
            placeholder: 'Pilih ruang'
        }).val(<?= $detail['ruang_rawat'] ?? 0 ?>).trigger('change');
        // akhir ruang rawat

        // mulai simpan
        $('#simpanPenjadwalan').click(function () {
            let form = $('#formBuatPenjadwalan').serialize();
            $.ajax({
                url: "<?= base_url('admin/Penjadwalan/simpan') ?>",
                dataType: 'json',
                type: 'POST',
                data: form,
                success: function (data) {
                    if (data.status === 'success') {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Berhasil menyimpan',
                            sound: false,
                            title: 'Berhasil',
                        });
                        window.location.href = "<?= base_url('admin/Penjadwalan/index') ?>";
                    } else {
                        $.each(data.errors, function (index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                },

                error: function (jqXHR, textStatus, errorThrown) {
                    Lobibox.notify('error', {
                        icon: 'bx bx-x-circle',
                        msg: 'Internal Server Error!',
                        sound: false
                    });
                }
            });
        });
        // akhir simpan

        // mulai tambah jadwal dokter
        $('#tambahJadwalPenjadwalan').click(function () {
            $('#modalJadwalPenjadwalan').modal('show');
            $('#formJadwalPenjadwalan')[0].reset();
            $('#valJdokter').val($('#dokterPenjadwalan').val());
        });
        // akhir tambah jadwal dokter

        // mulai simpan jadwal dokter
        $('#simpanJadwalPenjadwalan').click(function () {
            let data = $('#formJadwalPenjadwalan').serialize();
            $.ajax({
                url: "<?= base_url('admin/perjanjian/tambah_jadwal') ?>",
                dataType: 'json',
                type: 'POST',
                data: data,
                success: function (data) {
                    if (data.status == '200') {
                        $('#modalJadwalPenjadwalan').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Berhasil menambah jadwal',
                            sound: false,
                            title: 'Berhasil'
                        });
                        jadwal();
                    } else {
                        $.each(data.errors, function (index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                },
                error: function (e) {
                    toastr.error('Ada kesalahan');
                }
            });
        });
        // akhir simpan jadwal dokter
    });
</script>