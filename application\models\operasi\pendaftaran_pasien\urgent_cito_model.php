<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Urgent_cito_model extends CI_Model
{
    protected $_table = 'medis.tb_pendaftaran_operasi penop';
    protected $_primary_key = 'tpo.nokun';
    protected $_order_by = 'tpo.nokun';
    protected $_order_by_type = 'asc';
    protected $_urutan = ['mp.NAMA' => 'asc'];

    protected $_urutan_kolom = [
        null,
        'nama_pasien',
        'pen.NORM',
        'penop.diagnosa_medis',
        'penop.rencana_tindakan_operasi',
        'master.getNamaLengkapPegawai(dokb.NIP)',
        'r_rwt.DESKRIPSI',
        'var.variabel',
        'rmp.TANGGAL',
        "date_format(penop.created_at,'%d-%m-%Y %H:%i')",
        'alasan_sifat_op'
    ];

    protected $_pencarian_kolom = [
        'master.getNamaLengkap(pen.NORM)',
        'pen.NORM',
        'penop.diagnosa_medis',
        'penop.rencana_tindakan_operasi',
        'master.getNamaLengkapPegawai(dokb.NIP)',
        'r_rwt.DESKRIPSI',
        'var.variabel',
        'rmp.TANGGAL',
        "date_format(penop.created_at,'%d-%m-%Y %H:%i')"
    ];

    public function tabel()
    {
        $this->db->select(
            "master.getNamaLengkap(pen.NORM) nama_pasien,
            pen.NORM,
            penop.nokun,
            CONCAT(
                (date_format(ps.TANGGAL_LAHIR,'%d-%m-%Y')),
                ' (',(master.getCariUmurTahun(CURDATE(), DATE(ps.TANGGAL_LAHIR))),')'
            ) tgl_lahir_umur,
            date_format(penop.created_at,'%d-%m-%Y %H:%i') waktu_daftar,
            penop.diagnosa_medis,
            penop.rencana_tindakan_operasi,
            master.getNamaLengkapPegawai(dok.NIP) DPJP,
            penop.dokter_bedah iddokter_bedah,
            master.getNamaLengkapPegawai(dokb.NIP) dokter_bedah,
            penop.sifat_operasi id_sifat_op,
            var.variabel sifat_op_desk,
            CASE WHEN penop.sifat_operasi =2131 THEN alsn_c.deskripsi
                WHEN penop.sifat_operasi = 6080 THEN alsn_u.deskripsi
                WHEN penop.sifat_operasi = 6125 THEN alsn_p.deskripsi
            ELSE '-' END AS alasan_sifat_op,
            penop.tanggal_operasi tgl_rencana,
            penop.jam_operasi jam_rencana,
            po.ruang_rawat id_r_rawat,
            r_rwt.DESKRIPSI r_rawat,
            rmp.TANGGAL tgl_tindakan,
            po.waktu_operasi jam_operasi,
            po.durasi_operasi durasi_po_mins,
            penop.perkiraan_lama_operasi durasi_pen_mins,
            kmr.nama,
            penop.ruang_tujuan,
            penop.catatan_khusus,
            r.DESKRIPSI"
        );
        $this->db->from($this->_table);
        $this->db->join('pendaftaran.kunjungan kunj', 'kunj.NOMOR = penop.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran pen', 'pen.NOMOR = kunj.NOPEN', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = pen.NORM', 'left');
        $this->db->join('master.dokter dokb', 'dokb.ID = penop.dokter_bedah', 'left');
        $this->db->join('db_master.variabel var', 'var.id_variabel = penop.sifat_operasi AND var.id_referensi = 621', 'left');
        $this->db->join('db_master.tb_alasan_sifat_operasi alsn_c', 'alsn_c.id = penop.sifat_operasi_lain', 'left');
        $this->db->join('db_master.tb_alasan_sifat_operasi alsn_u', 'alsn_u.id = penop.alasan_urgent', 'left');
        $this->db->join('db_master.tb_alasan_sifat_operasi alsn_p', 'alsn_p.id = penop.alasan_prioritas', 'left');
        $this->db->join('master.ruangan r', 'r.ID = kunj.RUANGAN', 'left');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = pen.NOMOR', 'left');
        $this->db->join('master.dokter dok', 'dok.ID = tp.DOKTER', 'left');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.nokun = penop.nokun AND wlo.status != 0', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.NOMR = pen.NORM AND date(pen.TANGGAL) = rmp.TANGGAL AND rmp.STATUS != 0', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi po', 'po.id_perjanjian = rmp.ID AND po.STATUS != 0', 'left');
        $this->db->join('db_master.tb_kamar kmr', 'kmr.ID = po.kamar_operasi', 'left');
        $this->db->join('master.ruangan r_rwt', 'r_rwt.ID = po.ruang_rawat', 'left');
        $this->db->where('date(penop.created_at) > DATE_SUB(CURDATE(), INTERVAL 1 MONTH)');
        $this->db->where('penop.status !=', 0);
        $this->db->where('penop.sifat_operasi != 2129');
        $this->db->order_by('penop.created_at DESC');

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil()
    {
        $this->tabel();
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring()
    {
        $this->tabel();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua()
    {
        $this->tabel();
        return $this->db->count_all_results();
    }
}

// End of File Urgent_cito_model.php
// Location: ./application/models/operasi/pendaftaran_pasien/Urgent_cito_model.php