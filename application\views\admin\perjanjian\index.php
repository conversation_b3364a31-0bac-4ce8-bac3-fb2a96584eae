<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Per<PERSON><PERSON><PERSON></div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item active" aria-current="page">Perjanjian Operasi</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <a class="btn btn-primary" href="<?= base_url('admin/Perjanjian/form') ?>" role="button">Buat Baru</a>
        </div>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Cari Perjanjian</h5>
        <hr>
        <!-- mulai form -->
        <form id="formPerjanjianOperasi">
            <!-- mulai periode -->
            <div class="row mb-3 align-items-center">
                <label for="periodePerjanjian" class="col-2 col-form-label">Periode tanggal operasi</label>
                <div class="col-10">
                    <div class="input-group">
                        <span class="input-group-text">dari</span>
                        <input type="date" name="mulai" id="mulaiPerjanjianOperasi" class="form-control form-control-sm" placeholder="Mulai" aria-label="Mulai periode">
                        <span class="input-group-text">sampai</span>
                        <input type="date" name="akhir" id="akhirPerjanjianOperasi" class="form-control form-control-sm" placeholder="Akhir" aria-label="Akhir periode">
                    </div>
                </div>
            </div>
            <!-- akhir periode -->
            <!-- mulai dokter  -->
            <div class="row mb-3 align-items-center">
                <label for="dokterPerjanjianOperasi" class="col-2 col-form-label">Dokter</label>
                <div class="col-10">
                    <select name="dokter" id="dokterPerjanjianOperasi" class="form-select single-select">
                        <option value=""></option>
                        <?php foreach ($dokter as $d): ?>
                            <option id="dokterPerjanjianOperasi<?= $d['id_dokter'] ?>" value="<?= $d['id_dokter'] ?>">
                                <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                            </option>
                        <?php endforeach ?>
                    </select>
                </div>
            </div>
            <div class="row mb-3 align-items-center d-none">
                <label for="ruangan" class="col-2 col-form-label">Ruangan</label>
                <div class="col-10">
                <select class="form-control form-control-sm select2" name="jruangan" id="ruangan" required>
                    <option value="">Pilih Ruangan</option>
                </select>
                </div>
            </div>
            <div class="row mb-3 align-items-center">
                <label for="tujuan" class="col-2 col-form-label">Tujuan RS</label>
                <div class="col-10">
                <select class="form-control form-control-sm select2" name="tujuan" id="tujuan" >
                </select>
                </div>
            </div>

            <!-- akhir dokter  -->
            <!-- mulai tampilkan -->
            <div class="row row-cols-auto">
                <div class="col me-auto">
                    <button type="button" class="btn btn-primary" id="tblPerjanjianOperasi">Tampilkan</button>
                </div>
                <div class="col">
                    <div class="dropdown">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Laporan akan dibagi berdasarkan ukuran kertas">Laporan per Halaman</button>
                        <ul class="dropdown-menu">
                            <li>
                                <a href="#" class="dropdown-item laporan-perjanjian-operasi" data-ext="pdf" data-format="halaman" id="laporanPerHalamanPdfPerjanjianOperasi">
                                    <i class="fa-regular fa-file-pdf"></i> PDF
                                </a>
                            </li>
                            <li>
                                <a href="#" class="dropdown-item laporan-perjanjian-operasi" data-ext="xls" data-format="halaman" id="laporanPerHalamanExcelPerjanjianOperasi">
                                    <i class="fa-regular fa-file-excel"></i> Excel
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col">
                    <div class="dropdown">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Laporan tidak dibagi-bagi per halaman dan bersambung sampai selesai">Laporan Bersambung</button>
                        <ul class="dropdown-menu">
                            <li>
                                <a href="#" class="dropdown-item laporan-perjanjian-operasi" data-ext="pdf" data-format="sambung" id="laporanBersambungPdfPerjanjianOperasi">
                                    <i class="fa-regular fa-file-pdf"></i> PDF
                                </a>
                            </li>
                            <li>
                                <a href="#" class="dropdown-item laporan-perjanjian-operasi" data-ext="xls" data-format="sambung" id="laporanBersambungExcelPerjanjianOperasi">
                                    <i class="fa-regular fa-file-excel"></i> Excel
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- akhir tampilkan -->
        </form>
        <!-- akhir form -->
    </div>
</div>
<!-- akhir kartu -->

<div class="d-none" id="tampilTabelPerjanjianOperasi"></div>

<!-- mulai modal tanggal rawat -->
<div class="modal fade" id="modalTanggalRawatPerjanjianOperasi" aria-labelledby="judulModalTanggalRawatPerjanjianOperasi" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalTanggalRawatPerjanjianOperasi">Ubah Tanggal Rawat</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- mulai tanggal rawat -->
                <div class="row mb-3 align-items-center">
                    <label for="tangalUbahTanggalRawatPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Tanggal rawat</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="date" name="tgl_operasi" id="tangalUbahTanggalRawatPerjanjianOperasi" class="form-control form-control-sm" placeholder="Tanggal rawat">
                    </div>
                </div>
                <!-- akhir tanggal rawat -->
                <input type="hidden" name="id_reservasi" id="reservasiUbahTanggalRawatPerjanjianOperasi">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="tutupUbahTanggalRawatPerjanjianOperasi" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="yakinUbahTanggalRawatPerjanjianOperasi">Yakin ubah</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal tanggal rawat -->

<!-- mulai modal tanggal operasi -->
<div class="modal fade" id="modalTanggalOperasiPerjanjianOperasi" aria-labelledby="judulModalTanggalOperasiPerjanjianOperasi" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalTanggalOperasiPerjanjianOperasi">Ubah Tanggal Operasi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- mulai tanggal operasi -->
                <div class="row mb-3 align-items-center">
                    <label for="tanggalOperasiUbahPerjanjianOperasi" class="col-sm-12 col-md-4 col-form-label">Tanggal operasi</label>
                    <div class="col-sm-12 col-md-8">
                        <div class="input-group input-group-sm">
                            <input type="date" name="tgl_operasi" id="tanggalOperasiUbahPerjanjianOperasi" class="form-control form-control-sm" placeholder="Tanggal operasi" readonly>
                            <button class="btn btn-outline-secondary" type="button" id="pilihTanggalOperasiUbahPerjanjianOperasi">Pilih</button>
                        </div>
                    </div>
                </div>
                <!-- akhir tanggal operasi -->
                <input type="hidden" name="id_perjanjian" id="perjanjianUbahTanggalOperasiPerjanjianOperasi">
                <input type="hidden" name="id_dokter" id="dokterUbahTanggalOperasiPerjanjianOperasi">
                <input type="hidden" name="id_ruangan" id="ruanganUbahTanggalOperasiPerjanjianOperasi">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="tutupUbahTanggalOperasiPerjanjianOperasi" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="yakinUbahTanggalOperasiPerjanjianOperasi">Yakin ubah</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal tanggal operasi -->

<!-- mulai modal pilih tanggal -->
<div class="modal fade" id="modalUbahTanggalPerjanjianOperasi" aria-labelledby="pilihTanggalPerjanjianOperasi" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pilihTanggalPerjanjianOperasi">Pilih Tanggal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-sm-2">
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary btn-block btn-lg" name="add" id="add">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-sm-1">
                        <i class="fas fa-angle-left fa-3x" id="prevPerjanjianOperasi" style="cursor: pointer;opacity: 0.6"></i>
                    </div>
                    <div class="col-sm-8">
                        <input class="form-control bg-warning text-dark text-center fw-bold fs-4 border border-0 shadow-none" type="month" name="bulan" id="bulanPerjanjianOperasi" readonly>
                    </div>
                    <div class="col-sm-1">
                        <i class="fas fa-angle-right fa-3x" id="nextPerjanjianOperasi" style="cursor: pointer;opacity: 0.6"></i>
                    </div>
                </div>
                <div class="row" id="jadwalUbahTanggalPerjanjianOperasi"></div>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal pilih tanggal -->

<!-- mulai modal batal -->
<div class="modal fade" id="modalBatalPerjanjianOperasi" aria-labelledby="judulModalBatalPerjanjianOperasi" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalBatalPerjanjianOperasi">Konfirmasi Batal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Perjanjian ini akan dibatalkan, apa Anda yakin?
                <input type="hidden" name="id_reservasi" id="reservasiBatalPerjanjianOperasi">
                <input type="hidden" name="id_perjanjian" id="perjanjianBatalPerjanjianOperasi">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="tutupBatalPerjanjianOperasi" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-danger" id="yakinBatalPerjanjianOperasi">Yakin batal</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal batal -->

<script>
    $(document).ready(function () {
        let type;
        let ext;
        let name;

        // mulai dokter
        $('#dokterPerjanjianOperasi').select2({
            placeholder: 'Pilih dokter',
            allowClear: true
        });
        // akhir dokter

        // mulai tabel
        $('#tblPerjanjianOperasi').click(function () {
            tabel_perjanjian_operasi();
        });
        // akhir tabel

        $('#ruangan').select2({
            placeholder: 'Pilih Ruangan',
            allowClear: true,
            ajax: {
                url: '<?= base_url('admin/perjanjian/get_ruangan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });
        $('#tujuan').select2({
            placeholder: 'Pilih Tujuan',
            allowClear: true,
            ajax: {
                url: '<?= base_url('admin/penjadwalan/get_tujuan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });

        // mulai laporan per id
        $(document).on('click', '.tbl-cetak-perjanjian-operasi', function () {
            window.requestPrint({
                NAME: 'aplikasi.perjanjian.CetakPerjanjian_1',
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    ID: $(this).data('perjanjian'),
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: 'CetakPerjanjian',
                CONNECTION_NUMBER: 0,
                COPIES: 1,
                id: 'data.model.RequestReport-1'
            });
        });
        // akhir laporan per id

        // mulai laporan
        $('.laporan-perjanjian-operasi').click(function () {
            let ext = $(this).data('ext');
            let format = $(this).data('format');

            // mulai periksa extension
            if (ext === 'pdf') {
                type = 'Pdf';
                ext = 'pdf';
            } else if (ext === 'xls') {
                type = 'Excel';
                ext = 'xls';
            }
            // akhir periksa extension

            // mulai periksa format
            if (format === 'halaman') {
                name = 'aplikasi.perjanjian.LaporanPenjadwalanOperasi';
            } else if (format === 'sambung') {
                name = 'aplikasi.perjanjian.LaporanRencanaOperasi';
            }
            // akhir periksa format

            window.requestPrint({
                NAME: name,
                TYPE: type,
                EXT: ext,
                PARAMETER: {
                    TGLAWAL: $('#mulaiPerjanjianOperasi').val() !== '' ? $('#mulaiPerjanjianOperasi').val() : '1991-01-01',
                    TGLAKHIR: $('#akhirPerjanjianOperasi').val() !== '' ? $('#akhirPerjanjianOperasi').val() : '2099-12-31',
                    DOKTER: $('#dokterPerjanjianOperasi').val() !== '' ? $('#dokterPerjanjianOperasi').val() : 0,
                    TUJUAN_RS: $('#tujuan').val()!== ''? $('#tujuan').val() : '',
                    
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: 'LaporanOperasi',
                CONNECTION_NUMBER: 0,
                COPIES: 1,
                id: 'data.model.RequestReport-1'
            });
        });
        // akhir laporan

        // mulai ubah tanggal rawat
        $(document).on('click', '.ubah-tgl-rawat-perjanjian-operasi', function () {
            $('#reservasiUbahTanggalRawatPerjanjianOperasi').val($(this).data('reservasi'));
            $('#tangalUbahTanggalRawatPerjanjianOperasi').val($(this).data('tanggal'));
        });
        // akhir ubah tanggal rawat

        // mulai ubah tanggal operasi
        $(document).on('click', '.ubah-tgl-operasi-perjanjian-operasi', function () {
            $('#perjanjianUbahTanggalOperasiPerjanjianOperasi').val($(this).data('perjanjian'));
            $('#dokterUbahTanggalOperasiPerjanjianOperasi').val($(this).data('dokter'));
            $('#tanggalOperasiUbahPerjanjianOperasi').val($(this).data('tanggal'));
            $('#ruanganUbahTanggalOperasiPerjanjianOperasi').val($(this).data('id_ruangan')); 
        });
        // akhir ubah tanggal operasi

        // mulai bulan
        let tanggal = new Date();
        let bulan = ('0' + (tanggal.getMonth() + 1)).slice(-2);
        let tahun = tanggal.getFullYear();
        $('#bulanPerjanjianOperasi').val(tahun + '-' + bulan);
        // akhir bulan

        // mulai ganti bulan operasi
        // mulai bulan sebelumnya
        $('#prevPerjanjianOperasi').hover(function () {
            $(this).css('opacity', 1);
        }, function () {
            $(this).css('opacity', 0.6);
        });
        $('#prevPerjanjianOperasi').click(function () {
            if (tahun + '-' + bulan != $('#bulanPerjanjianOperasi').val()) {
                document.getElementById('bulanPerjanjianOperasi').stepDown();
                jadwal();
            } else {
                Lobibox.notify('warning', {
                    icon: 'bx bx-error',
                    msg: 'Tidak bisa mengambil data sebelum bulan ini',
                    sound: false,
                    title: 'Peringatan'
                });
            }
        });
        // akhir bulan sebelumnya

        // mulai bulan seleanjutnya
        $('#nextPerjanjianOperasi').hover(function () {
            $(this).css('opacity', 1);
        }, function () {
            $(this).css('opacity', 0.6);
        });
        $('#nextPerjanjianOperasi').click(function () {
            document.getElementById('bulanPerjanjianOperasi').stepUp();
            jadwal();
        });
        // akhir bulan selanjutnya
        // akhir ganti bulan operasi

        // mulai pilih tanggal operasi
        $('#pilihTanggalOperasiUbahPerjanjianOperasi').click(function () {
            jadwal();
        });
        // akhir pilih tanggal operasi

        // mulai fungsi jadwal
        function jadwal() {
            $.ajax({
                url: "<?= base_url('admin/Perjanjian/jadwal') ?>",
                type: 'POST',
                data: {
                    dokter: $('#dokterUbahTanggalOperasiPerjanjianOperasi').val(),
                    bulan: $('#bulanPerjanjianOperasi').val(),
                },
                dataType: 'JSON',
                success: function (data) {
                    $('#jadwalUbahTanggalPerjanjianOperasi').html(null);
                    $('#modalUbahTanggalPerjanjianOperasi').modal('show');

                    if (data.length) {
                        $.each(data, function (index, element) {
                            let bg = 'bg-info';
                            if (element.id_ruangan == 105020201 || element.id_ruangan == 105020202) {
                                bg = 'bg-success';
                            }

                            let cls = 'tanggal';
                            if (parseInt(element.jumlah) >= parseInt(element.kuota)) {
                                bg = 'bg-danger';
                                // cls = "penuh";
                            }

                            let kuota = element.kuota;
                            if (kuota == -1) {
                                kuota = '~';
                            }

                            let pagi = '';
                            let sore = '';
                            let ikon_sore = '';
                            let ikon_pagi = '';

                            if (element.kuota !== null) {
                                ikon_pagi = '<a class="link-light changeSchedule" data-status="2" data-id="' + element.id + '" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>'
                            }

                            if (element.kuota_sore !== null) {
                                sore = '<div class="card-body p-0 d-none" id="areaSore' + element.id + '">' +
                                    '<div class="d-flex justify-content-between">' +
                                    '<h4><span class="badge text-bg-dark rounded-start-0 rounded-end-pill"><i class="fas fa-clock fa-sm"></i> ' + element.waktu_sore + '</span></h4>' +
                                    '<h6 class="mt-2">' + ikon_pagi + '</h6>' +
                                    '<h4 class="card-title text-right"><span class="badge text-bg-dark rounded-start-pill rounded-end-0">' + element.jumlah_sore + '/' + element.kuota_sore + '</span></h4>' +
                                    '</div>' +
                                    '<div class="card-body p-0 m-0 ' + cls + ' " tanggal="' + element.tanggal + '" ruangan="' + element.ruangan + '" idr="' + element.id_ruangan + '" sore="1" style="cursor: pointer;">' +
                                    '<p class="card-text text-center text-warning fw-bold fs-1"><u>' + element.day + '</u></p>' +
                                    '</div>' +
                                    '</div>';
                                ikon_sore = '<a class="link-light changeSchedule" data-status="1" data-id="' + element.id + '" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>';
                            }

                            if (element.kuota !== null) {
                                pagi = '<div class="card-body p-0" id="areaPagi' + element.id + '">' +
                                    '<div class="d-flex justify-content-between">' +
                                    '<h4><span class="badge text-bg-primary rounded-start-0 rounded-end-pill"><i class="fas fa-clock" style="font-size: 14px;"></i> ' + element.waktu + '</span></h4>' +
                                    '<h6 class="mt-2">' + ikon_sore + '</h6>' +
                                    '<h4 class="card-title text-right"><span class="badge text-bg-warning rounded-start-pill rounded-end-0">' + element.jumlah + '/' + kuota + '</span></h4>' +
                                    '</div>' +
                                    '<div class="card-body p-0 m-0 ' + cls + ' " tanggal="' + element.tanggal + '" ruangan="' + element.ruangan + '" idr="' + element.id_ruangan + '" sore="0" style="cursor: pointer;">' +
                                    '<p class="card-text text-center text-warning fw-bold fs-1"><u>' + element.day + '</u></p>' +
                                    '</div>' +
                                    '</div>';
                            }

                            if (element.kuota !== null || element.kuota_sore !== null) {
                                $('#jadwalUbahTanggalPerjanjianOperasi').append(
                                    '<div class="col-3 mb-3">' +
                                    '<div class="card text-white h-100" style="background-color:' + element.color + '">' +
                                    '<div class="card-header text-center text-warning" style="font-size: 12px;">' + element.ruangan + '</div>' +
                                    pagi +
                                    sore +
                                    '<div class="card-footer text-center text-warning">' + element.hari + '</div>' +
                                    '</div>');
                                if (element.kuota !== null) {
                                    if (element.jumlah == element.kuota && element.kuota_sore !== null) {
                                        $('#areaPagi' + element.id).addClass('d-none');
                                        $('#areaSore' + element.id).removeClass('d-none');
                                    } else {
                                        $('#areaPagi' + element.id).removeClass('d-none');
                                        $('#areaSore' + element.id).addClass('d-none');
                                    }
                                } else {
                                    $('#areaPagi' + element.id).addClass('d-none');
                                    $('#areaSore' + element.id).removeClass('d-none');
                                }
                            }
                        });

                        $('.changeSchedule').click(function () {
                            let id = $(this).attr('data-id');
                            let status = $(this).attr('data-status');
                            if (status == 1) {
                                $('#areaPagi' + id).addClass('d-none');
                                $('#areaSore' + id).removeClass('d-none');
                            } else {
                                $('#areaPagi' + id).removeClass('d-none');
                                $('#areaSore' + id).addClass('d-none');
                            }
                        });
                    } else {
                        $('#jadwalUbahTanggalPerjanjianOperasi').append(
                            "<div class='col'>" +
                            "<div class='alert alert-warning' role='alert'>" +
                            "Jadwal dokter belum tersedia" +
                            "</div>" +
                            "</div>"
                        );
                    }
                },
                error: function () {
                    Lobibox.error('Ada Kesalahan!');
                }
            });
        }
        // akhir fungsi jadwal

        // mulai klik tanggal
        $(document).on('click', '.tanggal', function () {
            let tanggal = $(this).attr('tanggal');
            let ruangan = $(this).attr('ruangan');
            let idr = $(this).attr('idr');
            let sore = $(this).attr('sore');

            $('#btnSelesai').attr('disabled', false);
            if (idr == '105020704' || idr == '105020705' || idr == '105060101' || idr == '105120101' || idr == '105110101' || idr == '105090101' || idr == '105020708') {
                $('.rencana').css('display', 'block');
            }
            if (idr == '105090101' || idr == '105090104') {
                $('.operasi').css('display', 'block');
            }

            $('#tanggalOperasiUbahPerjanjianOperasi').val(tanggal);
            $('#ruanganUbahTanggalOperasiPerjanjianOperasi').val(idr);           
            $('#modalUbahTanggalPerjanjianOperasi').modal('hide');
        });
        // akhir klik tanggal

        // mulai fungsi tabel
        function tabel_perjanjian_operasi() {
            $.ajax({
                method: 'POST',
                url: "<?= base_url('admin/Perjanjian/tabel') ?>",
                data: {
                    mulai: $('#mulaiPerjanjianOperasi').val() !== '' ? $('#mulaiPerjanjianOperasi').val() : '1991-01-01',
                    akhir: $('#akhirPerjanjianOperasi').val() !== '' ? $('#akhirPerjanjianOperasi').val() : '2099-12-31',
                    id_dokter: $('#dokterPerjanjianOperasi').val() !== '' ? $('#dokterPerjanjianOperasi').val() : null,
                    id_ruangan: $('#ruangan').val()!== ''? $('#ruangan').val() : null,
                    tujuan : $('#tujuan').val()!== ''? $('#tujuan').val() : null
                },
                success: function (data) {
                    $('#tampilTabelPerjanjianOperasi').removeClass('d-none').html(data);
                }
            });
        }
        // akhir fungsi tabel

        // mulai yakin ubah tanggal rawat
        $('#yakinUbahTanggalRawatPerjanjianOperasi').click(function () {
            $.ajax({
                url: "<?= base_url('admin/Perjanjian/ubah_tanggal_rawat') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id_reservasi: $('#reservasiUbahTanggalRawatPerjanjianOperasi').val(),
                    tanggal: $('#tangalUbahTanggalRawatPerjanjianOperasi').val()
                },
                success: function (data) {
                    if (data.status === 200) {
                        $('#modalTanggalRawatPerjanjianOperasi').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Perjanjian mengubah tanggal rawat',
                            sound: false,
                            title: 'Berhasil'
                        });
                        tabel_perjanjian_operasi();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Gagal mengubah',
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir yakin ubah tanggal rawat

        // mulai yakin ubah tanggal operasi
        $('#yakinUbahTanggalOperasiPerjanjianOperasi').click(function () {
            $.ajax({
                url: "<?= base_url('admin/Perjanjian/ubah_tanggal_operasi') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    idPerjanjian: $('#perjanjianUbahTanggalOperasiPerjanjianOperasi').val(),
                    tanggal: $('#tanggalOperasiUbahPerjanjianOperasi').val(),
                    ruangan: $('#ruanganUbahTanggalOperasiPerjanjianOperasi').val()
                },
                success: function (data) {
                    if (data.status === 200) {
                        $('#modalTanggalOperasiPerjanjianOperasi').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Perjanjian mengubah tanggal operasi',
                            sound: false,
                            title: 'Berhasil'
                        });
                        tabel_perjanjian_operasi();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Gagal mengubah',
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir yakin ubah tanggal operasi

        // mulai batal
        $(document).on('click', '.tbl-batal-perjanjian-operasi', function () {
            let id_reservasi = $(this).data('reservasi');
            let id_perjanjian = $(this).data('perjanjian');
            $('#reservasiBatalPerjanjianOperasi').val(id_reservasi);
            $('#perjanjianBatalPerjanjianOperasi').val(id_perjanjian);
        });
        // akhir batal

        // mulai yakin batal
        $('#yakinBatalPerjanjianOperasi').click(function () {
            $.ajax({
                url: "<?= base_url('admin/Perjanjian/batal') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id: $('#reservasiBatalPerjanjianOperasi').val(),
                    idPerjanjian: $('#perjanjianBatalPerjanjianOperasi').val(),
                },
                success: function (data) {
                    if (data.status === 200) {
                        $('#modalBatalPerjanjianOperasi').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Perjanjian dibatalkan',
                            sound: false,
                            title: 'Berhasil'
                        });
                        tabel_perjanjian_operasi();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Gagal membatalkan',
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir yakin batal
    });
</script>