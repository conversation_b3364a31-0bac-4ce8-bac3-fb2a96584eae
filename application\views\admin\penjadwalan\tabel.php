<table class="table table-striped table-bordered table-hover w-100" id="tabelPenjadwalan">
    <thead>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col">W<PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col"><PERSON><PERSON> (Umur)</th>
            <th class="text-center" scope="col">Ruang Kelas/Rawat</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <?php if ($jenis != 'history'): ?>
                <th class="text-center" scope="col">Aksi</th>
            <?php endif ?>
        </tr>
    </thead>
    <tbody></tbody>
    <tfoot>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Tanggal Tindakan</th>
            <th class="text-center" scope="col">Kamar Operasi</th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col">Waktu Dibuat</th>
            <th class="text-center" scope="col">Nama Pasien</th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col">Tanggal Lahir (Umur)</th>
            <th class="text-center" scope="col">Ruang Kelas/Rawat</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col">Dokter Anestesi</th>
            <th class="text-center" scope="col">Catatan Khusus</th>
            <?php if ($jenis != 'history'): ?>
                <th class="text-center" scope="col">Aksi</th>
            <?php endif ?>
        </tr>
    </tfoot>
</table>

<script>
    $(document).ready(function () {
        // mulai tabel
        if ($.fn.DataTable.isDataTable('#tabelPenjadwalan')) {
            $('#tabelPenjadwalan').DataTable().destroy();
        }
        setTimeout(function () {
            $('#tabelPenjadwalan').DataTable({
                autoWidth: true,
                order: [
                    [1, 'desc'],
                    [2, 'asc'],
                    [4, 'asc']
                ],
                language: {
                    url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
                },

                clear: true,
                destroy: true,
                processing: true,
                serverSide: true,
                iDisplayLength: 25,
                search: {
                    regex: true
                },

                // mulai ambil data
                ajax: {
                    url: "<?= base_url('admin/Penjadwalan/isi_tabel') ?>",
                    type: 'POST',
                    data: {
                        jenis: '<?= $jenis ?? null ?>',
                        mulai: '<?= $mulai ?? null ?>',
                        akhir: '<?= $akhir ?? null ?>',
                        hari: '<?= $hari ?? null ?>',
                        id_ruangan: '<?= $id_ruangan?? null?>',
                        tujuanOperasi: '<?= $tujuanOperasi?? null?>',

                    }
                },
                // akhir ambil data

                // mulai pendefinisian kolom
                columnDefs: [{
                    targets: <?= $target_order ?>,
                    orderable: false,
                }],
                // akhir pendefinisian kolom
            });
        }, 10);
        // akhir tabel
    });
</script>