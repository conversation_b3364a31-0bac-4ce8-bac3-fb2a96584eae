<table class="table table-striped table-bordered table-hover w-100" id="tabelPasienDaftarTunggu">
    <thead>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">Tanggal <PERSON>hir (Umur)</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Tanggal Daftar</th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON>ata<PERSON></th>
            <th class="text-center" scope="col">Status</th>
        </tr>
    </thead>
    <tbody></tbody>
    <tfoot>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col">Nama Pasien</th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col">Jenkel</th>
            <th class="text-center" scope="col">Tanggal Lahir (Umur)</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Ruang Operasi</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Tanggal Daftar</th>
            <th class="text-center" scope="col">Sifat Operasi</th>
            <th class="text-center" scope="col">Dokter Bedah Lain</th>
            <th class="text-center" scope="col">Catatan Khusus</th>
            <th class="text-center" scope="col">Status</th>
        </tr>
    </tfoot>
</table>

<script>
    $(document).ready(function() {
        // mulai tabel
        $('#tabelPasienDaftarTunggu').DataTable({
            autoWidth: true,
            order: [
                [11, 'asc'],
                [7, 'desc']
            ],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('operasi/Daftar_tunggu/isi_tabel') ?>",
                type: 'POST',
                data: function(d) {
                    d.PDOKTER = '<?= $PDOKTER ?? null?>';
                    d.PTUJUAN_RS = '<?= $PTUJUAN_RS ?? null?>';
                    return d;
                }
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [0],
                orderable: false,
            }],
            // akhir pendefinisian kolom
        });
        // akhir tabel
    });
</script>