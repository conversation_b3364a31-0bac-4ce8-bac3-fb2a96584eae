<!DOCTYPE html>
<!--
========== CETAK GAMBAR STANDALONE - DUAL MODE RESPONSIF ==========
File ini mendukung 2 mode berbeda dengan style yang responsif:

1. PREVIEW MODE (cetakGambarModal & cetakGambarTab):
   - Style responsif dan simetris untuk preview
   - table-layout: fixed untuk kolom yang konsisten
   - Font size adaptif: 11px (desktop) → 8px (mobile)
   - Column widths tetap dengan persentase yang proporsional
   - Overflow hidden untuk mencegah konten keluar dari kolom

2. DOWNLOAD MODE (btnSaveImages JPG & ZIP):
   - Menggunakan style dari cetak_gambar_standalone_save.php
   - Font size seragam 32px untuk semua kolom tabel
   - Diterapkan via JavaScript dengan class 'download-mode'
   - Optimized untuk output gambar high-resolution

PERBAIKAN RESPONSIF:
- Fixed column widths untuk tabel simetris
- Responsive font sizes untuk berbagai ukuran layar
- Overflow control untuk mencegah konten keluar kolom
- Modal optimizations dengan padding dan font yang lebih kecil
========================================================
-->
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cetak Gambar - Daftar Operasi Elektif</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- html2canvas -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- JSZip untuk membuat ZIP file -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <!-- FileSaver untuk download -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>

    <style>
        /* Optimized styles for WhatsApp sharing - JPG format */
        body {
            font-family: 'Roboto', 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        .print-page {
            width: 1200px; /* Wider for better content visibility */
            min-height: auto; /* Dynamic height based on content */
            margin: 20px auto;
            padding: 20px; /* Normal padding for preview */
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1); /* Shadow for preview */
            page-break-after: always;
            overflow: visible; /* Allow content to flow naturally */
            position: relative;
        }
        
        .print-page:last-child {
            page-break-after: auto;
        }
        
        /* Typography for preview (readable size) */
        .header-title {
            text-align: center;
            font-size: 16px; /* Slightly larger for better readability */
            font-weight: bold;
            margin-bottom: 8px;
            color: #1a1a1a;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .header-subtitle {
            text-align: center;
            font-size: 14px; /* Slightly larger for better readability */
            margin-bottom: 12px;
            color: #333;
            font-style: italic;
        }

        .tujuan-rs {
            font-size: 14px; /* Slightly larger for better readability */
            margin-bottom: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        /* ========== STYLE PREVIEW (MODAL & TAB) - FIXED RESPONSIF ========== */
        /* Table styling for preview - SIMETRIS DAN RESPONSIF */
        .table-operasi {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px; /* Font normal untuk preview */
            line-height: 1.3;
            margin-bottom: 18px;
            table-layout: fixed; /* FIXED untuk kolom simetris dan responsif */
            word-break: break-word;
        }

        .table-operasi th,
        .table-operasi td {
            border: 1px solid #000;
            padding: 4px 3px; /* Padding lebih kecil untuk preview */
            vertical-align: top;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal; /* Allow text wrapping */
            overflow: hidden; /* Prevent content overflow */
            text-overflow: ellipsis; /* Add ellipsis for long text */
            height: auto;
            max-height: none;
        }

        /* Kolom TINDAKAN dan DIAGNOSA - RATA KIRI */
        .table-operasi td:nth-child(8), /* TINDAKAN */
        .table-operasi td:nth-child(7)  /* DIAGNOSA */ {
            text-align: left !important;
            padding-left: 6px;
        }

        /* Memastikan rowspan cells sejajar dengan baik */
        .table-operasi td[rowspan] {
            vertical-align: middle;
            text-align: center;
        }

        .table-operasi td[rowspan].text-left {
            text-align: left;
        }

        /* PERBAIKAN: Kolom yang perlu lebih lebar untuk subreport - RESPONSIF */
        .table-operasi th:nth-child(8), /* TINDAKAN */
        .table-operasi td:nth-child(8) {
            min-width: 180px; /* PERBAIKAN: Lebih lebar untuk tindakan subreport */
            width: auto; /* Auto width sesuai konten */
        }

        .table-operasi th:nth-child(9), /* OPERATOR */
        .table-operasi td:nth-child(9) {
            min-width: 150px; /* PERBAIKAN: Lebih lebar untuk operator subreport */
            width: auto; /* Auto width sesuai konten */
        }

        .table-operasi th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
            font-size: 11px; /* Consistent with table content */
            color: #2c3e50;
        }

        .group-header-date {
            background-color: #FFCCCC !important;
            font-weight: bold;
            font-size: 12px; /* Slightly larger for prominence */
            padding: 8px 6px; /* More padding for date headers */
            text-align: left;
            color: #8B0000;
        }

        .group-header-room {
            background-color: #CCCCCC !important;
            font-weight: bold;
            font-size: 11px; /* Consistent with table */
            text-align: center;
            padding: 6px 4px;
            color: #2c3e50;
        }
        
        .text-center { text-align: center; }
        .text-left { text-align: left; }

        /* PERBAIKAN: Styling untuk subreport data - RAPI DAN RESPONSIF */
        .subreport-item {
            margin-bottom: 2px;
            line-height: 1.1;
        }

        .subreport-separator {
            border-bottom: 1px dotted #ccc;
            margin: 1px 0;
        }

        /* ========== SUBREPORT CELLS STYLING - RESPONSIF ========== */
        .subreport-cell {
            font-size: 9px !important; /* Smaller font for subreport */
            line-height: 1.2 !important;
            padding: 2px 3px !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            white-space: normal !important;
            vertical-align: top !important;
            border: 1px solid #000 !important;
            overflow: hidden !important; /* Prevent overflow */
            text-overflow: ellipsis !important;
            text-align: left !important;
        }

        /* ========== FIXED COLUMN WIDTHS UNTUK PREVIEW SIMETRIS ========== */
        /* Kolom dengan lebar tetap untuk preview yang simetris */
        .table-operasi th:nth-child(1), .table-operasi td:nth-child(1) { width: 3%; }   /* NO */
        .table-operasi th:nth-child(2), .table-operasi td:nth-child(2) { width: 4.5%; } /* JAM */
        .table-operasi th:nth-child(3), .table-operasi td:nth-child(3) { width: 11%; }  /* NAMA PASIEN */
        .table-operasi th:nth-child(4), .table-operasi td:nth-child(4) { width: 4.5%; } /* MR */
        .table-operasi th:nth-child(5), .table-operasi td:nth-child(5) { width: 2.5%; } /* UMUR */
        .table-operasi th:nth-child(6), .table-operasi td:nth-child(6) { width: 7.5%; } /* KELAS */
        .table-operasi th:nth-child(7), .table-operasi td:nth-child(7) { width: 18%; }  /* DIAGNOSA */
        .table-operasi th:nth-child(8), .table-operasi td:nth-child(8) { width: 13%; }  /* TINDAKAN */
        .table-operasi th:nth-child(9), .table-operasi td:nth-child(9) { width: 13%; }  /* OPERATOR */
        .table-operasi th:nth-child(10), .table-operasi td:nth-child(10) { width: 13%; } /* DOKTER ANESTESI */
        .table-operasi th:nth-child(11), .table-operasi td:nth-child(11) { width: 6%; }  /* JENIS ANESTESI */
        .table-operasi th:nth-child(12), .table-operasi td:nth-child(12) { width: 4%; }  /* PTGS */

        /* ========== COLUMN BEHAVIOR UNTUK PREVIEW RESPONSIF ========== */
        /* Compact columns - minimal height, centered content */
        .table-operasi td:nth-child(1), /* NO: 3% */
        .table-operasi td:nth-child(2), /* JAM: 4.5% */
        .table-operasi td:nth-child(4), /* MR: 4.5% */
        .table-operasi td:nth-child(5), /* UMUR: 2.5% */
        .table-operasi td:nth-child(6), /* KELAS: 7.5% */
        .table-operasi td:nth-child(11), /* JENIS ANES: 6% */
        .table-operasi td:nth-child(12) /* PTGS: 4% */ {
            text-align: center;
            line-height: 1.2;
            font-size: 10px; /* Slightly smaller for compact columns */
        }

        /* Medium columns - controlled wrapping */
        .table-operasi td:nth-child(3), /* NAMA PASIEN: 11% */
        .table-operasi td:nth-child(10) /* DOKTER ANESTESI: 13% */ {
            text-align: left;
            line-height: 1.2;
            font-size: 10px;
            padding-left: 3px;
        }

        /* Wide columns - optimized for medical content */
        .table-operasi td:nth-child(7), /* DIAGNOSA: 18% */
        .table-operasi td:nth-child(8), /* TINDAKAN: 13% */
        .table-operasi td:nth-child(9) /* OPERATOR: 13% */ {
            text-align: left;
            line-height: 1.2;
            font-size: 10px;
            padding-left: 3px;
        }

        .footer-notes {
            font-size: 11px; /* Larger for better readability */
            margin-top: 20px;
            line-height: 1.4;
            color: #555;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        
        .control-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Preview stays at normal size - scaling only applied to download clone */

        /* Modal-specific optimizations */
        .modal-body .print-page {
            width: 100%; /* Full modal width utilization */
            max-width: none; /* Remove max-width restriction */
            transform: none; /* No scaling in modal */
            margin: 10px 0; /* Reduced margin for modal */
            box-shadow: none; /* Remove shadow in modal */
        }

        /* Responsive adjustments for standalone page only */
        body:not(.modal-open) .print-page {
            /* These rules only apply when NOT in modal */
        }

        /* ========== RESPONSIVE DESIGN UNTUK PREVIEW ========== */
        @media (max-width: 1400px) {
            body:not(.modal-open) .print-page {
                width: 95%;
                max-width: 1200px;
                transform: scale(0.95); /* Less aggressive scaling */
                transform-origin: top center;
            }

            .table-operasi {
                font-size: 10px; /* Smaller font for smaller screens */
            }

            .table-operasi th,
            .table-operasi td {
                padding: 3px 2px; /* Smaller padding */
            }
        }

        @media (max-width: 1200px) {
            body:not(.modal-open) .print-page {
                width: 95%;
                max-width: 1000px;
                transform: scale(0.85); /* Moderate scaling */
                transform-origin: top center;
            }

            .table-operasi {
                font-size: 9px; /* Even smaller font */
            }
        }

        @media (max-width: 900px) {
            body:not(.modal-open) .print-page {
                width: 95%;
                max-width: 800px;
                transform: scale(0.75); /* Smaller scale for mobile */
                transform-origin: top center;
            }

            .table-operasi {
                font-size: 8px; /* Mobile font size */
            }

            .table-operasi th,
            .table-operasi td {
                padding: 2px 1px; /* Minimal padding for mobile */
            }
        }
        
        @media print {
            .control-buttons {
                display: none;
            }
            .print-page {
                box-shadow: none;
                margin: 0;
                page-break-after: always;
            }
            body {
                background-color: white;
            }
        }
        
        .loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2000;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
        }
        
        .btn-group-custom {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .info-panel {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 12px;
        }

        /* ========== MODAL TABLE OPTIMIZATIONS - FIXED RESPONSIF ========== */
        .modal-body .table-operasi {
            font-size: 10px; /* Smaller font for modal */
            margin-bottom: 15px;
            table-layout: fixed; /* FIXED untuk kolom simetris di modal */
            width: 100%; /* Full width dalam modal */
            word-break: break-word;
        }

        .modal-body .table-operasi th,
        .modal-body .table-operasi td {
            padding: 3px 2px; /* Smaller padding for modal */
            border: 1px solid #000;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
            overflow: hidden; /* Prevent overflow in modal */
            text-overflow: ellipsis;
            height: auto;
            max-height: none;
        }

        /* Modal column widths - same as main table */
        .modal-body .table-operasi th:nth-child(1), .modal-body .table-operasi td:nth-child(1) { width: 3%; }
        .modal-body .table-operasi th:nth-child(2), .modal-body .table-operasi td:nth-child(2) { width: 4.5%; }
        .modal-body .table-operasi th:nth-child(3), .modal-body .table-operasi td:nth-child(3) { width: 11%; }
        .modal-body .table-operasi th:nth-child(4), .modal-body .table-operasi td:nth-child(4) { width: 4.5%; }
        .modal-body .table-operasi th:nth-child(5), .modal-body .table-operasi td:nth-child(5) { width: 2.5%; }
        .modal-body .table-operasi th:nth-child(6), .modal-body .table-operasi td:nth-child(6) { width: 7.5%; }
        .modal-body .table-operasi th:nth-child(7), .modal-body .table-operasi td:nth-child(7) { width: 18%; }
        .modal-body .table-operasi th:nth-child(8), .modal-body .table-operasi td:nth-child(8) { width: 13%; }
        .modal-body .table-operasi th:nth-child(9), .modal-body .table-operasi td:nth-child(9) { width: 13%; }
        .modal-body .table-operasi th:nth-child(10), .modal-body .table-operasi td:nth-child(10) { width: 13%; }
        .modal-body .table-operasi th:nth-child(11), .modal-body .table-operasi td:nth-child(11) { width: 6%; }
        .modal-body .table-operasi th:nth-child(12), .modal-body .table-operasi td:nth-child(12) { width: 4%; }

        /* Modal rowspan styling */
        .modal-body .table-operasi td[rowspan] {
            vertical-align: middle;
            text-align: center;
        }

        .modal-body .table-operasi td[rowspan].text-left {
            text-align: left;
        }

        /* PERBAIKAN: Styling khusus untuk subreport cell di modal */
        .modal-body .subreport-cell {
            font-size: 10px !important;
            line-height: 1.3 !important;
            padding: 4px 5px !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            white-space: normal !important;
            vertical-align: top !important;
            border: 1px solid #000 !important;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left !important;
        }

        /* Khusus untuk kolom TINDAKAN subreport di modal */
        .modal-body .table-operasi td.subreport-cell:nth-child(8) {
            max-width: 150px !important;
            min-width: 120px !important;
        }

        /* Khusus untuk kolom OPERATOR subreport di modal */
        .modal-body .table-operasi td.subreport-cell:nth-child(9) {
            max-width: 130px !important;
            min-width: 100px !important;
        }

        /* Modal container diperlebar mepet tepi */
        .modal-body {
            padding: 10px 5px; /* Minimal padding untuk memaksimalkan lebar */
        }

        /* Print container dalam modal diperlebar */
        .modal-body #printContainer {
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        .modal-body .header-title {
            font-size: 14px; /* Smaller header for modal */
            margin-bottom: 5px;
        }

        .modal-body .header-subtitle {
            font-size: 12px; /* Smaller subtitle for modal */
            margin-bottom: 6px;
        }

        .modal-body .tujuan-rs {
            font-size: 12px; /* Smaller tujuan for modal */
            margin-bottom: 10px;
        }

        .modal-body .footer-notes {
            font-size: 9px; /* Smaller footer for modal */
            margin-top: 12px;
            padding: 6px;
        }

        /* ========== STYLE PREVIEW - DARI FILE PRIVIEW.PHP ========== */
        /* Style ini untuk preview (modal & tab) - font normal 11px */
        /* Download akan menggunakan style berbeda via JavaScript */

        /* ========== STYLE DOWNLOAD - DARI FILE SAVE.PHP ========== */
        /* Style ini HANYA diterapkan via JavaScript saat download */
        /* TIDAK untuk preview, hanya untuk clone download */
        .download-mode .table-operasi td {
            font-size: 32px !important; /* Font size seragam untuk semua kolom */
            font-family: Arial, sans-serif !important;
            font-weight: normal !important;
            line-height: 1.3 !important;
            padding: 8px 6px !important;
            vertical-align: top !important;
            white-space: normal !important;
            overflow-wrap: break-word !important;
            word-break: break-word !important;
        }

        /* ========== SET UKURAN KERTAS CETAK ========== */
        @media print {
            body {
                margin: 0;
                padding: 0;
                width: 3508px; /* A4 300DPI width */
                font-family: Arial, sans-serif;
            }
        }
    </style>
</head>
<body>
    <!-- Control Buttons -->
    <div class="control-buttons">
        <div class="info-panel">
            <strong>Total Data:</strong> <?= isset($data_operasi_by_date) ? array_sum(array_map('count', $data_operasi_by_date)) : 0 ?> operasi<br>
            <strong>Periode:</strong> <?= date('d/m/Y', strtotime($tglawal)) ?> - <?= date('d/m/Y', strtotime($tglakhir)) ?><br>
            <strong>Halaman:</strong> <span id="totalPages">-</span> halaman (1 gambar per tanggal)
        </div>
        
        <div class="btn-group-custom">
            <button id="btnSaveImages" class="btn btn-primary btn-sm">
                <i class="bi bi-download"></i> Download Images (ZIP)
            </button>
            <button id="btnPrint" class="btn btn-secondary btn-sm">
                <i class="bi bi-printer"></i> Print
            </button>
            <button id="btnClose" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-x-circle"></i> Tutup
            </button>
        </div>
    </div>
    
    <!-- Loading Indicator -->
    <div id="loading" class="loading">
        <div class="text-center">
            <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2" id="loadingText">Sedang membuat gambar...</div>
        </div>
    </div>
    
    <!-- Print Pages Container -->
    <div id="printContainer">
        <?php
        // Function to convert date to Indonesian format
        function formatTanggalIndonesia($date) {
            $hari = array(
                'Sunday' => 'Minggu',
                'Monday' => 'Senin', 
                'Tuesday' => 'Selasa',
                'Wednesday' => 'Rabu',
                'Thursday' => 'Kamis',
                'Friday' => 'Jumat',
                'Saturday' => 'Sabtu'
            );
            
            $bulan = array(
                'January' => 'Januari',
                'February' => 'Februari', 
                'March' => 'Maret',
                'April' => 'April',
                'May' => 'Mei',
                'June' => 'Juni',
                'July' => 'Juli',
                'August' => 'Agustus',
                'September' => 'September',
                'October' => 'Oktober',
                'November' => 'November',
                'December' => 'Desember'
            );
            
            $day_name = $hari[date('l', strtotime($date))];
            $day = date('d', strtotime($date));
            $month_name = $bulan[date('F', strtotime($date))];
            $year = date('Y', strtotime($date));
            
            return $day_name . ', ' . $day . ' ' . $month_name . ' ' . $year;
        }
        
        // Check if there's data
        if (empty($data_operasi_by_date)) {
            // Show empty data message
            echo '<div class="print-page" id="page-1">';
            ?>
            <!-- Page Header -->
            <div class="header-title">
                DAFTAR OPERASI ELEKTIF INSTALASI BEDAH SENTRAL RS KANKER DHARMAIS
            </div>
            <div class="header-subtitle">
                (Persiapan Operasi Lengkap: SLO+Konsul - konsul + Hasil PCR)
            </div>
            <div class="tujuan-rs">
                Tujuan Masuk RS : <?= $penjamin_operasi ?>
            </div>
            
            <table class="table-operasi">
                <thead>
                    <tr>
                            <!-- ========== SET TABEL CETAK - UKURAN KOLOM OPTIMIZED ========== -->
                            <th style="width: 3%;">NO</th>           
                            <th style="width: 4.5%;">JAM</th>        
                            <th style="width: 11%;">NAMA PASIEN</th> 
                            <th style="width: 4.5%;">MR</th>         
                            <th style="width: 2.5%;">UMUR</th>       
                            <th style="width: 7.5%;">KELAS</th>      
                            <th style="width: 18%;">DIAGNOSA</th>    
                            <th style="width: 13%;">TINDAKAN</th>    
                            <th style="width: 13%;">OPERATOR</th>    
                            <th style="width: 13%;">DOKTER ANESTESI</th> 
                            <th style="width: 6%;">JENIS ANESTESI</th>   
                            <th style="width: 4%;">PTGS</th>         
                        </tr>
                </thead>
                <tbody>
                    <tr><td colspan="12" class="text-center" style="padding: 20px;">Tidak ada data operasi untuk periode yang dipilih</td></tr>
                </tbody>
            </table>
            
            <!-- Footer -->
            <div class="footer-notes">
                <strong>NB :</strong><br>
                1. Jam operasi sewaktu-waktu dapat berubah<br>
                2. Pasien dengan jadwal urutan jam pertama dan jam kedua dari masing-masing kamar operasi dipuasakan sejak 02.00 dini hari, Sedangkan pasien urutan ke-3 dipuasakan sejak pukul 06.00 pagi hari<br>
                3. Apabila pasien sudah masuk rawat inap, mohon lapor ke dokter/operator<br>
                4. MKR (Menunggu Konfirmasi Ruangan )
            </div>
            <?php
            echo '</div>';
        } else {
            // Generate one page per date
            $page_number = 1;
            foreach ($data_operasi_by_date as $date => $data_for_date) {
                echo '<div class="print-page" id="page-' . $page_number . '">';
                ?>
                <!-- Page Header -->
                <div class="header-title">
                    DAFTAR OPERASI ELEKTIF INSTALASI BEDAH SENTRAL RS KANKER DHARMAIS
                </div>
                <div class="header-subtitle">
                    (Persiapan Operasi Lengkap: SLO+Konsul - konsul + Hasil PCR)
                </div>
                <div class="tujuan-rs">
                    Tujuan Masuk RS : <?= $penjamin_operasi ?>
                </div>

                <table class="table-operasi">
                    <thead>
                        <tr>
                            <!-- ========== SET TABEL CETAK - UKURAN KOLOM OPTIMIZED ========== -->
                            <th style="width: 3%;">NO</th>           
                            <th style="width: 4.5%;">JAM</th>        
                            <th style="width: 11%;">NAMA PASIEN</th> 
                            <th style="width: 4.5%;">MR</th>         
                            <th style="width: 2.5%;">UMUR</th>       
                            <th style="width: 7.5%;">KELAS</th>      
                            <th style="width: 18%;">DIAGNOSA</th>    
                            <th style="width: 13%;">TINDAKAN</th>    
                            <th style="width: 13%;">OPERATOR</th>    
                            <th style="width: 13%;">DOKTER ANESTESI</th> 
                            <th style="width: 6%;">JENIS ANESTESI</th>   
                            <th style="width: 4%;">PTGS</th>         
                        </tr>
                    </thead>
                    <tbody>
                <?php

                // Group by room for this date
                $rooms_for_date = [];
                foreach ($data_for_date as $item) {
                    $room = $item->kamar_operasi_full;
                    $rooms_for_date[$room][] = $item;
                }

                // Date group header
                echo '<tr><td colspan="12" class="group-header-date" style="text-align: left; padding-left: 10px;">';
                echo formatTanggalIndonesia($date);
                echo '</td></tr>';

                // Loop through rooms for this date
                foreach ($rooms_for_date as $room => $items) {
                    // Room group header
                    echo '<tr><td colspan="12" class="group-header-room">' . $room . '</td></tr>';

                    $row_number = 1;
                    foreach ($items as $item) {
                        // Data row dengan subreport integration - setiap subreport item dalam baris terpisah
                        if (!empty($item->subreport_data) && is_array($item->subreport_data) && count($item->subreport_data) > 0) {
                            // Tampilkan setiap subreport item dalam baris terpisah
                            foreach ($item->subreport_data as $sub_index => $sub_item) {
                                echo '<tr>';

                                // Kolom NO - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . $row_number . '</td>';
                                }

                                // Kolom JAM - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->jam_operasi ?: '') . '</td>';
                                }

                                // Kolom NAMA PASIEN - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-left" style="padding-left: 5px;" rowspan="' . count($item->subreport_data) . '">' . ($item->nama_pasien ?: '') . '</td>';
                                }

                                // Kolom MR - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->norm ?: '') . '</td>';
                                }

                                // Kolom UMUR - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->umur ?: '') . '</td>';
                                }

                                // Kolom KELAS - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->kamar_rawat_real ?: 'MKR') . '</td>';
                                }

                                // Kolom DIAGNOSA - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-left" style="padding-left: 3px;" rowspan="' . count($item->subreport_data) . '">' . ($item->diagnosis ?: '') . '</td>';
                                }

                                // Kolom TINDAKAN - setiap subreport item dalam baris terpisah dengan border (SESUAI GAMBAR)
                                echo '<td class="text-left subreport-cell" style="padding: 4px 6px; word-wrap: break-word; overflow-wrap: break-word; max-width: 200px; white-space: normal; vertical-align: top; line-height: 1.3; border: 1px solid #000;">' . ($sub_item->tindakan ?: '') . '</td>';

                                // Kolom OPERATOR - setiap subreport item dalam baris terpisah dengan border (SESUAI GAMBAR)
                                echo '<td class="text-left subreport-cell" style="padding: 4px 6px; word-wrap: break-word; overflow-wrap: break-word; max-width: 180px; white-space: normal; vertical-align: top; line-height: 1.3; border: 1px solid #000;">' . ($sub_item->dokter_operator ?: '') . '</td>';

                                // Kolom DOKTER ANESTESI - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-left" style="padding-left: 3px;" rowspan="' . count($item->subreport_data) . '">' . ($item->dokter_anestesi ?: '') . '</td>';
                                }

                                // Kolom JENIS ANESTESI - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '">' . ($item->nama_anestesi ?: '') . '</td>';
                                }

                                // Kolom PTGS - hanya tampilkan di baris pertama
                                if ($sub_index === 0) {
                                    echo '<td class="text-center" rowspan="' . count($item->subreport_data) . '"></td>';
                                }

                                echo '</tr>';
                            }
                        } else {
                            // Fallback: tampilkan data utama jika subreport kosong
                            echo '<tr>';
                            echo '<td class="text-center">' . $row_number . '</td>';
                            echo '<td class="text-center">' . ($item->jam_operasi ?: '') . '</td>';
                            echo '<td class="text-left" style="padding-left: 5px;">' . ($item->nama_pasien ?: '') . '</td>';
                            echo '<td class="text-center">' . ($item->norm ?: '') . '</td>';
                            echo '<td class="text-center">' . ($item->umur ?: '') . '</td>';
                            echo '<td class="text-center">' . ($item->kamar_rawat_real ?: 'MKR') . '</td>';
                            echo '<td class="text-left" style="padding-left: 3px;">' . ($item->diagnosis ?: '') . '</td>';
                            echo '<td class="text-left" style="padding-left: 3px;">' . ($item->tindakan ?: '') . '</td>';
                            echo '<td class="text-left" style="padding-left: 3px;">' . ($item->dokter_operator ?: '') . '</td>';
                            echo '<td class="text-left" style="padding-left: 3px;">' . ($item->dokter_anestesi ?: '') . '</td>';
                            echo '<td class="text-center">' . ($item->nama_anestesi ?: '') . '</td>';
                            echo '<td class="text-center"></td>'; // PTGS column (empty)
                            echo '</tr>';
                        }

                        $row_number++;
                    }
                }

                // Close this date's page
                echo '</tbody></table>';
                ?>
                <!-- Footer -->
                <div class="footer-notes">
                    <strong>NB :</strong><br>
                    1. Jam operasi sewaktu-waktu dapat berubah<br>
                    2. Pasien dengan jadwal urutan jam pertama dan jam kedua dari masing-masing kamar operasi dipuasakan sejak 02.00 dini hari, Sedangkan pasien urutan ke-3 dipuasakan sejak pukul 06.00 pagi hari<br>
                    3. Apabila pasien sudah masuk rawat inap, mohon lapor ke dokter/operator<br>
                    4. MKR (Menunggu Konfirmasi Ruangan )
                </div>
                <?php
                echo '</div>'; // Close this page
                $page_number++;
            } // End foreach date
        } // End of else block
        ?>
    </div>

    <script>
        // Check required libraries
        if (typeof html2canvas === 'undefined') {
            console.error('html2canvas library not loaded');
            alert('Error: html2canvas library tidak tersedia. Silakan refresh halaman.');
        }

        if (typeof JSZip === 'undefined') {
            console.error('JSZip library not loaded');
            alert('Error: JSZip library tidak tersedia. Silakan refresh halaman.');
        }

        // Get unique dates from PHP
        const uniqueDates = <?= json_encode($unique_dates) ?>;

        // ========== SET UKURAN KERTAS CETAK - PROFESIONAL DAN SIMETRIS ==========
        // Ukuran kertas: 3508px (A4 300DPI landscape width)
        function createHighResolutionClone(originalPage) {
            const clone = originalPage.cloneNode(true);

            // ========== SET POSISI DAN UKURAN DASAR CETAK - FULL WIDTH ==========
            clone.style.position = 'absolute';
            clone.style.left = '-9999px';
            clone.style.top = '0';
            clone.style.width = '3508px'; // A4 300DPI landscape width - UKURAN KERTAS CETAK
            clone.style.padding = '30px'; // Padding lebih kecil untuk memaksimalkan area konten
            clone.style.boxShadow = 'none';
            clone.style.margin = '0 auto'; // Center alignment untuk simetris
            clone.style.backgroundColor = '#ffffff';
            clone.style.fontFamily = 'Arial, sans-serif'; // Font konsisten untuk seluruh halaman

            // ========== SET TYPOGRAPHY CETAK - FONT KONSISTEN ==========
            // Header utama - font konsisten Arial
            const headerTitle = clone.querySelector('.header-title');
            if (headerTitle) {
                headerTitle.style.fontSize = '52px'; // Lebih besar untuk profesional
                headerTitle.style.marginBottom = '25px';
                headerTitle.style.fontWeight = 'bold';
                headerTitle.style.fontFamily = 'Arial, sans-serif';
                headerTitle.style.textAlign = 'center';
            }

            // Header subtitle
            const headerSubtitle = clone.querySelector('.header-subtitle');
            if (headerSubtitle) {
                headerSubtitle.style.fontSize = '46px'; // Proporsional dengan title
                headerSubtitle.style.marginBottom = '30px';
                headerSubtitle.style.fontFamily = 'Arial, sans-serif';
                headerSubtitle.style.textAlign = 'center';
            }

            // Tujuan RS
            const tujuanRs = clone.querySelector('.tujuan-rs');
            if (tujuanRs) {
                tujuanRs.style.fontSize = '48px'; // Konsisten dengan header
                tujuanRs.style.marginBottom = '35px';
                tujuanRs.style.fontFamily = 'Arial, sans-serif';
                tujuanRs.style.fontWeight = 'bold';
            }

            // ========== SET TABEL CETAK - PROFESIONAL DAN SIMETRIS ==========
            const table = clone.querySelector('.table-operasi');
            if (table) {
                // Tabel menggunakan fixed layout untuk proporsional dan simetris
                table.style.fontSize = '38px'; // PERBAIKAN: Font lebih besar untuk readability
                table.style.lineHeight = '1.4'; // Line height lebih baik untuk readability
                table.style.marginBottom = '35px';
                table.style.width = '100%'; // Full width untuk memenuhi halaman
                table.style.tableLayout = 'fixed'; // Fixed untuk kolom proporsional
                table.style.wordBreak = 'break-word';
                table.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                table.style.borderCollapse = 'collapse';

                // ========== SET UKURAN CELL TABEL CETAK - PROFESIONAL ==========
                const cells = clone.querySelectorAll('.table-operasi th, .table-operasi td');
                cells.forEach(cell => {
                    cell.style.border = '2px solid #000';
                    cell.style.padding = '12px 10px'; // Padding lebih besar untuk profesional
                    cell.style.verticalAlign = 'top';
                    cell.style.wordWrap = 'break-word';
                    cell.style.overflowWrap = 'break-word';
                    cell.style.whiteSpace = 'normal'; // Normal untuk text wrapping
                    cell.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                    cell.style.fontSize = '38px'; // PERBAIKAN: Font size lebih besar untuk semua cell
                    cell.style.fontWeight = 'normal'; // Weight normal untuk readability
                    cell.style.height = 'auto';
                    cell.style.maxHeight = 'none';
                    cell.style.overflow = 'visible';
                });

                // PERBAIKAN: KOLOM TINDAKAN, OPERATOR DAN DIAGNOSA - RATA KIRI DAN RESPONSIF
                const tindakanCells = clone.querySelectorAll('.table-operasi td:nth-child(8)'); // TINDAKAN
                const operatorCells = clone.querySelectorAll('.table-operasi td:nth-child(9)'); // OPERATOR
                const diagnosaCells = clone.querySelectorAll('.table-operasi td:nth-child(7)'); // DIAGNOSA

                // Kolom TINDAKAN - font size lebih besar untuk readability
                tindakanCells.forEach(cell => {
                    cell.style.textAlign = 'left'; // Tetap rata kiri
                    cell.style.padding = '12px 10px'; // Padding konsisten
                    cell.style.whiteSpace = 'normal'; // Normal untuk wrapping
                    cell.style.overflowWrap = 'break-word'; // Break word otomatis
                    cell.style.wordBreak = 'break-word'; // Break word untuk teks panjang
                    cell.style.lineHeight = '1.4'; // Line height konsisten
                    cell.style.verticalAlign = 'top';
                    cell.style.fontSize = '38px'; // PERBAIKAN: Font lebih besar untuk TINDAKAN
                    cell.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                    cell.style.fontWeight = 'normal'; // Weight normal untuk readability
                    cell.style.height = 'auto';
                    cell.style.maxHeight = 'none';
                    cell.style.overflow = 'visible'; // Visible agar tidak terpotong
                });

                // Kolom OPERATOR - font size lebih besar untuk readability
                operatorCells.forEach(cell => {
                    cell.style.textAlign = 'left'; // Tetap rata kiri
                    cell.style.padding = '12px 10px'; // Padding konsisten
                    cell.style.whiteSpace = 'normal'; // Normal untuk wrapping
                    cell.style.overflowWrap = 'break-word'; // Break word otomatis
                    cell.style.wordBreak = 'break-word'; // Break word untuk teks panjang
                    cell.style.lineHeight = '1.4'; // Line height konsisten
                    cell.style.verticalAlign = 'top';
                    cell.style.fontSize = '38px'; // PERBAIKAN: Font lebih besar untuk OPERATOR
                    cell.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                    cell.style.fontWeight = 'normal'; // Weight normal untuk readability
                    cell.style.height = 'auto';
                    cell.style.maxHeight = 'none';
                    cell.style.overflow = 'visible'; // Visible agar tidak terpotong
                });

                // Kolom DIAGNOSA - font size lebih besar untuk readability
                diagnosaCells.forEach(cell => {
                    cell.style.textAlign = 'left'; // Tetap rata kiri
                    cell.style.padding = '12px 10px'; // Padding konsisten
                    cell.style.whiteSpace = 'normal'; // Normal untuk wrapping
                    cell.style.overflowWrap = 'break-word'; // Break word otomatis
                    cell.style.wordBreak = 'break-word'; // Break word untuk teks panjang
                    cell.style.lineHeight = '1.4'; // Line height konsisten
                    cell.style.verticalAlign = 'top';
                    cell.style.fontSize = '38px'; // PERBAIKAN: Font lebih besar untuk DIAGNOSA
                    cell.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                    cell.style.fontWeight = 'normal'; // Weight normal untuk readability
                    cell.style.height = 'auto';
                    cell.style.maxHeight = 'none';
                    cell.style.overflow = 'visible'; // Visible agar tidak terpotong
                });

                // ========== SET UKURAN KOLOM TABEL CETAK - PERSENTASE OPTIMIZED ==========
                // Column widths: NO(3%), JAM(4.5%), NAMA(11%), MR(4.5%), UMUR(2.5%),
                // KELAS(7.5%), DIAGNOSA(18%), TINDAKAN(13%), OPERATOR(13%),
                // DR.ANES(13%), JENIS ANES(6%), PTGS(4%)
                // Total: 100% - Optimized untuk A4 landscape layout

                // ========== SET HEADER TABEL CETAK - PROFESIONAL ==========
                const headers = clone.querySelectorAll('.table-operasi th');
                headers.forEach(header => {
                    header.style.fontSize = '38px'; // PERBAIKAN: Font size lebih besar untuk header
                    header.style.fontWeight = 'bold';
                    header.style.textAlign = 'center';
                    header.style.backgroundColor = '#f8f9fa';
                    header.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                    header.style.padding = '12px 10px'; // Padding konsisten
                    header.style.lineHeight = '1.4'; // Line height konsisten
                    header.style.verticalAlign = 'middle';
                });

                // ========== SET GROUP HEADERS CETAK - PROFESIONAL ==========
                // Date headers
                const dateHeaders = clone.querySelectorAll('.group-header-date');
                dateHeaders.forEach(header => {
                    header.style.fontSize = '38px'; // Lebih besar untuk prominence
                    header.style.padding = '15px 10px'; // Padding lebih besar
                    header.style.backgroundColor = '#FFCCCC';
                    header.style.fontWeight = 'bold';
                    header.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                    header.style.textAlign = 'left';
                    header.style.lineHeight = '1.4';
                });

                // Room headers
                const roomHeaders = clone.querySelectorAll('.group-header-room');
                roomHeaders.forEach(header => {
                    header.style.fontSize = '36px'; // Konsisten dengan hierarchy
                    header.style.padding = '12px 10px'; // Padding konsisten
                    header.style.backgroundColor = '#CCCCCC';
                    header.style.fontWeight = 'bold';
                    header.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                    header.style.textAlign = 'center';
                    header.style.lineHeight = '1.4';
                });
            }

            // ========== SET GROUP HEADER DATE - RATA KIRI ==========
            const groupHeaderDates = clone.querySelectorAll('.group-header-date');
            groupHeaderDates.forEach(header => {
                header.style.textAlign = 'left'; // PERBAIKAN: Rata kiri untuk group header date
                header.style.fontFamily = 'Arial, sans-serif';
                header.style.fontWeight = 'bold';
            });

            // ========== SET FOOTER CETAK - PROFESIONAL ==========
            const footer = clone.querySelector('.footer-notes');
            if (footer) {
                footer.style.fontSize = '28px'; // PERBAIKAN: Font lebih kecil dari isi tabel (38px)
                footer.style.marginTop = '30px';
                footer.style.lineHeight = '1.4'; // Line height konsisten
                footer.style.padding = '20px';
                footer.style.backgroundColor = '#f9f9f9';
                footer.style.borderRadius = '8px';
                footer.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                footer.style.textAlign = 'left'; // Rata kiri untuk readability
            }

            // Apply dynamic scaling based on content density
            applyDynamicScalingToClone(clone, originalPage);

            return clone;
        }

        // Optimized save function with better performance - FIXED BUTTON TEXT BUG
        document.getElementById('btnSaveImages').addEventListener('click', function() {
            const loading = document.getElementById('loading');
            const pages = document.querySelectorAll('.print-page');

            if (pages.length === 0) {
                alert('Tidak ada halaman untuk diunduh.');
                return;
            }

            // FIXED: Simpan text asli button sebelum diubah
            const originalText = this.innerHTML;
            this.setAttribute('data-original-text', originalText);

            // Disable button to prevent multiple clicks
            this.disabled = true;
            this.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';

            loading.style.display = 'block';

            // Check if single date or multiple dates
            if (pages.length === 1 && uniqueDates.length === 1) {
                // Single date - download direct image
                document.getElementById('loadingText').textContent = 'Sedang membuat gambar...';

                // Create high-resolution clone for download
                const clonedPage = createHighResolutionClone(pages[0]);
                document.body.appendChild(clonedPage);

                // ========== CANVAS SETTINGS PROFESIONAL UNTUK DOWNLOAD ==========
                html2canvas(clonedPage, {
                    scale: 2.0, // High quality scale
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: 3508, // A4 300DPI landscape width
                    windowHeight: clonedPage.scrollHeight,
                    logging: false, // Disable logging untuk speed
                    removeContainer: true,
                    width: 3508, // A4 300DPI landscape width - PROFESIONAL
                    height: null, // Auto height based on content
                    imageTimeout: 5000, // Timeout untuk prevent hanging
                    onclone: function(clonedDoc) {
                        // Remove unnecessary elements untuk speed
                        const controlButtons = clonedDoc.querySelector('.control-buttons');
                        if (controlButtons) controlButtons.remove();

                        // PERBAIKAN: TABEL PROFESIONAL UNTUK DOWNLOAD
                        const table = clonedDoc.querySelector('.table-operasi');
                        if (table) {
                            table.style.width = '100%'; // Full width untuk memenuhi halaman
                            table.style.tableLayout = 'fixed'; // Fixed untuk kolom proporsional
                            table.style.wordBreak = 'break-word'; // Break long words
                            table.style.margin = '0 auto'; // Center alignment
                            table.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                        }

                        // ========== STYLING DOWNLOAD JPG - DARI FILE SAVE (JANGAN DIUBAH) ==========
                        // Tambahkan class download-mode untuk mengaktifkan style download
                        clonedDoc.body.classList.add('download-mode');

                        const allTableCells = clonedDoc.querySelectorAll('.table-operasi td');
                        const subreportCells = clonedDoc.querySelectorAll('.subreport-cell');

                        // STYLING SERAGAM UNTUK SEMUA CELL TABEL - FONT SIZE SAMA (SESUAI GAMBAR)
                        allTableCells.forEach(cell => {
                            cell.style.fontSize = '32px'; // FONT SIZE SERAGAM UNTUK SEMUA KOLOM
                            cell.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                            cell.style.fontWeight = 'normal'; // Weight normal
                            cell.style.lineHeight = '1.3'; // Line height konsisten
                            cell.style.padding = '8px 6px'; // Padding seragam
                            cell.style.verticalAlign = 'top'; // Top alignment
                            cell.style.whiteSpace = 'normal'; // Allow wrapping
                            cell.style.overflowWrap = 'break-word'; // Break word otomatis
                            cell.style.wordBreak = 'break-word'; // Break word untuk teks panjang
                            cell.style.height = 'auto';
                            cell.style.maxHeight = 'none';
                            cell.style.overflow = 'visible'; // Visible agar tidak terpotong
                        });

                        // PERBAIKAN: Subreport cells dengan border tambahan (SESUAI GAMBAR)
                        subreportCells.forEach(cell => {
                            cell.style.border = '1px solid #000'; // Border hitam untuk subreport cells
                            cell.style.textAlign = 'left'; // Rata kiri untuk subreport
                        });

                        // PERBAIKAN: Group header date rata kiri untuk single image download
                        const groupHeaderDates = clonedDoc.querySelectorAll('.group-header-date');
                        groupHeaderDates.forEach(header => {
                            header.style.textAlign = 'left'; // Rata kiri untuk group header date
                            header.style.fontFamily = 'Arial, sans-serif';
                            header.style.fontWeight = 'bold';
                        });

                        // PERBAIKAN: Footer font size lebih kecil untuk single image download
                        const footer = clonedDoc.querySelector('.footer-notes');
                        if (footer) {
                            footer.style.fontSize = '28px'; // Font lebih kecil dari isi tabel (38px)
                            footer.style.fontFamily = 'Arial, sans-serif';
                            footer.style.textAlign = 'left';
                        }
                    }
                }).then(canvas => {
                    try {
                        canvas.toBlob(function(blob) {
                            if (!blob) {
                                throw new Error('Failed to create image blob');
                            }

                            // Use date from ppo.tgl_operasi for filename
                            const dateStr = uniqueDates[0].replace(/-/g, '_');
                            const fileName = `operasi_${dateStr}.jpg`; // JPG format for WhatsApp

                            // Check if saveAs is available
                            if (typeof saveAs === 'function') {
                                saveAs(blob, fileName);
                            } else {
                                // Fallback download method
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = fileName;
                                document.body.appendChild(a);
                                a.click();
                                document.body.removeChild(a);
                                URL.revokeObjectURL(url); // Clean up
                            }

                            loading.style.display = 'none';
                            resetDownloadButton();

                            // Clean up cloned element
                            document.body.removeChild(clonedPage);
                        }, 'image/jpeg', 0.85); // Optimized quality untuk speed (85%)
                    } catch (error) {
                        console.error('Error in canvas.toBlob:', error);
                        loading.style.display = 'none';
                        resetDownloadButton();
                        document.body.removeChild(clonedPage);
                        alert('Terjadi kesalahan saat membuat gambar. Silakan coba lagi.');
                    }
                }).catch(error => {
                    console.error('Error generating image:', error);
                    loading.style.display = 'none';
                    resetDownloadButton();
                    document.body.removeChild(clonedPage);
                    alert('Terjadi kesalahan saat membuat gambar. Silakan coba lagi.');
                });
            } else {
                // Multiple dates - create ZIP with sequential processing for better performance
                processZipSequentially(pages, uniqueDates, loading);
            }
        });

        // Helper function to reset download button - FIXED BUG BUTTON TEXT
        function resetDownloadButton() {
            const downloadBtn = document.getElementById('btnSaveImages');
            downloadBtn.disabled = false;

            const totalPages = document.querySelectorAll('.print-page').length;
            // FIXED: Simpan text asli button dan kembalikan setelah download
            const originalText = downloadBtn.getAttribute('data-original-text');
            if (originalText) {
                downloadBtn.innerHTML = originalText;
            } else {
                // Fallback: tentukan berdasarkan kondisi
                if (totalPages === 1 && uniqueDates.length === 1) {
                    downloadBtn.innerHTML = '<i class="bi bi-download"></i> Download A4 JPG';
                } else {
                    downloadBtn.innerHTML = '<i class="bi bi-download"></i> Download A4 JPG (ZIP)';
                }
            }
        }

        // Optimized sequential ZIP processing to prevent memory overload
        async function processZipSequentially(pages, uniqueDates, loading) {
            const startTime = performance.now();
            const zip = new JSZip();
            const totalPages = pages.length;

            document.getElementById('loadingText').textContent = 'Memulai pemrosesan ' + totalPages + ' halaman...';
            console.log('Starting ZIP processing for', totalPages, 'pages');

            try {
                for (let i = 0; i < totalPages; i++) {
                    const page = pages[i];
                    const progress = i + 1;

                    // Update progress
                    document.getElementById('loadingText').textContent = `Memproses halaman ${progress} dari ${totalPages}...`;

                    // Wait a bit to allow UI update
                    await new Promise(resolve => setTimeout(resolve, 100));

                    try {
                        // Create high-resolution clone for this page
                        const clonedPage = createHighResolutionClone(page);
                        document.body.appendChild(clonedPage);

                        // ========== CANVAS SETTINGS PROFESIONAL UNTUK ZIP PROCESSING ==========
                        const canvas = await html2canvas(clonedPage, {
                            scale: 2.0, // High quality scale
                            useCORS: true,
                            allowTaint: true,
                            backgroundColor: '#ffffff',
                            scrollX: 0,
                            scrollY: 0,
                            windowWidth: 3508, // A4 300DPI landscape width
                            windowHeight: clonedPage.scrollHeight,
                            logging: false, // Disable logging untuk speed
                            removeContainer: true,
                            width: 3508, // A4 300DPI landscape width - PROFESIONAL
                            height: null, // Auto height
                            imageTimeout: 3000, // Shorter timeout untuk ZIP processing
                            onclone: function(clonedDoc) {
                                // Remove unnecessary elements untuk speed
                                const controlButtons = clonedDoc.querySelector('.control-buttons');
                                if (controlButtons) controlButtons.remove();

                                // PERBAIKAN: TABEL PROFESIONAL UNTUK ZIP DOWNLOAD
                                const table = clonedDoc.querySelector('.table-operasi');
                                if (table) {
                                    table.style.width = '100%'; // Full width untuk memenuhi halaman
                                    table.style.tableLayout = 'fixed'; // Fixed untuk kolom proporsional
                                    table.style.wordBreak = 'break-word'; // Break long words
                                    table.style.margin = '0 auto'; // Center alignment
                                    table.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                                }

                                // ========== STYLING DOWNLOAD ZIP - DARI FILE SAVE (JANGAN DIUBAH) ==========
                                // Tambahkan class download-mode untuk mengaktifkan style download
                                clonedDoc.body.classList.add('download-mode');

                                const allTableCells = clonedDoc.querySelectorAll('.table-operasi td');
                                const subreportCells = clonedDoc.querySelectorAll('.subreport-cell');

                                // STYLING SERAGAM UNTUK SEMUA CELL TABEL ZIP - FONT SIZE SAMA (SESUAI GAMBAR)
                                allTableCells.forEach(cell => {
                                    cell.style.fontSize = '32px'; // FONT SIZE SERAGAM UNTUK SEMUA KOLOM
                                    cell.style.fontFamily = 'Arial, sans-serif'; // Font konsisten
                                    cell.style.fontWeight = 'normal'; // Weight normal
                                    cell.style.lineHeight = '1.3'; // Line height konsisten
                                    cell.style.padding = '8px 6px'; // Padding seragam
                                    cell.style.verticalAlign = 'top'; // Top alignment
                                    cell.style.whiteSpace = 'normal'; // Allow wrapping
                                    cell.style.overflowWrap = 'break-word'; // Break word otomatis
                                    cell.style.wordBreak = 'break-word'; // Break word untuk teks panjang
                                    cell.style.height = 'auto';
                                    cell.style.maxHeight = 'none';
                                    cell.style.overflow = 'visible'; // Visible agar tidak terpotong
                                });

                                // PERBAIKAN: Subreport cells dengan border tambahan ZIP (SESUAI GAMBAR)
                                subreportCells.forEach(cell => {
                                    cell.style.border = '1px solid #000'; // Border hitam untuk subreport cells
                                    cell.style.textAlign = 'left'; // Rata kiri untuk subreport
                                });

                                // PERBAIKAN: Group header date rata kiri untuk ZIP download
                                const groupHeaderDates = clonedDoc.querySelectorAll('.group-header-date');
                                groupHeaderDates.forEach(header => {
                                    header.style.textAlign = 'left'; // Rata kiri untuk group header date
                                    header.style.fontFamily = 'Arial, sans-serif';
                                    header.style.fontWeight = 'bold';
                                });

                                // PERBAIKAN: Footer font size lebih kecil untuk ZIP download
                                const footer = clonedDoc.querySelector('.footer-notes');
                                if (footer) {
                                    footer.style.fontSize = '28px'; // Font lebih kecil dari isi tabel (38px)
                                    footer.style.fontFamily = 'Arial, sans-serif';
                                    footer.style.textAlign = 'left';
                                }
                            }
                        });

                        // Convert to JPG blob
                        const blob = await new Promise((resolve, reject) => {
                            canvas.toBlob(function(blob) {
                                if (blob) {
                                    resolve(blob);
                                } else {
                                    reject(new Error('Failed to create blob'));
                                }
                            }, 'image/jpeg', 0.80); // Optimized quality untuk ZIP speed (80%)
                        });

                        // Add to ZIP
                        const dateStr = uniqueDates[i] ? uniqueDates[i].replace(/-/g, '_') : `halaman_${i + 1}`;
                        const fileName = `operasi_${dateStr}.jpg`; // JPG format
                        zip.file(fileName, blob);

                        // ========== AGGRESSIVE MEMORY CLEANUP UNTUK PERFORMANCE ==========
                        // Clean up canvas immediately
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        canvas.width = 1;
                        canvas.height = 1;

                        // Remove cloned element
                        document.body.removeChild(clonedPage);

                        // Force garbage collection if available
                        if (window.gc) window.gc();

                    } catch (error) {
                        console.error(`Error processing page ${progress}:`, error);
                        // Clean up cloned element on error
                        if (document.body.contains(clonedPage)) {
                            document.body.removeChild(clonedPage);
                        }
                        throw new Error(`Gagal memproses halaman ${progress}`);
                    }
                }

                // Generate ZIP
                document.getElementById('loadingText').textContent = 'Membuat file ZIP...';
                await new Promise(resolve => setTimeout(resolve, 200)); // Allow UI update

                // ========== OPTIMIZED ZIP GENERATION UNTUK SPEED ==========
                const content = await zip.generateAsync({
                    type: 'blob',
                    compression: 'DEFLATE',
                    compressionOptions: { level: 3 }, // Faster compression (level 3 instead of 6)
                    streamFiles: true // Stream files untuk better memory management
                });

                // Download ZIP
                const now = new Date();
                const datetime = now.getFullYear() +
                               String(now.getMonth() + 1).padStart(2, '0') +
                               String(now.getDate()).padStart(2, '0') + '_' +
                               String(now.getHours()).padStart(2, '0') +
                               String(now.getMinutes()).padStart(2, '0') +
                               String(now.getSeconds()).padStart(2, '0');

                if (typeof saveAs === 'function') {
                    saveAs(content, `cetak_${datetime}.zip`);
                } else {
                    // Fallback download
                    const url = URL.createObjectURL(content);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `cetak_${datetime}.zip`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }

                loading.style.display = 'none';
                resetDownloadButton();

                // Log performance
                logPerformance('ZIP processing', startTime);
                cleanupMemory();

            } catch (error) {
                console.error('Error in ZIP processing:', error);
                loading.style.display = 'none';
                resetDownloadButton();
                alert('Terjadi kesalahan: ' + error.message + '. Silakan coba lagi.');
                cleanupMemory();
            }
        }

        // Print function
        document.getElementById('btnPrint').addEventListener('click', function() {
            window.print();
        });
        
        // Detect if loaded in modal or new tab
        function isInModal() {
            try {
                return window.parent !== window && window.parent.document.getElementById('modalCetakGambar');
            } catch (e) {
                return false;
            }
        }

        // Handle close button based on context
        document.getElementById('btnClose').addEventListener('click', function() {
            if (isInModal()) {
                // If in modal, close the modal
                try {
                    window.parent.$('#modalCetakGambar').modal('hide');
                } catch (e) {
                    // Fallback if jQuery not available
                    window.parent.document.getElementById('modalCetakGambar').style.display = 'none';
                }
            } else {
                // If in new tab, close the window
                window.close();
            }
        });

        // ========== AGGRESSIVE MEMORY CLEANUP FUNCTION ==========
        function cleanupMemory() {
            // Clear any remaining canvas elements
            const canvases = document.querySelectorAll('canvas');
            canvases.forEach(canvas => {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                canvas.width = 1;
                canvas.height = 1;
            });

            // Clear any cloned elements that might be left
            const clonedElements = document.querySelectorAll('[style*="-9999px"]');
            clonedElements.forEach(el => {
                if (el.parentNode) el.parentNode.removeChild(el);
            });

            // Force garbage collection if available
            if (window.gc) {
                window.gc();
            }

            console.log('Memory cleanup completed');
        }

        // Performance monitoring
        function logPerformance(operation, startTime) {
            const endTime = performance.now();
            const duration = (endTime - startTime) / 1000;
            console.log(`${operation} completed in ${duration.toFixed(2)} seconds`);
        }

        // ========== DYNAMIC CONTENT SCALING UNTUK PERFORMANCE ==========
        function applyDynamicScalingToClone(clone, originalPage) {
            const tableRows = originalPage.querySelectorAll('.table-operasi tbody tr');
            const dataRows = Array.from(tableRows).filter(row =>
                !row.querySelector('.group-header-date') &&
                !row.querySelector('.group-header-room')
            );

            // Apply large dataset scaling untuk dense data (optimized untuk 3508px)
            if (dataRows.length > 15) {
                const table = clone.querySelector('.table-operasi');
                if (table) {
                    table.style.fontSize = '28px'; // Reduced untuk dense data performance
                }

                const cells = clone.querySelectorAll('.table-operasi th, .table-operasi td');
                cells.forEach(cell => {
                    cell.style.padding = '6px 4px'; // Tighter spacing untuk landscape layout
                    cell.style.lineHeight = '1.1'; // Compact line height
                });

                console.log('Applied large dataset scaling to clone for page with', dataRows.length, 'rows');
            }
        }

        // Auto focus for better UX and update page count
        window.onload = function() {
            document.body.focus();

            // Detect if in modal and apply appropriate styling
            if (isInModal()) {
                document.body.classList.add('modal-context');
                // Apply modal-specific optimizations
                const printContainer = document.getElementById('printContainer');
                if (printContainer) {
                    printContainer.style.maxWidth = '100%';
                    printContainer.style.overflow = 'auto';
                }
            }

            // Update total pages info
            const totalPages = document.querySelectorAll('.print-page').length;
            document.getElementById('totalPages').textContent = totalPages;

            // Update button text based on context
            const closeBtn = document.getElementById('btnClose');
            if (isInModal()) {
                closeBtn.innerHTML = '<i class="bi bi-x-circle"></i> Tutup Modal';
            } else {
                closeBtn.innerHTML = '<i class="bi bi-x-circle"></i> Tutup Tab';
            }

            // Update download button text based on single vs multiple dates - FIXED INITIALIZATION
            const downloadBtn = document.getElementById('btnSaveImages');
            let buttonText;
            if (totalPages === 1 && uniqueDates.length === 1) {
                buttonText = '<i class="bi bi-download"></i> Download A4 JPG';
            } else {
                buttonText = '<i class="bi bi-download"></i> Download A4 JPG (ZIP)';
            }

            // Set button text dan simpan sebagai original text
            downloadBtn.innerHTML = buttonText;
            downloadBtn.setAttribute('data-original-text', buttonText);

            // Show performance tip for large datasets
            if (totalPages > 5) {
                console.log('Performance tip: Processing ' + totalPages + ' pages. This may take a while.');
            }

            // Log content analysis
            const totalDataRows = document.querySelectorAll('.table-operasi tbody tr').length;
            console.log('Content analysis: ' + totalDataRows + ' total table rows across ' + totalPages + ' pages');
        };
    </script>
</body>
</html>
