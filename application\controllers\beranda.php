<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>randa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Beranda',
            'isi' => 'beranda',
            'session' => $this->session->get_userdata(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }
}

// End of File Beranda.php
// Location: ./application/controllers/Beranda.php