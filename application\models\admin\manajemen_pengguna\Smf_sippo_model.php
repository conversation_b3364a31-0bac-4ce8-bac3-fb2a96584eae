<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Smf_sippo_model extends CI_Model
{
    protected $_table = 'aplikasi.smf_sippo';
    protected $_primary_key = 'id';

    protected $_urutan_kolom = [
        null,
        'smf',
        null,
        null,
        null,
        null,
        null,
    ];

    protected $_pencarian_kolom = ['smf.DESKRIPSI'];

    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'smf',
                'label' => 'Nama SMF',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi'
                ]
            ],
            [
                'field' => 'akses[]',
                'label' => 'Akses',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi'
                ]
            ],
        ];
    }

    function simpan($data)
    {
        $this->db->replace($this->_table, $data);
    }

    function tabel()
    {
        $this->db->select('ass.id_smf, smf.DESKRIPSI smf, ass.akses_sippo');
        $this->db->from('aplikasi.smf_sippo ass');
        $this->db->join('master.referensi smf', 'smf.ID = ass.id_smf AND smf.JENIS = 26 AND smf.STATUS = 1', 'left');
        $this->db->where('ass.status', 1);

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil()
    {
        $this->tabel();
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring()
    {
        $this->tabel();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua()
    {
        $this->tabel();
        return $this->db->count_all_results();
    }

    function detail($id_smf)
    {
        $this->db->select('akses_sippo');
        $this->db->from($this->_table);
        $this->db->where('id_smf', $id_smf);

        $query = $this->db->get();
        return $query->row_array();
    }
}

// End of File Smf_sippo_model.php
// Location: ./application/models/admin/manajemen_pengguna/Smf_sippo_model.php