<!-- mulai header -->
<header>
    <div class="topbar d-flex align-items-center">
        <nav class="navbar navbar-expand">
            <div class="topbar-logo-header">
                <img src="<?= base_url('assets/images/logo.png') ?>" class="logo-icon" alt="Logo Dharmais">
                <h4 class="logo-text">Sippo</h4>
            </div>
            <div class="mobile-toggle-menu"><i class='bx bx-menu'></i></div>
            <div class="top-menu ms-auto"></div>
            <div class="user-box dropdown">
                <a class="d-flex align-items-center nav-link dropdown-toggle dropdown-toggle-nocaret" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="user-info ps-3">
                        <p class="user-name mb-0"><?= $session['nama'] ?></p>
                        <p class="designattion mb-0"><?= $session['jenis'] ?></p>
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" id="ubahTema" data-tema="<?= $session['mode_gelap'] ?>">
                            <?php if ($session['mode_gelap'] == 1): ?>
                                <i class='bx bxs-sun'></i><span>Masuki Mode Terang</span>
                            <?php else: ?>
                                <i class='bx bxs-moon'></i><span>Masuki Mode Gelap</span>
                            <?php endif ?>
                        </a>
                    </li>
                    <li>
                        <div class="dropdown-divider mb-0"></div>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?= base_url('Akun/keluar') ?>">
                            <i class='bx bx-log-out-circle'></i><span>Keluar</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
</header>
<!-- akhir header -->

<!-- mulai navigasi -->
<div class="nav-container">
    <div class="mobile-topbar-header">
        <div>
            <img src="<?= base_url('assets/images/logo.png') ?>" class="logo-icon" alt="Logo Dharmais">
        </div>
        <div>
            <h4 class="logo-text">Sippo</h4>
        </div>
        <div class="toggle-icon ms-auto"><i class='bx bx-arrow-to-left'></i>
        </div>
    </div>
    <nav class="topbar-nav">
        <ul class="metismenu" id="menu">
            <!-- mulai beranda -->
            <li>
                <a href="<?= base_url('beranda') ?>">
                    <div class="parent-icon">
                        <i class='bx bx-home-circle'></i>
                    </div>
                    <div class="menu-title">Beranda</div>
                </a>
            </li>
            <!-- akhir beranda -->
            <?php if (array_intersect(['0', '1'], $session['hak_akses'])): ?>
                <!-- mulai dashboard -->
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon">
                            <i class='bx bxs-dashboard'></i>
                        </div>
                        <div class="menu-title"><em>Dashboard</em></div>
                    </a>
                    <ul>
                        <li>
                            <a class="has-arrow" href="javascript:;">
                                Pendaftaran
                            </a>
                            <ul>
                                <li>
                                    <a href="<?= base_url('operasi/pendaftaran_pasien/Pasien_baru') ?>">
                                        - Pasien Baru Hari Ini
                                    </a>
                                </li>
                                <li>
                                    <a href="<?= base_url('operasi/pendaftaran_pasien/Urgent_cito') ?>">
                                        - Pasien Tindakan&nbsp;<em>Urgent</em>/CITO/Prioritas
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="<?= base_url('operasi/Daftar_tunggu') ?>">
                                Daftar Tunggu Operasi
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('operasi/Jadwal_operasi') ?>">
                                Jadwal Operasi Elektif Terkini
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('operasi/Operasi_today') ?>">
                                Operasi Hari Ini
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('operasi/Batal_operasi') ?>">
                                Pembatalan Operasi Hari Ini
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('operasi/Sop') ?>">
                                <em>Standard Operating Procedure</em>
                            </a>
                        </li>
                    </ul>
                </li>
                <!-- akhir dashboard -->
            <?php endif ?>
            <?php if (array_intersect(['2', '3', '4', '5'], $session['hak_akses'])): ?>
                <!-- mulai admin -->
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon">
                            <i class="fa-solid fa-user-tie fa-xs"></i>
                        </div>
                        <div class="menu-title">Admin</div>
                    </a>
                    <ul>
                        <?php if (in_array('2', $session['hak_akses'])): ?>
                            <li>
                                <a href="<?= base_url('admin/Daftar_tunggu') ?>">
                                    Daftar Tunggu
                                </a>
                            </li>
                        <?php endif ?>
                        <?php if (in_array('3', $session['hak_akses'])): ?>
                            <li>
                                <a href="<?= base_url('admin/Perjanjian') ?>">
                                    Perjanjian Operasi
                                </a>
                            </li>
                        <?php endif ?>
                        <?php if (in_array('4', $session['hak_akses'])): ?>
                            <li>
                                <a href="<?= base_url('admin/Penjadwalan') ?>">
                                    Penjadwalan Operasi
                                </a>
                            </li>
                        <?php endif ?>
                        <?php if (in_array('5', $session['hak_akses'])): ?>
                            <li>
                                <a class="has-arrow" href="javascript:;">
                                    Manajemen Pengguna
                                </a>
                                <ul>
                                    <li>
                                        <a href="<?= base_url('admin/manajemen_pengguna/Pengguna') ?>">
                                            - Per Pengguna
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?= base_url('admin/manajemen_pengguna/Smf') ?>">
                                            - Per SMF
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        <?php endif ?>
                    </ul>
                </li>
                <!-- akhir admin -->
            <?php endif ?>
        </ul>
    </nav>
</div>
<!-- akhir navigasi -->

<script>
    $(document).ready(function () {
        // mulai ubah tema
        $('#ubahTema').click(function () {
            $.ajax({
                url: "<?= base_url('Akun/ubah_tema') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    tema_sekarang: $(this).data('tema')
                },
                success: function (data) {
                    if (data.status === 200) {
                        location.reload();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Gagal mengubah',
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir ubah tema
    });
</script>