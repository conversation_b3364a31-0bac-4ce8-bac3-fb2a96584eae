<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Reservasi_antrian_model extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    public function cek_antrean()
    {
        $post = $this->input->post();
        $loket = [
            '105060101' => 'A',
            '105020201' => 'B',
            '105020705' => 'C',
            '105020704' => 'D',
            '105120101' => 'E'
        ];

        $this->db->select('*');
        $this->db->from('kemkes.reservasi_antrian kra');
        $this->db->where(
            [
                'kra.PASIEN' => $post['nomr'],
                'kra.RUANGAN' => $loket[$post['idr']],
                'kra.TANGGAL_KUNJUNGAN' => $post['tgl'],
            ]
        );
        $this->db->where("(kra.KONTAK = '" . $post['nomor'] . "' OR kr.KONTAK IS NULL)");

        $query = $this->db->get();
        $num = $query->num_rows();
        return $num;
    }

    public function simpan($data)
    {
        $this->db->insert('kemkes.reservasi_antrian', $data);
    }
}

// End of File Reservasi_antrian_model.php
// Location: ./application/models/admin/Reservasi_antrian_model.php