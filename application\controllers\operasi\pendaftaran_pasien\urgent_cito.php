<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Urgent_cito extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model('operasi/pendaftaran_pasien/Urgent_cito_model');
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Pendaftaran Pasien Urgent/CITO Hari Ini',
            'isi' => 'operasi/pendaftaran_pasien/urgent_cito/index',
            'session' => $this->session->get_userdata(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $draw = intval($post['draw']);
        $tabel = $this->Urgent_cito_model->ambil();
        $no = $post['start'];
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {

            if ($t->id_sifat_op == 2131) {
                $status = '<span class="badge bg-danger">' . $t->sifat_op_desk . '</span>';
            } else if ($t->id_sifat_op == 6080) {
                $status = '<span class="badge bg-warning">' . $t->sifat_op_desk . '</span>';
            } else {
                $status = '<span class="badge bg-info">' . $t->sifat_op_desk . '</span>';
            }

            $data[] = [
                ++$no . '.',
                $t->nama_pasien,
                $t->NORM,
                $t->tgl_lahir_umur,
                $t->diagnosa_medis,
                $t->dokter_bedah,
                $t->rencana_tindakan_operasi,
                $t->waktu_daftar,
                $status,
                $t->alasan_sifat_op,
                $t->catatan_khusus ?? '-',
                $t->r_rawat ?? '-',
                isset($t->tgl_tindakan) ? date('d/m/Y', strtotime($t->tgl_tindakan)) : '-',
                isset($t->jam_operasi) ? date('H.i.s', strtotime($t->jam_operasi)) : '-',
                $t->durasi_pen_mins
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Urgent_cito_model->hitung_semua(),
            'recordsFiltered' => $this->Urgent_cito_model->hitung_tersaring(),
            'data' => $data
        ];

        echo json_encode($output);
    }
}

// End of File Urgent_cito.php
// Location: ./application/controllers/operasi/pendaftaran_pasien/Urgent_cito.php