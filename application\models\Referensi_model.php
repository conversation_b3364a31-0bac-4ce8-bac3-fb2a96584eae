<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Referensi_model extends CI_Model
{
    public function ambil($jenis, $id)
    {
        $q = $this->input->get('q');
        $this->db->select('JENIS, ID, DESKRIPSI');
        $this->db->from('master.referensi');
        $this->db->where('STATUS', 1);
        $this->db->where('JENIS', $jenis);

        // mulai pencarian
        if ($q) {
            $this->db->like('DESKRIPSI', $q);
        }
        // akhir pencarian

        $this->db->where_in('ID', $id);
        $this->db->order_by('DESKRIPSI', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function referensi($id)
    {
        $this->db->select('id_variabel, variabel, nilai, status, status_checked');
        $this->db->from('db_master.variabel');
        $this->db->where('id_referensi', $id);
        $this->db->order_by('id_variabel', 'asc');
        $this->db->order_by('seq', 'asc');
        $this->db->where('status !=', 0);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function smf()
    {
        $this->db->select('p.SMF id_smf, smf.DESKRIPSI smf');
        $this->db->from('master.referensi smf');
        $this->db->join('master.pegawai p', 'p.SMF = smf.ID', 'left');
        $this->db->join('master.dokter d', 'd.NIP = p.NIP', 'left');
        $this->db->where('smf.JENIS', 26);
        $this->db->where('smf.STATUS', 1);
        $this->db->where('d.STATUS', 1);
        $this->db->group_by('p.SMF');
        $this->db->order_by('smf.DESKRIPSI', 'ASC');
        $query = $this->db->get();
        return $query->result_array();
    }
    public function get_ruangan_operasi()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan');
        $this->db->where('jenis_kunjungan', '6');
        $this->db->where('JENIS', '5');
        $this->db->where('ID !=', '105090102');
        $query = $this->db->get();
        return $query->result_array();
    }
    public function get_ruangan_operasi2()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan');
        $this->db->where('jenis_kunjungan', '6');
        $this->db->where('JENIS', '5');
        $this->db->where('ID !=', '105090102');
        $query = $this->db->get();
        
        // Pastikan mengembalikan array of objects
        return $query->result();
    }
    public function get_tujuan_rs()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.referensi');
        $this->db->where('JENIS', '81');
        $this->db->where_IN('ID', array('2', '16'));
        $this->db->order_by('DESKRIPSI', 'ASC'); 
        $query = $this->db->get();
        return $query->result_array();
    }
}

// End of File Referensi_model.php
// Location: ./application/models/Referensi_model.php