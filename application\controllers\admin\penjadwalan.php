<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON>dwalan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'Kamar_model',
                'Referensi_model',
                'Ruang_model',
                'admin/penjadwalan/Penjadwalan_model',
                'admin/penjadwalan/Batal_operasi_model',
                'admin/penjadwalan/Daftar_perjanjian_model',
                'admin/penjadwalan/Hari_ini_model',
                'admin/penjadwalan/History_model',
                'admin/Perjanjian_model',
                'admin/Reservasi_model',
                'admin/Waiting_list_model',
                'operasi/Dokter_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }
    }

    public function index()
    {
        $mulai = $this->session->userdata('penjadwalan_mulai');
        $akhir = $this->session->userdata('penjadwalan_akhir');
        $hari = $this->session->userdata('penjadwalan_hari');


        $data = [
            'judul' => 'Penjadwalan Operasi',
            'isi' => 'admin/penjadwalan/index',
            'session' => $this->session->get_userdata(),
            'mulai' => $mulai,
            'akhir' => $akhir,
            'id_ruangan' => $this->session->userdata('penjadwalan_ruang'),
            'tujuan' => $this->session->userdata('tujuanOperasi'),
            'ruangan' => $this->Referensi_model->get_ruangan_operasi2(),
            'hari' => $hari,

        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    public function tabel()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $jenis = $post['jenis'] ?? null;
        $this->session->set_userdata([
            'penjadwalan_mulai' => $post['mulai'] ?? null,
            'penjadwalan_akhir' => $post['akhir'] ?? null,
            'penjadwalan_ruang' => $post['id_ruangan']?? null,
            'penjadwalan_hari' => $post['hari'] ?? null,
            'tujuanOperasi' => $post['tujuanOperasi'] ?? null,
        ]);
        // var_dump($this->session->userdata('penjadwalan_ruang')); exit();

        // mulai periksa jenis
        if ($jenis == 'hari_ini') {
            $data = [
                'id_ruangan' => $post['id_ruangan'] ?? null,
                'tujuanOperasi' => $post['tujuan_rs']?? null,
            ];
            $this->load->view('admin/penjadwalan/tabel_hari_ini', $data);
        } else {
            $mulai = $post['mulai'] ?? null;
            $akhir = $post['akhir'] ?? null;
            $id_ruangan = $post['id_ruangan'] ?? null;

            if ($jenis == 'batal') {
                $data = [
                    'mulai' => $mulai,
                    'akhir' => $akhir,
                    'id_ruangan' => $id_ruangan,
                    'tujuanOperasi' => $post['tujuanOperasi'],
                ];
                // echo '<pre>';print_r($data);exit();
                $this->load->view('admin/penjadwalan/tabel_batal', $data);
            } else {
                // mulai target order
                if ($jenis == 'history') {
                    $target_order = '[0]';
                } else {
                    $target_order = '[0, 13]';
                }
                // akhir target order

                $data = [
                    'jenis' => $jenis,
                    'mulai' => $mulai,
                    'akhir' => $akhir,
                    'id_ruangan' => $id_ruangan,
                    'tujuanOperasi' => $post['tujuanOperasi'],
                    'target_order' => $target_order,

                ];

                // mulai hari perjanjian
                if ($jenis == 'daftar_perjanjian') {
                    $data['hari'] = $post['hari'];
                }
                // akhir hari perjanjian

                // echo '<pre>';print_r($data);exit();
                $this->load->view('admin/penjadwalan/tabel', $data);
            }
        }
        // akhir periksa jenis
    }

    public function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $jenis = $post['jenis'] ?? null;
        $mulai = $post['mulai'] ?? null;
        $akhir = $post['akhir'] ?? null;
        $id_ruangan = $post['id_ruangan'] ?? null;
        $tujuanOperasi = $post['tujuanOperasi'] ?? null;
        $draw = intval($post['draw']);
        $no = $post['start'];
        $id = null;
        $ruang_rawat = null;
        $disabled = null;
        $disabled_link = null;
        $tombol_status = null;

        // mulai periksa jenis
        if ($jenis == 'daftar_perjanjian') {
            // mulai tabel daftar tunggu
            $hari = $post['hari'] ?? null;
            $tabel = $this->Daftar_perjanjian_model->ambil($mulai, $akhir, $hari, $tujuanOperasi);
            
            foreach ($tabel as $t) {
                // mulai ruang rawat
                if ($t->menunggu_konfirmasi_ruang == '6233') {
                    $ruang_rawat = "<p class='text-warning'>Menunggu Konfirmasi Ruang</p>";
                } elseif (isset($t->ruang_rawat)) {
                    $ruang_rawat = $t->ruang_rawat;
                } else {
                    $ruang_rawat = '-';
                }
                // akhir ruang rawat

                // mulai id dan disabled
                if (isset($t->id_penjadwalan)) {
                    $id = 'penjadwalan/' . $t->id_penjadwalan;
                    $disabled = ($t->status_penjadwalan == 0) ? 'disabled' : null;
                } else {
                    $id = "reservasi/{$t->id_reservasi}";
                    $disabled = 'disabled';
                }
                // akhir id dan disabled

                $data[] = [
                    ++$no . '.',
                    isset($t->tgl_operasi) && $t->tgl_operasi != '0000-00-00' ? date('d/m/Y', strtotime($t->tgl_operasi)) : '-',
                    $t->kamar_operasi ?? '-',
                    $t->tujuan_rs ?? '-',
                    isset($t->tgl_dibuat) ? date('d/m/Y, H.i.s', strtotime($t->tgl_dibuat)) : '-',
                    $t->nama,
                    $t->norm,
                    $t->tgl_lahir_umur,
                    $ruang_rawat,
                    $t->diagnosis ?? '-',
                    $t->tindakan ?? '-',
                    !empty(trim($t->dokter_operator)) ? $t->dokter_operator : '-',
                    !empty(trim($t->dokter_anestesi)) ? $t->dokter_anestesi : '-',
                    !empty(trim($t->catatan_khusus)) ? $t->catatan_khusus : '-',
                    "<div class='btn-group-vertical' role='group'>
                        <a href='" . base_url('admin/penjadwalan/form/' . $id) . "' role='button' class='btn btn-primary tbl-ubah-penjadwalan' data-bs-toggle='tooltip' data-bs-placement='right' title='Buat atau ubah'>
                            <i class='bx bx-edit'></i>
                        </a>
                        <button type='button' class='btn btn-danger tbl-batal-penjadwalan' data-id='$t->id_penjadwalan' data-bs-toggle='modal' data-bs-target='#modalBatalPenjadwalan' $disabled>
                            <i class='bx bx-x'></i>
                        </button>
                    </div>"
                ];
            }
            // echo '<pre>';print_r($data);exit();

            $output = [
                'draw' => $draw,
                'recordsTotal' => $this->Daftar_perjanjian_model->hitung_semua($mulai, $akhir, $hari, $id_ruangan, $tujuanOperasi),
                'recordsFiltered' => $this->Daftar_perjanjian_model->hitung_tersaring($mulai, $akhir, $hari, $id_ruangan, $tujuanOperasi),
                'data' => $data
            ];
            // echo '<pre>';print_r($output);exit();
            // akhir tabel daftar tunggu
        } elseif ($jenis == 'hari_ini') {
            $tabel = $this->Hari_ini_model->ambil($id_ruangan, $tujuanOperasi);
            
            foreach ($tabel as $t) {
                // mulai ruang rawat
                if ($t->menunggu_konfirmasi_ruang == '6233') {
                    $ruang_rawat = "<p class='text-warning'>Menunggu Konfirmasi Ruang</p>";
                } elseif (isset($t->ruang_rawat)) {
                    $ruang_rawat = $t->ruang_rawat;
                } else {
                    $ruang_rawat = '-';
                }
                // akhir ruang rawat

                // mulai status penjadwalan
                if (isset($t->id_penjadwalan)) {
                    $id = 'penjadwalan/' . $t->id_penjadwalan;
                    if ($t->status_penjadwalan == 0) {
                        $disabled = 'disabled';
                        $disabled_link = "tabindex='-1' aria-disabled='true'";
                        $tombol_status = "<p class='text-danger'>Batal</p>";
                    } else {
                        $tombol_status = "<div class='btn-group-vertical btn-group-sm' role='group'>";
                        if ($t->status_penjadwalan == 1) {
                            $disabled = null;
                            $disabled_link = null;

                            $tombol_status .= "<button class='btn btn-sm btn-primary'>Menunggu masuk IBS</button>";
                            $tombol_status .= "<a class='btn btn-sm btn-outline-primary tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='1' data-proses='selanjutnya'><i class='bx bx-chevron-down'></i></a>";
                        } elseif ($t->status_penjadwalan == 2) {
                            $disabled = null;
                            $disabled_link = null;

                            $tombol_status .= "<a class='btn btn-sm btn-outline-success tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='2' data-proses='sebelumnya'><i class='bx bx-chevron-up'></i></a>";
                            $tombol_status .= "<button class='btn btn-sm btn-success'>Ruang tunggu operasi</button>";
                            $tombol_status .= "<a class='btn btn-sm btn-outline-success tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='2' data-proses='selanjutnya'><i class='bx bx-chevron-down'></i></a>";
                        } elseif ($t->status_penjadwalan == 3) {
                            $disabled = null;
                            $disabled_link = null;

                            $tombol_status .= "<a class='btn btn-sm btn-outline-warning tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='3' data-proses='sebelumnya'><i class='bx bx-chevron-up'></i></a>";
                            $tombol_status .= "<button class='btn btn-sm btn-warning'>Sedang operasi</button>";
                            $tombol_status .= "<a class='btn btn-sm btn-outline-warning tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='3' data-proses='selanjutnya'><i class='bx bx-chevron-down'></i></a>";
                        } elseif ($t->status_penjadwalan == 4) {
                            $disabled = null;
                            $disabled_link = null;

                            $tombol_status .= "<a class='btn btn-sm btn-outline-info tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='4' data-proses='sebelumnya'><i class='bx bx-chevron-up'></i></a>";
                            $tombol_status .= "<button class='btn btn-sm btn-info'>Ruang observasi</button>";
                            $tombol_status .= "<a class='btn btn-sm btn-outline-info tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='4' data-proses='selanjutnya'><i class='bx bx-chevron-down'></i></a>";
                        } elseif ($t->status_penjadwalan == 5) {
                            $disabled = 'disabled';
                            $disabled_link = "tabindex='-1' aria-disabled='true'";

                            $tombol_status .= "<a class='btn btn-sm btn-outline-secondary tombol-status-penjadwalan' role='button' data-id='$t->id_penjadwalan' data-status='5' data-proses='sebelumnya'><i class='bx bx-chevron-up'></i></a>";
                            $tombol_status .= "<button class='btn btn-sm btn-secondary'>Selesai operasi</button>";
                        }
                        $tombol_status .= '</div>';
                    }
                } else {
                    $id = "reservasi/{$t->id_reservasi}";
                    $disabled = 'disabled';
                }
                // akhir status penjadwalan

                // Mengatur id berdasarkan penjadwalan atau reservasi
                $id = isset($t->id_penjadwalan) ? 'penjadwalan/' . $t->id_penjadwalan : 'reservasi' . $t->id_reservasi;

                $data[] = [
                    ++$no . '.',
                    $t->kamar_operasi ?? '-',
                    $t->tujuan_rs?? '-',
                    isset($t->tgl_dibuat) ? date('d/m/Y, H.i.s', strtotime($t->tgl_dibuat)) : '-',
                    $t->nama,
                    $t->norm,
                    $t->tgl_lahir_umur,
                    $ruang_rawat,
                    $t->diagnosis ?? '-',
                    $t->tindakan ?? '-',
                    !empty(trim($t->dokter_operator)) ? $t->dokter_operator : '-',
                    !empty(trim($t->dokter_anestesi)) ? $t->dokter_anestesi : '-',
                    !empty(trim($t->catatan_khusus)) ? $t->catatan_khusus : '-',
                    $tombol_status,
                    "<div class='btn-group-vertical btn-group-sm' role='group'>
                        <a href='" . base_url('admin/penjadwalan/form/' . $id) . "' role='button' class='btn btn-primary tbl-ubah-penjadwalan $disabled' data-bs-toggle='tooltip' data-bs-placement='right' title='Buat atau ubah' $disabled_link>
                            <i class='bx bx-edit'></i>
                        </a>
                        <button type='button' class='btn btn-danger tbl-batal-penjadwalan' data-id='$t->id_penjadwalan' data-bs-toggle='modal' data-bs-target='#modalBatalPenjadwalan' $disabled>
                            <i class='bx bx-x'></i>
                        </button>
                    </div>"
                ];
            }
            // echo '<pre>';print_r($data);exit();

            $output = [
                'draw' => $draw,
                'recordsTotal' => $this->Hari_ini_model->hitung_semua($id_ruangan, $tujuanOperasi),
                'recordsFiltered' => $this->Hari_ini_model->hitung_tersaring($id_ruangan, $tujuanOperasi),
                'data' => $data
            ];
            // echo '<pre>';print_r($output);exit();
            // akhir tabel hari ini
        } elseif ($jenis == 'history') {
            // mulai tabel history
            $tabel = $this->History_model->ambil($mulai, $akhir, $id_ruangan, $tujuanOperasi);
            // echo '<pre>';print_r($tabel);exit();

            foreach ($tabel as $t) {
                // mulai ruang rawat
                if ($t->menunggu_konfirmasi_ruang == '6233') {
                    $ruang_rawat = "<p class='text-warning'>Menunggu Konfirmasi Ruang</p>";
                } elseif (isset($t->ruang_rawat)) {
                    $ruang_rawat = $t->ruang_rawat;
                } else {
                    $ruang_rawat = '-';
                }
                // akhir ruang rawat

                $data[] = [
                    ++$no . '.',
                    isset($t->tgl_operasi) ? date('d/m/Y', strtotime($t->tgl_operasi)) : '-',
                    $t->kamar_operasi ?? '-',
                    $t->tujuan_rs?? '-',
                    isset($t->tgl_dibuat) ? date('d/m/Y, H.i.s', strtotime($t->tgl_dibuat)) : '-',
                    $t->nama,
                    $t->norm,
                    $t->tgl_lahir_umur,
                    $ruang_rawat,
                    $t->diagnosis ?? '-',
                    $t->tindakan ?? '-',
                    !empty(trim($t->dokter_operator)) ? $t->dokter_operator : '-',
                    !empty(trim($t->dokter_anestesi)) ? $t->dokter_anestesi : '-',
                    !empty(trim($t->catatan_khusus)) ? $t->catatan_khusus : '-',
                ];
            }
            // echo '<pre>';print_r($data);exit();

            $output = [
                'draw' => $draw,
                'recordsTotal' => $this->History_model->hitung_semua($mulai, $akhir, $id_ruangan, $tujuanOperasi),
                'recordsFiltered' => $this->History_model->hitung_tersaring($mulai, $akhir, $id_ruangan, $tujuanOperasi),
                'data' => $data
            ];
            // echo '<pre>';print_r($output);exit();
            // akhir tabel history
        }
        // akhir periksa jenis

        echo json_encode($output);
    }

    public function isi_tabel_batal()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $draw = intval($post['draw']);
        $mulai = $post['mulai'] ?? null;
        $akhir = $post['akhir'] ?? null;
        $id_ruangan = $post['id_ruangan']?? null;
        $tujuanOperasi = $post['tujuanOperasi']?? null;
        $no = $post['start'];

        // mulai tabel
        $tabel = $this->Batal_operasi_model->ambil($mulai, $akhir, $id_ruangan, $tujuanOperasi);
        foreach ($tabel as $t) {
            // mulai ruang rawat
            if ($t->menunggu_konfirmasi_ruang == '6233') {
                $ruang_rawat = "<p class='text-warning'>Menunggu Konfirmasi Ruang</p>";
            } elseif (isset($t->ruang_rawat)) {
                $ruang_rawat = $t->ruang_rawat;
            } else {
                $ruang_rawat = '-';
            }
            // akhir ruang rawat

            $data[] = [
                ++$no . '.',
                isset($t->tgl_operasi) ? date('d/m/Y', strtotime($t->tgl_operasi)) : '-',
                $t->kamar_operasi,
                $t->tujuan_rs,
                $t->nama ?? '-',
                $t->norm ?? '-',
                $t->tgl_lahir_umur,
                $ruang_rawat,
                $t->diagnosis ?? '-',
                $t->tindakan ?? '-',
                !empty(trim($t->dokter_operator)) ? $t->dokter_operator : '-',
                !empty(trim($t->catatan_khusus)) ? $t->catatan_khusus : '-',
                !empty(trim($t->alasan_batal)) ? $t->alasan_batal : '-',
                date('d/m/Y, H.i.s', strtotime($t->cancel_at)),
                !empty(trim($t->dibatalkan_oleh)) ? $t->dibatalkan_oleh : '-',
                "<button type='button' class='btn btn-sm btn-success tombol-aktifkan-penjadwalan' data-id='$t->id_penjadwalan'>
                    <i class='bx bx-check'></i> Aktifkan
                </button>"
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Batal_operasi_model->hitung_semua($mulai, $akhir, $id_ruangan),
            'recordsFiltered' => $this->Batal_operasi_model->hitung_tersaring($mulai, $akhir, $id_ruangan),
            'data' => $data
        ];
        // akhir tabel

        echo json_encode($output);
    }

    public function cetak_gambar()
    {
        // Ambil parameter dari GET atau session
        $tglawal = $this->input->get('TGLAWAL') ?: $this->session->userdata('penjadwalan_mulai');
        $tglakhir = $this->input->get('TGLAKHIR') ?: $this->session->userdata('penjadwalan_akhir');
        $tujuan_rs = $this->input->get('TUJUAN_RS') ?: $this->session->userdata('tujuanOperasi');

        // Load model untuk mengambil data
        $this->load->model('admin/penjadwalan/Cetak_gambar_model');

        // Ambil data berdasarkan tanggal (1 gambar per tanggal)
        $data_operasi_by_date = $this->Cetak_gambar_model->get_data_operasi_by_date($tglawal, $tglakhir, $tujuan_rs);

        // Ambil unique dates untuk menentukan jumlah gambar
        $unique_dates = $this->Cetak_gambar_model->get_unique_dates($tglawal, $tglakhir, $tujuan_rs);

        // Fallback: jika get_unique_dates gagal, ambil dari data_operasi_by_date
        if (empty($unique_dates) && !empty($data_operasi_by_date)) {
            $unique_dates = array_keys($data_operasi_by_date);
            sort($unique_dates);
        }

        // Ambil nama penjamin operasi
        $penjamin_operasi = $this->Cetak_gambar_model->get_penjamin_operasi($tujuan_rs);

        $data = [
            'data_operasi_by_date' => $data_operasi_by_date,
            'unique_dates' => $unique_dates,
            'tglawal' => $tglawal,
            'tglakhir' => $tglakhir,
            'tujuan_rs' => $tujuan_rs,
            'penjamin_operasi' => $penjamin_operasi
        ];

        // Load view standalone tanpa wrapper untuk menghindari error session
        $this->load->view('admin/penjadwalan/cetak_gambar_standalone', $data);
    }

    // Method untuk test subreport data
    public function test_subreport($id_pendaftaran_op = null)
    {
        if (!$id_pendaftaran_op) {
            echo "Parameter id_pendaftaran_op diperlukan";
            return;
        }

        $this->load->model('admin/penjadwalan/Cetak_gambar_model');
        $subreport_data = $this->Cetak_gambar_model->get_tindakan_operasi($id_pendaftaran_op);

        echo "<h3>Test Subreport Data untuk ID Pendaftaran Operasi: $id_pendaftaran_op</h3>";
        echo "<pre>";
        print_r($subreport_data);
        echo "</pre>";
    }

    public function form($jenis = null, $id_reservasi = null)
    {
        $mulai = $this->session->userdata('penjadwalan_mulai');
        $akhir = $this->session->userdata('penjadwalan_akhir');
        $id_ruangan = $this->session->userdata('penjadwalan_ruang');
        $hari = $this->session->userdata('penjadwalan_hari');
        $tujuanOperasi = $this->session->userdata('penjadwalan_tujuanOperasi');

    
        $data = [
            'judul' => 'Form Penjadwalan Operasi',
            'isi' => 'admin/penjadwalan/form',
            'session' => $this->session->get_userdata(),
            'ambil_norm' => $this->Waiting_list_model->ambil_norm(),
            'dokter' => $this->Dokter_model->dokter(),
            // 'kamar_operasi' => $this->Kamar_model->ambil(105090101),
           'kamar_operasi' => array_merge(
                $this->Kamar_model->ambil(105090101),
                $this->Kamar_model->ambil(105090104)
            ),
            'dokter_anestesi' => $this->Dokter_model->dokter_anestesi(),
            'jenis_anestesi' => $this->Referensi_model->referensi(622),
            'menunggu_konfirmasi_ruang' => $this->Referensi_model->referensi(1848),
            'ruang_rawat' => $this->Ruang_model->ambil(),
            'potong_beku' => $this->Referensi_model->referensi(1802),
            'join_operasi' => $this->Referensi_model->referensi(1851),
            'mulai' => $mulai,
            'akhir' => $akhir,
            'id_ruangan' => $id_ruangan,
            'hari' => $hari,

        ];

        // mulai periksa ID reservasi
        if (isset($jenis) && isset($id_reservasi)) {
            $detail = $this->Penjadwalan_model->detail($jenis, $id_reservasi);
            $data['detail'] = $detail;
            $data['dokter_lain'] = $this->Waiting_list_model->ambil_tindakan($detail['id_waiting_list_operasi']);
        }
        // akhir periksa ID reservasi
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    public function batal()
    {
        $this->db->trans_begin();
        $session = $this->session->get_userdata();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $data = [
            'alasan_batal' => $post['alasan_batal'] ?? null,
            'status' => 0,
            'cancel_at' => date('Y-m-d H:i:s'),
            'cancel_by' => $session['id']
        ];
        // echo '<pre>';print_r($data);exit();
        $this->Penjadwalan_model->ubah($post['id'], $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal membatalkan',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }

    public function simpan()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $ruang_rawat = null;
        $id_penjadwalan = $post['id_penjadwalan'] ?? null;
        // echo '<pre>';print_r($id_penjadwalan);exit();
        $session = $this->session->get_userdata();

        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            // mulai periksa validasi
            $this->form_validation->set_rules($this->Penjadwalan_model->rules());
            if (isset($post['menunggu_konfirmasi_ruang'])) {
                $ruang_rawat = ($post['menunggu_konfirmasi_ruang'] === '6234') ? $post['ruang_rawat'] : null;
            }
            // akhir periksa validasi

            if ($this->form_validation->run() == true) {
                // mulai data penjadwalan
                $data_penjadwalan = [
                    'id_waiting_list_operasi' => $post['id_waiting_list'],
                    'kamar_operasi' => $post['kamar_operasi'],
                    'tgl_rawat' => $post['tgl_rencanaMasuk'],
                    'tgl_operasi' => $post['tgl_operasi'],
                    'waktu_operasi' => $post['waktu_operasi'],
                    'dr_anestesi' => $post['dr_anestesi'] ?? null,
                    'jenis_anestesi' => $post['jenis_anestesi'],
                    'durasi_operasi' => $post['durasi_operasi'],
                    'menunggu_konfirmasi_ruang' => $post['menunggu_konfirmasi_ruang'] ?? null,
                    'ruang_rawat' => $ruang_rawat,
                    'tujuan_rs' => $post['tujuan']
                    
                ];
                // echo '<pre>';print_r($data_penjadwalan);exit();
                // akhir data penjadwalan

                // mulai simpan
                if (!empty($id_penjadwalan)) {
                    $this->Penjadwalan_model->ubah($id_penjadwalan, $data_penjadwalan);
                } else {
                    $data_penjadwalan['status'] = 1;
                    $data_penjadwalan['created_by'] = $session['id'];
                    // echo '<pre>';print_r($data_penjadwalan);exit();
                    $this->Penjadwalan_model->simpan($data_penjadwalan);
                }
                // akhir simpan

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array(),
                ];
            }

            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    public function ubah_status()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $status = $post['status'];
        $waktu = date('Y-m-d H:i:s');

        // mulai data
        $data = ['status' => $status];
        // akhir data

        // mulai status
        switch ($status) {
            case 2:
                $data['ruang_tunggu'] = $waktu;
                break;
            case 3:
                $data['sedang_operasi'] = $waktu;
                break;
            case 4:
                $data['ruang_observasi'] = $waktu;
                break;
            case 5:
                $data['selesai_operasi'] = $waktu;
                break;
        }
        // akhir status

        // echo '<pre>';print_r($data);exit();
        $this->Penjadwalan_model->ubah($post['id'], $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal mengubah status',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil mengubah status!',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }

    public function aktifkan()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $data = ['status' => 1];
        $this->Penjadwalan_model->ubah($post['id'], $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal mengaktifkan kembali',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil mengaktifkan kembali!',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }
    public function get_tujuan()
    {
        $this->load->model('referensi_model');
        $data = $this->referensi_model->get_tujuan_rs();
        echo json_encode($data);
    }

    // public function hapus_session() // nanti kalau nemu 
    // {
    //     if ($this->input->is_ajax_request()) {
    //         $this->session->unset_userdata([
    //             'penjadwalan_mulai',
    //             'penjadwalan_akhir',
    //             'penjadwalan_ruang',
    //             'penjadwalan_hari'
    //         ]);
    //         echo json_encode(['status' => true]);
    //     }
    // }
}

// End of File Penjadwalan.php
// Location: ./application/controllers/admin/Penjadwalan.php