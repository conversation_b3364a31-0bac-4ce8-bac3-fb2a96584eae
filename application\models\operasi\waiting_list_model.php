<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Waiting_list_model extends CI_Model
{
    protected $_table = 'medis.tb_waiting_list_operasi wlo';
    protected $_primary_key = 'wlo.id';
    protected $_order_by = 'wlo.status';
    protected $_order_by_type = 'asc';
    protected $_data_procedure = []; // Menyimpan data dari stored procedure

    protected $_urutan_kolom = [
        null,
        'nama_pasien',
        'norm',
        'jk',
        'tgl_lahir',
        'diagnosis',
        'rencana_tindakan',
        'tgl_daftar',
        'sifat_operasi',
        'dokter_bedah_lain',
        'catatan_khusus',
        'status'
    ];

    protected $_pencarian_kolom = [
        'master.getNamaLengkap(wlo.norm)',
        'wlo.norm',
        "IF(ps.JENIS_KELAMIN = 1, 'Laki-laki', 'Perempuan')",
        'ps.TANGGAL_LAHIR',
        "master.getCariUmurTahun(CURDATE(), DATE(ps.TANGGAL_LAHIR))",
        'wlo.diagnosis',
        'wlo.tindakan',
        'wlo.tanggal',
        'var.variabel',
        'master.getNamaLengkapPegawai(asisten_bedah.NIP)',
        'tpo.catatan_khusus',
        'wlo.status'
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function tabel($PDOKTER, $PTUJUAN_RS = '')
    {
        // Reset data procedure
        $this->_data_procedure = [];

        // Gunakan stored procedure
        $this->_data_procedure = $this->get_data_from_procedure($PDOKTER, $PTUJUAN_RS);

        // Jika ada pencarian, filter data dari procedure
        if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
            $this->_data_procedure = $this->filter_search_data($this->_data_procedure, $_POST['search']['value']);
        }

        // Jika ada ordering, urutkan data
        if (isset($_POST['order'])) {
            $this->_data_procedure = $this->sort_data($this->_data_procedure, $_POST['order']);
        }
    }

    /**
     * Ambil data dari stored procedure perjanjian.daftar_tunggu_operasi
     */
    private function get_data_from_procedure($PDOKTER, $PTUJUAN_RS = '')
    {
        try {
            // Panggil stored procedure dengan parameter sesuai test manual yang berhasil
            $sql = "CALL perjanjian.daftar_tunggu_operasi(?, ?)";
            $query = $this->db->query($sql, [$PDOKTER, $PTUJUAN_RS]);

            if (!$query) {
                return [];
            }

            $result = $query->result();

            // Handle stored procedure result sets
            while ($this->db->conn_id->more_results()) {
                $this->db->conn_id->next_result();
                if ($res = $this->db->conn_id->store_result()) {
                    $res->free();
                }
            }

            // Jika tidak ada data atau result bukan array, return array kosong
            if (empty($result) || !is_array($result)) {
                return [];
            }

            // Return data langsung dari procedure tanpa formatting berlebihan
            $formatted_result = [];
            foreach ($result as $row) {
                // Pastikan $row adalah object
                if (!is_object($row)) {
                    continue;
                }

                // Format dokter_bedah_lain sesuai kebutuhan controller
                $dokter_bedah_lain = isset($row->dokter_bedah_lain) ? $row->dokter_bedah_lain : '';
                if (!empty($dokter_bedah_lain) && $dokter_bedah_lain !== '[]') {
                    $dokter_bedah_lain = str_replace(['{id:', ', nama:'], ['{`id`:`', '`, `nama`:`'], $dokter_bedah_lain);
                    $dokter_bedah_lain = str_replace('}', '`}', $dokter_bedah_lain);
                }

                $formatted_result[] = (object) [
                    'nama_pasien' => $row->nama_pasien ?? '',
                    'norm' => $row->norm ?? '',
                    'jk' => $row->jk ?? '',
                    'tgl_lahir' => $row->tgl_lahir ?? '',
                    'umur' => $row->umur ?? '',
                    'diagnosis' => $row->diagnosis ?? '',
                    'rencana_tindakan' => $row->rencana_tindakan ?? '',
                    'tgl_daftar' => $row->tgl_daftar ?? '',
                    'sifat_operasi' => $row->sifat_operasi ?? '',
                    'dokter_bedah_lain' => $dokter_bedah_lain,
                    'catatan_khusus' => $row->catatan_khusus ?? '',
                    'status' => $row->status ?? '',
                    'tujuan_rs' => $row->TUJUAN_RS ?? '',
                    'ruangan' => '-'
                ];
            }

            return $formatted_result;

        } catch (Exception $e) {
            // Jika ada error, return array kosong
            log_message('error', 'Error in get_data_from_procedure: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Filter data berdasarkan pencarian
     */
    private function filter_search_data($data, $search_value)
    {
        if (empty($search_value)) {
            return $data;
        }

        $filtered_data = [];
        foreach ($data as $row) {
            // Cari di semua field yang bisa dicari
            $searchable_fields = [
                $row->nama_pasien,
                $row->norm,
                $row->jk,
                $row->tgl_lahir,
                $row->diagnosis,
                $row->rencana_tindakan,
                $row->sifat_operasi,
                $row->catatan_khusus
            ];

            foreach ($searchable_fields as $field) {
                if (stripos($field, $search_value) !== false) {
                    $filtered_data[] = $row;
                    break; // Jika sudah ketemu di satu field, tidak perlu cek field lain
                }
            }
        }

        return $filtered_data;
    }

    /**
     * Urutkan data berdasarkan order dari DataTable
     */
    private function sort_data($data, $order)
    {
        if (empty($data) || empty($order)) {
            return $data;
        }

        $column_index = $order[0]['column'];
        $direction = $order[0]['dir'];

        // Mapping kolom berdasarkan urutan di _urutan_kolom
        $sort_field = '';
        switch ($column_index) {
            case 1: $sort_field = 'nama_pasien'; break;
            case 2: $sort_field = 'norm'; break;
            case 3: $sort_field = 'jk'; break;
            case 4: $sort_field = 'tgl_lahir'; break;
            case 5: $sort_field = 'diagnosis'; break;
            case 6: $sort_field = 'rencana_tindakan'; break;
            case 7: $sort_field = 'tgl_daftar'; break;
            case 8: $sort_field = 'sifat_operasi'; break;
            case 9: $sort_field = 'dokter_bedah_lain'; break;
            case 10: $sort_field = 'catatan_khusus'; break;
            case 11: $sort_field = 'status'; break;
            default: $sort_field = 'tgl_daftar'; break;
        }

        if (!empty($sort_field)) {
            usort($data, function($a, $b) use ($sort_field, $direction) {
                $val_a = $a->$sort_field;
                $val_b = $b->$sort_field;

                if ($direction === 'asc') {
                    return strcmp($val_a, $val_b);
                } else {
                    return strcmp($val_b, $val_a);
                }
            });
        }

        return $data;
    }

    function ambil($PDOKTER, $PTUJUAN_RS = '')
    {
        // Ambil data dari procedure
        $this->tabel($PDOKTER, $PTUJUAN_RS);

        // Set default pagination
        $length = isset($_POST['length']) && $_POST['length'] > 0 ? (int)$_POST['length'] : 10;
        $start = isset($_POST['start']) && $_POST['start'] > 0 ? (int)$_POST['start'] : 0;

        // Pastikan _data_procedure adalah array yang valid
        if (!is_array($this->_data_procedure)) {
            return [];
        }

        // Ambil data yang sudah difilter dan diurutkan dari procedure
        $data = $this->_data_procedure;

        // Terapkan pagination
        $paginated_data = array_slice($data, $start, $length);

        // Pastikan return array yang valid
        return is_array($paginated_data) ? $paginated_data : [];
    }

    function hitung_tersaring($PDOKTER, $PTUJUAN_RS = '')
    {
        // Ambil data dari procedure
        $this->tabel($PDOKTER, $PTUJUAN_RS);

        // Pastikan _data_procedure adalah array sebelum count
        if (!is_array($this->_data_procedure)) {
            return 0;
        }

        // Return jumlah data yang sudah difilter
        return count($this->_data_procedure);
    }

    function hitung_semua($PDOKTER, $PTUJUAN_RS = '')
    {
        try {
            // Untuk stored procedure, ambil data tanpa filter pencarian
            $sql = "CALL perjanjian.daftar_tunggu_operasi(?, ?)";
            $query = $this->db->query($sql, [$PDOKTER, $PTUJUAN_RS]);

            if ($query) {
                $result = $query->result();

                // Pastikan result adalah array
                if (!is_array($result)) {
                    return 0;
                }

                $count = count($result);

                // Handle stored procedure result sets
                while ($this->db->conn_id->more_results()) {
                    $this->db->conn_id->next_result();
                    if ($res = $this->db->conn_id->store_result()) {
                        $res->free();
                    }
                }

                return $count;
            }

            return 0;

        } catch (Exception $e) {
            return 0;
        }
    }

}

// End of File Waiting_list_model.php
// Location: ./application/models/operasi/Waiting_list_model.php