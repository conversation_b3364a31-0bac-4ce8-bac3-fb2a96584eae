<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Daftar Tunggu</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item active" aria-current="page">Daftar tunggu</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <a class="btn btn-primary" href="<?= base_url('admin/Daftar_tunggu/form') ?>" role="button">Buat Baru</a>
        </div>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu dokter -->
<div class="card">
    <div class="card-body">
        <div class="row mb-3 align-items-center">
            <label for="dokterWl" class="col-sm-12 col-md-2 col-form-label">Dokter operator</label>
            <div class="col-sm-12 col-md-10">
                <select name="dokter_operasi" id="dokterWl" class="form-select single-select">
                    <option value="" selected disabled>Pilih Dokter</option>
                    <?php foreach ($dokter as $d) : ?>
                        <option id="dokterWl<?= $d['id_dokter'] ?>" value="<?= $d['id_dokter'] ?>">
                            <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                        </option>
                    <?php endforeach ?>
                </select>
            </div>
        </div>
        <div class="row mb-3 align-items-center d-none">
            <label for="ruangWl" class="col-2 col-form-label">Ruangan</label>
            <div class="col-sm-12 col-md-10">
                <select class="form-control form-control-sm select2" name="ruangWl" id="ruangWl" required>
                    <!-- <option value="0">Pilih Ruangan</option> -->
                </select>
            </div>
        </div>
        <div class="row mb-3 align-items-center">
            <label for="tujuanRs" class="col-2 col-form-label">Tujuan RS</label>
            <div class="col-10">
            <select class="form-control form-control-sm select2" name="tujuanRs" id="tujuanRs" >
            </select>
            </div>
        </div>
        <!-- akhir dokter bedah -->
        <!-- mulai judul tab -->
        <ul class="nav nav-pills" id="pills-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link tbl-wl" data-bs-toggle="pill" href="#tabSemuaTabelWl" role="tab" id="tblSemuaTabelWl" aria-selected="false" data-status="-1">Semua</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link tbl-wl" data-bs-toggle="pill" href="#tabTabelTungguWl" role="tab" id="tblTabelTungguWl" aria-selected="false" data-status="1">Menunggu Jadwal</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link tbl-wl" data-bs-toggle="pill" href="#tabTabelPraWl" role="tab" id="tblTabelPraWl" aria-selected="false" data-status="2">Pra Operasi</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link tbl-wl" data-bs-toggle="pill" href="#tabTabelPascaWl" role="tab" id="tblTabelPascaWl" aria-selected="false" data-status="3">Pasca Operasi</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link tbl-wl" data-bs-toggle="pill" href="#tabTabelBatalWl" role="tab" id="tblTabelBatalWl" aria-selected="false" data-status="0">Batal</a>
            </li>
        </ul>
        <!-- akhir judul tab -->
    </div>
</div>
<!-- akhir kartu dokter -->

<!-- mulai kartu tabel -->
<div class="card d-none" id="kartuTabelWl">
    <div class="card-body">
        <!-- mulai isi tab -->
        <div class="tab-content" id="pills-tabContent">
            <div class="tab-pane fade overflow-x-auto tabel-wl" id="tabSemuaTabelWl" role="tabpanel"></div>
            <div class="tab-pane fade overflow-x-auto tabel-wl" id="tabTabelTungguWl" role="tabpanel"></div>
            <div class="tab-pane fade overflow-x-auto tabel-wl" id="tabTabelPraWl" role="tabpanel"></div>
            <div class="tab-pane fade overflow-x-auto tabel-wl" id="tabTabelPascaWl" role="tabpanel"></div>
            <div class="tab-pane fade overflow-x-auto tabel-wl" id="tabTabelBatalWl" role="tabpanel"></div>
        </div>
        <!-- akhir isi tab -->
    </div>
</div>
<!-- akhir kartu tabel -->

<!-- mulai modal batal -->
<div class="modal fade" id="modalBatalWl" aria-labelledby="judulModalBatalWl" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalBatalWl">Konfirmasi Batal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Daftar tunggu pasien ini akan dibatalkan, apa Anda yakin?
                <input type="hidden" name="id" id="idWl">
                <div class="row mt-3 align-items-center">
                    <label for="keteranganWl" class="col-2 col-form-label">Keterangan</label>
                    <div class="col">
                        <textarea name="keterangan" id="keteranganWl" class="form-control form-control-sm" placeholder="Keterangan"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="tutupWl" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-danger" id="batalWl">Yakin batal</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal batal -->

<script>
    $(document).ready(function() {
        // mulai dokter
        $('#dokterWl').select2({
        });
        // akhir dokter

        // Disable/enable tab buttons based on doctor selection
        function updateTabButtons() {
            var dokterSelected = $('#dokterWl').val();
            if (!dokterSelected) {
                $('.tbl-wl')
                    .addClass('disabled')
                    .attr('title', 'Pilihlah dokter')        
                    .css({
                        'pointer-events': 'auto',            
                        'opacity': '0.6',
                        'cursor': 'not-allowed'            
                    });
            } else {
                $('.tbl-wl')
                    .removeClass('disabled')
                    .removeAttr('title')
                    .css({
                        'pointer-events': '',
                        'opacity': '',
                        'cursor': ''
                    });
            }
        }

        updateTabButtons();
        $('#dokterWl').on('change', function() {
            updateTabButtons();
        });
        $('#ruangWl').select2({
            placeholder: 'Pilih Ruangan',
            allowClear: true,
            ajax: {
                url: '<?= base_url('admin/perjanjian/get_ruangan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });
        $('#tujuanRs').select2({
            placeholder: 'Pilih Tujuan',
            allowClear: true,
            ajax: {
                url: '<?= base_url('admin/penjadwalan/get_tujuan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });

        // mulai aksi tab
        $('.tbl-wl').click(function() {
            let id_dokter = $('#dokterWl').val();
            let status = $(this).data('status');
            let id_ruang = $('#ruangWl').val();
            let tujuan = $('#tujuanRs').val();
            tampilTabelWl(id_dokter, status, id_ruang, tujuan);
        });
        // akhir aksi tab

        // mulai pilih dokter
        $('#dokterWl, #tujuanRs').change(function() {
            if ($('.tbl-wl').hasClass('active')) {
                let id_dokter = $('#dokterWl').val();
                let status = $('.tbl-wl.active').data('status');
                let id_ruang = $('#ruangWl').val();
                let tujuan = $('#tujuanRs').val();
                tampilTabelWl(id_dokter, status,  id_ruang, tujuan);
            }
        });
        // akhir pilih dokter

        // mulai tampil tabel
        function tampilTabelWl(id_dokter, status, id_ruang, tujuan) {
            $('#kartuTabelWl').removeClass('d-none');
            $('.tabel-wl, #tabelPasienWl').empty();
            $('#tabelPasienWl').DataTable().destroy();
            $('#tabelPasienWl').DataTable().ajax.reload();

            if (id_dokter !== null || id_dokter !== undefined) {
                $.ajax({
                    method: 'POST',
                    url: "<?= base_url('admin/Daftar_tunggu/tabel') ?>",
                    data: {
                        id_dokter: id_dokter,                       
                        status: status,
                        id_ruang: id_ruang,
                        tujuan: tujuan,
                    },
                    success: function(data) {
                        if (status === -1) {
                            $('#tabSemuaTabelWl').html(data);
                        } else if (status === 1) {
                            $('#tabTabelTungguWl').html(data);
                        } else if (status === 2) {
                            $('#tabTabelPraWl').html(data);
                        } else if (status === 3) {
                            $('#tabTabelPascaWl').html(data);
                        } else if (status === 0) {
                            $('#tabTabelBatalWl').html(data);
                        }
                    }
                });
            } else {
                Lobibox.notify('warning', {
                    icon: 'bx bx-error',
                    msg: 'Mohon pilih dokter terlebih dulu',
                    sound: false,
                    title: 'Peringatan'
                });
            }
        }
        // akhir tampil tabel

        // mulai tombol
        $(document).on('click', '.tombol-status-wl', function() {
            let id_waiting = $(this).data('waiting');
            let jenis = $(this).data('jenis');
            let status = $(this).data('status');

            $.ajax({
                url: "<?= base_url('admin/Daftar_tunggu/ubah_status') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id: id_waiting,
                    jenis: jenis,
                    status: status
                },
                success: function(data) {
                    if (data.status === 200) {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: data.message,
                            sound: false,
                            title: 'Berhasil'
                        });
                        $('#tabelPasienWl').DataTable().ajax.reload();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: data.message,
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir tombol

        // mulai batal
        $(document).on('click', '.tbl-batal-wl', function() {
            let id = $(this).data('id');
            $('#idWl').val(id);
        });
        // akhir batal

        // mulai yakin batal
        $('#batalWl').click(function() {
            $.ajax({
                url: "<?= base_url('admin/Daftar_tunggu/batal') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id: $('#idWl').val(),
                    keterangan: $('#keteranganWl').val(),
                },
                success: function(data) {
                    if (data.status === 200) {
                        $('#modalBatalWl').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: data.message,
                            sound: false,
                            title: 'Berhasil'
                        });
                        $('#tabelPasienWl').DataTable().ajax.reload();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: data.message,
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir yakin batal
    });
</script>