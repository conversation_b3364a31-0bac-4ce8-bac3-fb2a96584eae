<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Pendaftaran Pasien Baru Hari Ini</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item"><em>Dashboard</em></li>
                <li class="breadcrumb-item">Pendaftaran Pasien</li>
                <li class="breadcrumb-item active" aria-current="page">Pasien Baru Hari Ini</li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title"><?= date('d/m/Y') ?></h5>
        <hr />
        <!-- mulai tabel -->
        <div class="row mb-3 align-items-center overflow-x-auto">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover w-100" id="tabelPasienBaru">
                    <thead>
                        <tr>
                            <th class="text-center" scope="col">No.</th>
                            <th class="text-center" scope="col">Nama Pasien</th>
                            <th class="text-center" scope="col">No. RM</th>
                            <th class="text-center" scope="col">Tgl. Lahir</th>
                            <th class="text-center" scope="col">Diagnosis</th>
                            <th class="text-center" scope="col">Rencana Tindakan</th>
                            <th class="text-center" scope="col">Dokter Bedah</th>
                            <th class="text-center" scope="col">Dokter Bedah Lain</th>
                            <th class="text-center" scope="col">Catatan Khusus</th>
                            <th class="text-center" scope="col">Waktu Daftar</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th class="text-center" scope="col">No.</th>
                            <th class="text-center" scope="col">Nama Pasien</th>
                            <th class="text-center" scope="col">No. RM</th>
                            <th class="text-center" scope="col">Tgl. Lahir</th>
                            <th class="text-center" scope="col">Diagnosis</th>
                            <th class="text-center" scope="col">Rencana Tindakan</th>
                            <th class="text-center" scope="col">Dokter Bedah</th>
                            <th class="text-center" scope="col">Dokter Bedah Lain</th>
                            <th class="text-center" scope="col">Catatan Khusus</th>
                            <th class="text-center" scope="col">Waktu Daftar</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <!-- akhir tabel -->
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function() {
        // mulai tabel
        $('#tabelPasienBaru').DataTable({
            autoWidth: true,
            order: [9, 'desc'],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('operasi/pendaftaran_pasien/Pasien_baru/isi_tabel') ?>",
                type: 'POST'
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [0],
                orderable: false,
            }],
            // akhir pendefinisian kolom
        });
        // akhir tabel
    });
</script>