<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pasien_baru extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'admin/Waiting_list_model',
                'operasi/pendaftaran_pasien/Pasien_baru_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Pendaftaran Pasien Baru Hari Ini',
            'isi' => 'operasi/pendaftaran_pasien/pasien_baru/index',
            'session' => $this->session->get_userdata(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $dokter_lain = null;
        $draw = intval($post['draw']);
        $tabel = $this->Pasien_baru_model->ambil();
        $no = $post['start'];
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            // mulai dokter bedah lain
            $isi_dokter_lain = "<ul class='ps-3'>";
            $dokter_lain = str_replace("`", '"', trim($t->dokter_bedah_lain));
            foreach (json_decode($dokter_lain) as $a) {
                $isi_dokter_lain .= "<li>" . $a . "</li>";
            }
            $isi_dokter_lain .= '</ul>';
            // akhir dokter bedah lain

            $data[] = [
                ++$no . '.',
                "<a href='http://192.168.7.45/simrskd/medis/" . $t->id . "' target='_blank'>" . $t->nama . "</a>",
                $t->nomr,
                date('d/m/Y', strtotime($t->tgl_lahir)),
                $t->diagnosis,
                $t->rencana_tindakan,
                $t->dokter_bedah,
                $isi_dokter_lain,
                isset($t->catatan_khusus) && $t->catatan_khusus != '' ? $t->catatan_khusus : '-',
                date('d/m/Y, H.i.s', strtotime($t->waktu_daftar))
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Pasien_baru_model->hitung_semua(),
            'recordsFiltered' => $this->Pasien_baru_model->hitung_tersaring(),
            'data' => $data
        ];

        echo json_encode($output);
    }

    function pilih_pendaftaran_operasi($nomr)
    {
        $result = $this->Pasien_baru_model->pilih_pendaftaran_operasi($nomr);
        $data = [];
        foreach ($result as $r) {
            $sub_array = [];
            $sub_array['id'] = $r['id'];
            $sub_array['text'] = $r['nokun'] . ' - ' . $r['diagnosa_medis'] . ' - ' . $r['tanggal_operasi'];
            $data[] = $sub_array;
        }
        // echo '<pre>';print_r($data);exit();
        echo json_encode($data);
    }

    function ambil_pendaftaran_operasi()
    {
        $id = $this->input->post('id');
        // echo '<pre>';print_r($post);exit();
        $detail = $this->Pasien_baru_model->ambil_pendaftaran_operasi($id);
        $data = [
            'detail' => $detail,
            'tindakan' => $this->Waiting_list_model->ambil_tindakan($detail['id_waiting_list'])
        ];
        // echo '<pre>';print_r($data);exit();
        echo json_encode($data);
    }
}

// End of File Pasien_baru.php
// Location: ./application/controllers/operasi/pendaftaran_pasien/Pasien_baru.php