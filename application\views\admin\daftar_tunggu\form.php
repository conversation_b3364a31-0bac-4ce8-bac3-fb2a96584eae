<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Daftar Tunggu</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item">
                    <a href="<?= base_url('admin/Daftar_tunggu/index') ?>">Daftar Tunggu</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page"><?= isset($detail['id']) ? 'Ubah' : 'Buat' ?></li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai form -->
<form id="formWl" autocomplete="off">
    <!-- mulai kartu form -->
    <div class="card">
        <div class="card-body">
            <h5 class="card-title"><?= isset($detail['id']) ? 'Ubah' : 'Buat' ?> Daftar Tunggu</h5>
            <hr>
            <input type="hidden" name="id" id="idWl" value="<?= $detail['id'] ?? null ?>">
            <div class="row">
                <!-- mulai kolom 1 -->
                <div class="col">
                    <!-- mulai mr -->
                    <div class="row mb-3 align-items-center">
                        <label for="mrWl" class="col-sm-12 col-md-4 col-form-label">Nomor RM</label>
                        <div class="col-sm-12 col-md-8">
                            <div class="input-group input-group-sm">
                                <input type="text" name="norm" id="mrWl" class="form-control" placeholder="Masukkan nomor RM" maxlength="6" <?= isset($detail['norm']) ? "value='" . $detail['norm'] . "' readonly" : null ?>>
                                <button class="btn btn-outline-secondary" type="button" id="cariMrWl" <?= isset($detail['id']) ? 'disabled' : null ?>>
                                    <i class='bx bx-search'></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- akhir mr -->
                    <!-- mulai kunjungan -->
                    <div class="row mb-3 align-items-center">
                        <label for="kunjunganWl" class="col-sm-12 col-md-4 col-form-label">Nomor kunjungan</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="nokun" id="kunjunganWl" class="form-control form-control-sm" placeholder="Nomor kunjungan pasien" value="<?= $detail['nokun'] ?? null ?>" readonly>
                        </div>
                    </div>
                    <!-- akhir kunjungan -->
                    <!-- mulai nama -->
                    <div class="row mb-3 align-items-center">
                        <label for="namaWl" class="col-sm-12 col-md-4 col-form-label">Nama</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="nama" id="namaWl" class="form-control form-control-sm" placeholder="Nama pasien" value="<?= $detail['nama'] ?? null ?>" readonly>
                        </div>
                    </div>
                    <!-- akhir nama -->
                    <!-- mulai tanggal lahir -->
                    <div class="row mb-3 align-items-center">
                        <label for="tglLahirWl" class="col-sm-12 col-md-4 col-form-label">Tanggal lahir</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="date" name="tgl_lahir" id="tglLahirWl" class="form-control form-control-sm" placeholder="Tanggal lahir pasien" value="<?= isset($detail['tgl_lahir']) ? date('Y-m-d', strtotime($detail['tgl_lahir'])) : null ?>" readonly>
                        </div>
                    </div>
                    <!-- akhir tanggal lahir -->
                    <!-- mulai umur -->
                    <div class="row mb-3 align-items-center">
                        <label for="umurWl" class="col-sm-12 col-md-4 col-form-label">Umur</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="number" name="umur" id="umurWl" class="form-control form-control-sm" placeholder="Umur pasien" value="<?= $detail['umur'] ?? null ?>" readonly>
                        </div>
                    </div>
                    <!-- akhir umur -->
                    <!-- mulai pendaftaran pra operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="ppoWl" class="col-sm-12 col-md-4 col-form-label">Pendaftaran pra operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="ppo" id="ppoWl" class="form-select single-select" <?= isset($detail['id']) ? null : 'disabled' ?>></select>
                        </div>
                    </div>
                    <!-- akhir pendaftaran pra operasi -->
                    <!-- mulai dokter -->
                    <div class="row align-items-center">
                        <label for="dokterWl" class="col-sm-12 col-md-4 col-form-label">Dokter operator</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="dokter" id="dokterWl" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($dokter as $d): ?>
                                    <option id="dokterWl<?= $d['id_dokter'] ?>" value="<?= $d['id_dokter'] ?>">
                                        <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir dokter -->
                </div>
                <!-- akhir kolom 1 -->
                <!-- mulai kolom 2 -->
                <div class="col">
                    <!-- mulai diagnosis -->
                    <div class="row mb-3 align-items-center">
                        <label for="diagnosisWl" class="col-sm-12 col-md-4 col-form-label">Diagnosis</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="text" name="diagnosis" id="diagnosisWl" class="form-control form-control-sm" placeholder="Diagnosis pasien" value="<?= $detail['diagnosis'] ?? null ?>">
                        </div>
                    </div>
                    <!-- akhir diagnosis -->
                    <!-- mulai tindakan -->
                    <div class="row mb-3">
                        <label for="tindakanWl" class="col-sm-12 col-md-4 col-form-label">Tindakan</label>
                        <div class="col-sm-12 col-md-8">
                            <textarea name="tindakan" id="tindakanWl" class="form-control form-control-sm" placeholder="Tindakan operasi"><?= $detail['tindakan'] ?? null ?></textarea>
                        </div>
                    </div>
                    <!-- akhir tindakan -->
                    <!-- mulai tujuan operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanOperasiWl" class="col-sm-12 col-md-4 col-form-label">Tujuan operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="tujuan_operasi" id="tujuanOperasiWl" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($tujuan_operasi as $so): ?>
                                    <option id="tujuanOperasiWl<?= $so['id_variabel'] ?>" value="<?= $so['id_variabel'] ?>">
                                        <?= $so['variabel'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir tujuan operasi -->
                    <!-- mulai sifat operasi -->
                    <div class="row mb-3 align-items-center">
                        <label for="sifatOperasiWl" class="col-sm-12 col-md-4 col-form-label">Sifat operasi</label>
                        <div class="col-sm-12 col-md-8">
                            <select name="sifat_operasi" id="sifatOperasiWl" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($sifat_operasi as $so): ?>
                                    <option id="sifatOperasiWl<?= $so['id_variabel'] ?>" value="<?= $so['id_variabel'] ?>">
                                        <?= $so['variabel'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- akhir sifat operasi -->
                    <!-- mulai tanggal -->
                    <div class="row mb-3 align-items-center">
                        <label for="tanggalWl" class="col-sm-12 col-md-4 col-form-label">Tanggal daftar</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="date" name="tanggal" id="tanggalWl" class="form-control form-control-sm" placeholder="Tanggal daftar" value="<?= isset($detail['tanggal']) ? date('Y-m-d', strtotime($detail['tanggal'])) : null ?>" <?= isset($detail['id']) ? 'readonly' : null ?>>
                        </div>
                    </div>
                    <!-- akhir tanggal -->
                    <!-- mulai tanggal rencana operasi baru -->
                    <div class="row mb-3 align-items-center">
                        <label for="tanggalWl" class="col-sm-12 col-md-4 col-form-label">Tgl. rencana operasi baru</label>
                        <div class="col-sm-12 col-md-8">
                            <input type="date" name="tgl_rencana" id="tanggalRencanaBaruWl" class="form-control form-control-sm" placeholder="Tanggal rencana operasi" value="<?= $detail['tgl_rencana'] ?? null ?>">
                        </div>
                    </div>
                    <!-- akhir tanggal rencana operasi baru -->
                    <!-- mulai join operasi -->
                    <div class="row align-items-center">
                        <label for="joinOperasiWl" class="col-sm-12 col-md-4 col-form-label"><em>Join</em> operasi</label>
                        <div class="col-sm-12 col-md-8 px-4">
                            <div class="row">
                                <?php foreach ($join_operasi as $jo): ?>
                                    <div class="col form-check form-check-inline">
                                        <input type="radio" name="join_operasi" id="joinOperasiWl<?= $jo['id_variabel'] ?>" class="form-check-input join-operasi-wl" value="<?= $jo['id_variabel'] ?>" <?= isset($detail['join_operasi']) ? ($jo['id_variabel'] == $detail['join_operasi'] ? 'checked' : null) : null ?>>
                                        <label for="joinOperasiWl<?= $jo['id_variabel'] ?>" class="form-check-label">
                                            <?= $jo['variabel'] ?>
                                        </label>
                                    </div>
                                <?php endforeach ?>
                            </div>
                        </div>
                    </div>
                    <!-- akhir join operasi -->
                </div>
            </div>
        </div>
    </div>
    <!-- akhir kartu form -->

    <!-- mulai kartu dokter lain -->
    <div class="card mb-3 <?= isset($detail['id']) ? null : 'd-none' ?>" id="cardDokterLainWl">
        <div class="card-body">
            <h5 class="card-title">Dokter Bedah Lain dan Tindakannya</h5>
            <hr>
            <table class="table table-striped table-bordered table-hover w-100" id="tabelDokterLainWl">
                <thead>
                    <tr>
                        <th class="text-center" scope="col">No.</th>
                        <th class="text-center" scope="col">Dokter</th>
                        <th class="text-center" scope="col">Tindakan</th>
                    </tr>
                <tbody id="isiDokterLainWl">
                    <?php
                    $i = 0;
                    if (isset($tindakan)) {
                        foreach ($tindakan as $t) {
                            ?>
                            <tr>
                                <td><?= ++$i ?>.</td>
                                <td><?= $t['dokter_bedah_lain'] ?></td>
                                <td><?= $t['rencana_tindakan'] ?></td>
                            </tr>
                            <?php
                        }
                    }
                    ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th class="text-center" scope="col">No.</th>
                        <th class="text-center" scope="col">Dokter</th>
                        <th class="text-center" scope="col">Tindakan</th>
                    </tr>
                </tfoot>
                </thead>
            </table>
        </div>
    </div>
    <!-- akhir kartu dokter lain -->

    <?php if (isset($detail['id'])): ?>
        <!-- mulai rencana operasi lama -->
        <div class="mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Rencana Operasi Sebelumnya</h5>
                    <hr>
                    <!-- mulai tabel -->
                    <table class="table table-striped table-bordered table-hover w-100" id="tabelRencanaWl">
                        <thead>
                            <tr>
                                <th class="text-center" scope="col">No.</th>
                                <th class="text-center" scope="col">Tgl. Rencana</th>
                                <th class="text-center" scope="col">Keterangan</th>
                                <th class="text-center" scope="col">Tgl. Dibuat</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                        <tfoot>
                            <tr>
                                <th class="text-center" scope="col">No.</th>
                                <th class="text-center" scope="col">Tgl. Rencana</th>
                                <th class="text-center" scope="col">Keterangan</th>
                                <th class="text-center" scope="col">Tgl. Dibuat</th>
                            </tr>
                        </tfoot>
                    </table>
                    <!-- akhir tabel -->
                </div>
            </div>
        </div>
        <!-- akhir rencana operasi lama -->

        <!-- mulai aksi -->
        <div class="row">
            <div class="col">
                <button type="button" class="btn btn-primary" id="ubahWl" data-bs-toggle="modal" data-bs-target="#modalUbahWl">
                    Simpan perubahan
                </button>
            </div>
        </div>
        <!-- akhir aksi -->

        <!-- mulai modal ubah -->
        <div class="modal fade" id="modalUbahWl" aria-labelledby="judulModalUbahWl" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="judulModalUbahWl">Konfirmasi Ubah</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        Daftar tunggu pasien ini akan diubah dan tanggal rencana operasi sebelumnya akan dibatalkan, apa Anda yakin?
                        <div class="row mt-3 align-items-center">
                            <label for="keteranganUbahWl" class="col-2 col-form-label">Keterangan</label>
                            <div class="col">
                                <textarea name="keterangan" id="keteranganUbahWl" class="form-control form-control-sm" placeholder="Keterangan"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="tutupUbahWl" data-bs-dismiss="modal">Tutup</button>
                        <button type="button" class="btn btn-warning" id="yakinUbahWl">Yakin ubah</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- akhir modal ubah -->

        <!-- mulai modal sudah operasi -->
        <div class="modal fade" id="modalSudahOperasiWl" aria-labelledby="judulModalSudahOperasiWl" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="judulModalSudahOperasiWl">Konfirmasi Sudah</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        Apa Anda yakin pasien ini sudah operasi?
                        <input type="hidden" id="idSudahOperasiWl" value="">
                        <div class="row mt-3 align-items-center">
                            <label for="keteranganSudahOperasiWl" class="col-2 col-form-label">Keterangan</label>
                            <div class="col">
                                <textarea name="keterangan" id="keteranganSudahOperasiWl" class="form-control form-control-sm" placeholder="Keterangan"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="tutupSudahOperasiWl" data-bs-dismiss="modal">Tutup</button>
                        <button type="button" class="btn btn-success" id="yakinSudahOperasiWl">Yakin sudah</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- akhir modal sudah operasi -->

        <!-- mulai modal batal operasi -->
        <div class="modal fade" id="modalBatalOperasiWl" aria-labelledby="judulModalBatalOperasiWl" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="judulModalBatalOperasiWl">Konfirmasi Batal</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        Apa Anda yakin untuk membatalkan operasi pasien ini?
                        <input type="hidden" id="idBatalOperasiWl" value="">
                        <div class="row mt-3 align-items-center">
                            <label for="keteranganBatalOperasiWl" class="col-2 col-form-label">Keterangan</label>
                            <div class="col">
                                <textarea name="keterangan" id="keteranganBatalOperasiWl" class="form-control form-control-sm" placeholder="Keterangan"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="tutupBatalOperasiWl" data-bs-dismiss="modal">Tutup</button>
                        <button type="button" class="btn btn-danger" id="yakinBatalOperasiWl">Yakin batal</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- akhir modal batal operasi -->
    <?php else: ?>
        <!-- mulai aksi -->
        <div class="row">
            <div class="col">
                <button type="button" class="btn btn-primary" id="simpanWl">Simpan</button>
            </div>
        </div>
        <!-- akhir aksi -->
    <?php endif ?>
</form>
<!-- akhir form -->


<script>
    $(document).ready(function () {
        // mulai periksa detail
        <?php if (isset($detail['id'])): ?>
            // mulai rencana lama
            $('#tabelRencanaWl').DataTable({
                autoWidth: true,
                order: [1, 'asc'],
                language: {
                    url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
                },

                processing: true,
                serverSide: true,
                iDisplayLength: 25,
                search: {
                    regex: true
                },

                // mulai ambil data
                ajax: {
                    url: "<?= base_url('admin/Daftar_tunggu/isi_tabel_rencana') ?>",
                    type: 'POST',
                    data: {
                        id: <?= $detail['id'] ?>
                    }
                },
                // akhir ambil data

                // mulai pendefinisian kolom
                columnDefs: [{
                    targets: [0, 3],
                    orderable: false,
                }],
                // akhir pendefinisian kolom
            });
            // akhir rencana lama
        <?php else: ?>
            // mulai cari data pasien
            $('#cariMrWl').click(function () {
                let norm = $('#mrWl').val();

                $('.form-select').val(0).trigger('change');
                $('#diagnosisWl, #tindakanWl, #tanggalWl, #tanggalRencanaBaruWl').val(null);
                $('.join-operasi-wl').prop('checked', false);
                $('#isiDokterLainWl').html(null);
                $('#cardDokterLainWl').addClass('d-none');
                $.ajax({
                    type: 'POST',
                    url: "<?= base_url('admin/Perjanjian/ambil_data_pasien') ?>",
                    data: {
                        norm: norm
                    },
                    success: function (data) {
                        if (data === null || data === '' || data === 'null') {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: 'Data tidak ditemukan',
                                sound: false,
                                title: 'Peringatan'
                            });
                            $('#namaWl').val(null);
                        } else {
                            Lobibox.notify('success', {
                                icon: 'bx bx-check-circle',
                                msg: 'Data ditemukan',
                                sound: false,
                                title: 'Berhasil'
                            });

                            // mulai ambil data
                            let json = JSON.parse(data);
                            $('#namaWl').val(json['nama']);
                            $('#tglLahirWl').val(json['tgl_lahir']);
                            $('#umurWl').val(json['umur']);
                            // akhir ambil data

                            // mulai pendaftaran pra operasi
                            $('#ppoWl').removeAttr('disabled').select2({
                                placeholder: 'Pilih pendaftaran pra operasi',
                                ajax: {
                                    url: "<?= base_url('operasi/pendaftaran_pasien/Pasien_baru/pilih_pendaftaran_operasi/') ?>" + norm,
                                    dataType: 'json',
                                    delay: 250,
                                    processResults: function (data) {
                                        return {
                                            results: data
                                        };
                                    },
                                    cache: true
                                }
                            });
                            // akhir pendaftaran pra operasi
                        }
                    }
                });
            });
            // mulai cari data pasien
        <?php endif ?>
        // akhir periksa detail

        // mulai pendaftaran pra operasi
        <?php if (isset($detail['id'])): ?>
            $('#ppoWl').select2().append("<option value='<?= $detail['id'] ?>'><?= $detail['nokun'] . ' - ' . $detail['diagnosa_medis'] . ' - ' . date('d/m/Y', strtotime($detail['tanggal_operasi'])) ?></option>").val("<?= $detail['id'] ?>").trigger('change'); // Pendaftaran pra operasi
        <?php else: ?>
            $('#ppoWl').select2().change(function () {
                let id = $(this).val();
                if ($.trim(id)) {
                    $.ajax({
                        type: 'POST',
                        url: "<?= base_url('operasi/pendaftaran_pasien/Pasien_baru/ambil_pendaftaran_operasi') ?>",
                        data: {
                            id: id,
                        },
                        success: function (data) {
                            if ($.trim(data)) {
                                // mulai ambil data
                                let json = JSON.parse(data);
                                let detail = json['detail'];

                                $('#kunjunganWl').val(detail['nokun']);
                                $('#dokterWl').val(detail['dokter_bedah']).trigger('change');
                                $('#diagnosisWl').val(detail['diagnosa_medis']);
                                $('#tindakanWl').val(detail['rencana_tindakan_operasi']);
                                $('#tujuanOperasiWl').val(detail['tujuan_operasi']).trigger('change');
                                $('#sifatOperasiWl').val(detail['sifat_operasi']).trigger('change');
                                $('#tanggalWl').val(detail['waktu_daftar']);

                                // mulai tanggal rencana operasi baru
                                let tanggal_rencana = null;
                                if (detail['tanggal_operasi'] === '0000-00-00') {
                                    tanggal_rencana = null;
                                } else {
                                    tanggal_rencana = detail['tanggal_operasi'];
                                }
                                $('#tanggalRencanaBaruWl').val(tanggal_rencana);
                                // akhir tanggal rencana operasi baru

                                // mulai join operasi
                                if (detail['join_operasi'] !== null && detail['join_operasi'] !== '') {
                                    $('#joinOperasiWl' + detail['join_operasi']).prop('checked', true);
                                } else {
                                    $('.join-operasi-wl').prop('checked', false);
                                }
                                // akhir join operasi

                                // mulai dokter lain
                                let tindakan = json['tindakan'];
                                let isi = null;
                                let i = 0;
                                let rencana_tindakan = null;

                                $('#isiDokterLainWl').html(null);
                                if (tindakan.length) {
                                    $.each(tindakan, function (j) {
                                        // mulai rencana tindakan
                                        if (tindakan[j].rencana_tindakan !== null) {
                                            rencana_tindakan = tindakan[j].rencana_tindakan;
                                        } else {
                                            rencana_tindakan = '-';
                                        }
                                        // akhir rencana tindakan

                                        // mulai isi
                                        isi = '<tr>' +
                                            '<td>' + ++i + ".<input type='hidden' class='isiIdDokterLainEditWl' name='dokter_bedah[]' value='" + tindakan[j].id_dokter_bedah_lain + "'></td>" +
                                            '<td>' + tindakan[j].dokter_bedah_lain + '</td>' +
                                            '<td>' + rencana_tindakan + '</td>' +
                                            '</tr>';
                                        $(isi).hide().appendTo('#isiDokterLainWl').fadeIn(1000);
                                        // akhir isi
                                    });
                                    $('#cardDokterLainWl').removeClass('d-none');
                                } else {
                                    $('#cardDokterLainWl').addClass('d-none');
                                }
                                // akhir dokter lain
                                // akhir ambil data
                            }
                        }
                    });
                }
            });
        <?php endif ?>
        // akhir pendaftaran pra operasi

        // mulai dokter
        $('#dokterWl').select2({
            placeholder: 'Pilih dokter'
        }).val("<?= $detail['id_dokter'] ?? 0 ?>").trigger('change');
        // akhir dokter

        // mulai tujuan operasi
        $('#tujuanOperasiWl').select2({
            placeholder: 'Pilih tujuan operasi'
        }).val("<?= $detail['tujuan_operasi'] ?? 0 ?>").trigger('change');
        // akhir tujuan operasi

        // mulai sifat operasi
        $('#sifatOperasiWl').select2({
            placeholder: 'Pilih sifat operasi'
        }).val("<?= $detail['sifat_operasi'] ?? 0 ?>").trigger('change');
        // akhir sifat operasi

        // mulai sudah operasi
        $(document).on('click', '.tblSudahOperasiWl', function () {
            let id = $(this).data('id');
            $('#idSudahOperasiWl').val(id);
        });
        // akhir sudah operasi

        // mulai batal operasi
        $(document).on('click', '.tblBatalOperasiWl', function () {
            let id = $(this).data('id');
            $('#idBatalOperasiWl').val(id);
        });
        // akhir batal operasi

        // mulai simpan
        $('#simpanWl, #yakinUbahWl, #yakinSudahOperasiWl, #yakinBatalOperasiWl').click(function () {
            let form = $('#formWl').serializeArray();

            // mulai periksa ubah atau simpan
            if ($('#idWl').val() !== null) {
                // mulai status
                if (this.id === 'yakinBatalOperasiWl') { // Batal
                    form.push({
                        name: 'status',
                        value: 0,
                    });
                    form.push({
                        name: 'keterangan',
                        value: $('#keteranganBatalOperasiWl').val()
                    });
                    form.push({
                        name: 'id_wlor',
                        value: $('#idBatalOperasiWl').val()
                    });
                } else if (this.id === 'simpanWl') { // Simpan
                    form.push({
                        name: 'status',
                        value: 1,
                    });
                } else if (this.id === 'yakinUbahWl') { // Ubah
                    form.push({
                        name: 'status',
                        value: 2,
                    });
                    form.push({
                        name: 'keterangan',
                        value: $('#keteranganUbahWl').val()
                    });
                } else if (this.id === 'yakinSudahOperasiWl') { // Sudah operasi
                    form.push({
                        name: 'status',
                        value: 3,
                    });
                    form.push({
                        name: 'keterangan',
                        value: $('#keteranganSudahOperasiWl').val()
                    });
                    form.push({
                        name: 'id_wlor',
                        value: $('#idSudahOperasiWl').val()
                    });
                }
                // akhir status
            }
            // akhir periksa ubah atau simpan

            $.ajax({
                url: "<?= base_url('admin/Daftar_tunggu/simpan') ?>",
                dataType: 'json',
                type: 'POST',
                data: $.param(form),
                success: function (data) {
                    if (data.status === 'success') {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Data berhasil disimpan',
                            sound: false,
                            title: 'Berhasil',
                        });
                        window.location.href = "<?= base_url('admin/Daftar_tunggu/index') ?>";
                    } else {
                        $.each(data.errors, function (index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                },

                error: function (jqXHR, textStatus, errorThrown) {
                    Lobibox.notify('error', {
                        icon: 'bx bx-x-circle',
                        msg: 'Internal Server Error!',
                        sound: false
                    });
                }
            });
        });
        // akhir simpan
    });
</script>