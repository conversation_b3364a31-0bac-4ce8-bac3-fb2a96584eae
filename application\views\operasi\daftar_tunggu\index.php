<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Daftar Tunggu Operasi</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item"><em>Dashboard</em></li>
                <li class="breadcrumb-item active" aria-current="page">Daftar Tunggu Operasi</li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Tabel <PERSON>sien</h5>
        <hr>
        <!-- mulai dokter operasi -->
        <div class="row mb-3 align-items-center">
            <label for="dokterDaftarTunggu" class="col-2 col-form-label">Dokter operasi</label>
            <div class="col-10">
                <select name="dokter_operasi" id="dokterDaftarTunggu" class="form-select single-select">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <!-- akhir dokter operasi -->
        <!-- mulai tujuan RS -->
        <div class="row mb-3 align-items-center">
            <label for="tujuanRsDaftarTunggu" class="col-2 col-form-label">Tujuan RS</label>
            <div class="col-10">
                <select name="tujuan_rs" id="tujuanRsDaftarTunggu" class="form-select single-select">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <!-- akhir tujuan RS -->
        <!-- mulai tabel pasien -->
        <div class="overflow-x-auto d-none" id="tampilTabelPasienDaftarTunggu"></div>
        <!-- akhir tabel pasien -->
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function () {
        // mulai dokter
        $('#dokterDaftarTunggu').select2({
            placeholder: 'Pilih dokter',
            allowClear: true,
            ajax: {
                url: '<?= base_url('operasi/Daftar_tunggu/get_dokter') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        }).change(function () {
            loadTabel();
        });
        // akhir dokter

        // mulai tujuan RS
        $('#tujuanRsDaftarTunggu').select2({
            placeholder: 'Pilih tujuan RS',
            allowClear: true,
            ajax: {
                url: '<?= base_url('operasi/Daftar_tunggu/get_tujuan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        }).change(function () {
            if ($('#dokterDaftarTunggu').val()) {
                loadTabel();
            }
        });
        // akhir tujuan RS

        function loadTabel() {
            var id_dokter = $('#dokterDaftarTunggu').val();
            var tujuan = $('#tujuanRsDaftarTunggu').val();

            if (id_dokter) {
                $.ajax({
                    method: 'POST',
                    url: "<?= base_url('operasi/Daftar_tunggu/tabel') ?>",
                    data: {
                        id_dokter: id_dokter,
                        tujuan: tujuan
                    },
                    success: function (data) {
                        $('#tampilTabelPasienDaftarTunggu').removeClass('d-none').html(data);
                    }
                });
            }
        }
    });
</script>