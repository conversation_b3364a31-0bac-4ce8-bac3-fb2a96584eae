<table class="table table-striped table-bordered table-hover w-100" id="tabelPasienWl">
    <thead>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <!-- <th class="text-center" scope="col"><PERSON><PERSON></th> -->
            <!-- <th class="text-center" scope="col">R<PERSON></th> -->
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col"><PERSON><PERSON>t <PERSON></th>
            <th class="text-center" scope="col"><PERSON>gal <PERSON>ft<PERSON></th>
            <th class="text-center" scope="col">Tanggal Rencana Opera<PERSON></th>
            <th class="text-center" scope="col">Waktu Dibuat</th>
            <th class="text-center" scope="col">Keterangan</th>
            <th class="text-center" scope="col">Catatan Khusus</th>
            <th class="text-center" scope="col">Status</th>
            <th class="text-center" scope="col">Aksi</th>
        </tr>
    </thead>
    <tbody></tbody>
    <tfoot>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col">Nama Pasien</th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col">Jenis Kelamin</th>
            <th class="text-center" scope="col">Tanggal Lahir</th>
            <!-- <th class="text-center" scope="col">Nomor Kunjungan</th> -->
            <!-- <th class="text-center" scope="col">Ruang Operasi</th> -->
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Sifat Operasi</th>
            <th class="text-center" scope="col">Tanggal Daftar</th>
            <th class="text-center" scope="col">Tanggal Rencana Operasi</th>
            <th class="text-center" scope="col">Waktu Dibuat</th>
            <th class="text-center" scope="col">Keterangan</th>
            <th class="text-center" scope="col">Catatan Khusus</th>
            <th class="text-center" scope="col">Status</th>
            <th class="text-center" scope="col">Aksi</th>
        </tr>
    </tfoot>
</table>

<script>
    $(document).ready(function() {
        // mulai tabel
        $('#tabelPasienWl').DataTable().destroy();
        setTimeout(function() {
            $('#tabelPasienWl').DataTable({
                autoWidth: true,
                order: [
                    [10, 'desc'],
                    [1, 'asc'],
                ],
                language: {
                    url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
                },

                processing: true,
                serverSide: true,
                iDisplayLength: 25,
                search: {
                    regex: true
                },

                // mulai ambil data
                ajax: {
                    url: "<?= base_url('admin/Daftar_tunggu/isi_tabel') ?>",
                    type: 'POST',
                    data: {
                        id_dokter: <?= $id_dokter ?>,
                        status: <?= $status ?>,
                        id_ruang: '<?= $id_ruang?? null?>',                        
                        tujuan: '<?= $tujuan ?? null?>',
                    }
                },
                // akhir ambil data

                // mulai pendefinisian kolom
                columnDefs: [{
                    targets: [0, 14, 15],
                    orderable: false,
                }],
                // akhir pendefinisian kolom
            });
        }, 10);
        // akhir tabel
    });
</script>