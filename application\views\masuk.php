<!doctype html>
<html lang="id">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!--favicon-->
    <link rel="icon" href="<?= base_url('assets/images/logo.png') ?>" type="image/png" rel="icon" />
    <!--plugins-->
    <link href="<?= base_url('assets/plugins/notifications/css/lobibox.min.css') ?>" rel="stylesheet">
    <!-- loader-->
    <link href="<?= base_url('assets/css/pace.min.css') ?>" rel="stylesheet" />
    <script src="<?= base_url('assets/js/pace.min.js') ?>"></script>
    <!-- Bootstrap CSS -->
    <link href="<?= base_url('assets/css/bootstrap.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/bootstrap-extended.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/fonts.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/app.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/boxicons.css') ?>" rel="stylesheet">
    <!-- Theme Style CSS -->
    <link href="<?= base_url('assets/css/dark-theme.css') ?>" rel="stylesheet" />

    <title>Sippo | Masuk</title>
</head>

<body class="bg-lock-screen">
    <!--wrapper-->
    <div class="wrapper">
        <div class="section-authentication-signin d-flex align-items-center justify-content-center my-5 my-lg-0">
            <div class="container-fluid">
                <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3">
                    <div class="col mx-auto">
                        <div class="mb-4 text-center">
                            <img src="<?= base_url('assets/images/dharmais.png') ?>" class="img-fluid" alt="Logo Dharmais" />
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <div class="border p-4 rounded">
                                    <div class="text-center">
                                        <h3>Sippo</h3>
                                        <p>Lengkapi data di bawah untuk masuk</p>
                                    </div>
                                    <div class="form-body">
                                        <form method="post" id="formMasuk" class="row g-3">
                                            <div class="col-12">
                                                <label for="namaPenggunaMasuk" class="form-label">Nama pengguna</label>
                                                <input type="text" name="username" class="form-control" id="namaPenggunaMasuk" placeholder="Nama pengguna">
                                            </div>
                                            <div class="col-12">
                                                <label for="kataSandiMasuk" class="form-label">Kata sandi</label>
                                                <div class="input-group" id="lihatKataSandiMasuk">
                                                    <input type="password" name="password" class="form-control border-end-0" id="kataSandiMasuk" placeholder="Kata sandi">
                                                    <a href="javascript:;" class="input-group-text bg-transparent">
                                                        <i class='bx bx-hide'></i>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="row">
                                                    <label for="captchaMasuk" class="form-label">Captcha</label>
                                                    <div class="col-auto" id="gambarCaptchaMasuk">
                                                        <?= $captcha ?>
                                                    </div>
                                                    <div class="col">
                                                        <div class="input-group">
                                                            <input type="text" name="captcha" id="captchaMasuk" class="form-control border-end-0" placeholder="Tuliskan captcha" maxlength="4">
                                                            <a href="javascript:;" class="input-group-text bg-transparent" id="gantiCaptchaMasuk">
                                                                <i class='bx bx-shuffle'></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="d-grid">
                                                    <button type="submit" class="btn btn-primary" id="tblMasuk"><i class="bx bx-log-in"></i>Masuk</button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--end row-->
            </div>
        </div>
    </div>
    <!--end wrapper-->
    <!-- Bootstrap JS -->
    <script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
    <!--plugins-->
    <script src="<?= base_url('assets/js/jquery.min.js') ?>"></script>
    <!--notification js -->
    <script src="<?= base_url('assets/plugins/notifications/js/lobibox.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/notifications/js/notifications.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/notifications/js/notification-custom-script.js') ?>"></script>
    <script>
        $(document).ready(function () {
            // mulai lihat kata sandi
            $('#lihatKataSandiMasuk a').click(function (event) {
                event.preventDefault();
                if ($('#lihatKataSandiMasuk input').attr('type') == 'text') {
                    $('#lihatKataSandiMasuk input').attr('type', 'password');
                    $('#lihatKataSandiMasuk i').addClass('bx-hide');
                    $('#lihatKataSandiMasuk i').removeClass('bx-show');
                } else if ($('#lihatKataSandiMasuk input').attr('type') == 'password') {
                    $('#lihatKataSandiMasuk input').attr('type', 'text');
                    $('#lihatKataSandiMasuk i').removeClass('bx-hide');
                    $('#lihatKataSandiMasuk i').addClass('bx-show');
                }
            });
        });
        // akhir lihat kata sandi

        // mulai ganti captcha
        $('#gantiCaptchaMasuk').click(function () {
            $.get("<?= base_url('Akun/ganti_captcha') ?>", function (data) {
                $('#captchaMasuk').val(null);
                $('#gambarCaptchaMasuk').html(data);
            });
        });
        // akhir ganti captcha

        // mulai masuk
        $('#formMasuk').submit(function (event) {
            event.preventDefault();
            let form = $(this)[0];
            let form_data = new FormData(form);

            $.ajax({
                url: "<?= base_url('Akun/masuk') ?>",
                type: 'POST',
                processData: false,
                contentType: false,
                data: form_data,
                dataType: 'json',

                success: function (data) {
                    if (data.status === 'success') {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Akses diterima',
                            sound: false,
                            title: 'Berhasil',
                        });
                        window.location.href = "<?= base_url('beranda') ?>";
                    } else {
                        $.each(data.errors, function (index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                },

                error: function (jqXHR, textStatus, errorThrown) {
                    Lobibox.notify('error', {
                        icon: 'bx bx-x-circle',
                        msg: 'Internal Server Error!',
                        sound: false
                    });
                }
            });
        });
        // akhir masuk
    </script>
</body>

</html>