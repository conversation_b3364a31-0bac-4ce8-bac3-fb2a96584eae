<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Sop_model extends CI_Model
{
    protected $_urutan_kolom = [
        'e.ID_EDOC',
        'e.NOMOR_REVISI',
        'e.NAMA_EDOC',
        'e.TGL_TERBIT',
    ];

    protected $_pencarian_kolom = [
        'e.NOMOR_EDOC',
        'e.NOMOR_REVISI',
        'e.NAMA_EDOC',
        'e.TGL_TERBIT',
    ];

    public function get_sop()
    {
        $this->db2->select('e.ID_EDOC, e.NOMOR_REVISI, e.NAMA_EDOC, e.FILE_EDOC, e.NOMOR_EDOC, e.TGL_TERBIT, ek.NAMA_KATEGORI_EDOC');
        $this->db2->from('dbsdm.edoc e');
        $this->db2->join("dbsdm.edoc_kategori ek", "e.ID_KATEGORI_EDOC = ek.ID_KATEGORI_EDOC", "LEFT");
        $this->db2->where('e.CREATED_BY', 2017);
        $this->db2->where('(e.TGL_BERAKHIR IS NULL OR TGL_BERAKHIR="0000-00-00" OR e.TGL_BERAKHIR> NOW())');
        $this->db2->where('ek.NAMA_KATEGORI_EDOC = "SPO"');

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db2->group_start();
                    $this->db2->like($pk, $_POST['search']['value']);
                } else {
                    $this->db2->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db2->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db2->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $this->db2->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil()
    {
        $this->get_sop();
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db2->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db2->get();
        // print_r($this->db2->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring()
    {
        $this->get_sop();
        $query = $this->db2->get();
        return $query->num_rows();
    }

    function hitung_semua()
    {
        $this->get_sop();
        return $this->db2->count_all_results();
    }
}

// End of File Sop_model.php
// Location: ./application/models/operasi/Sop_model.php