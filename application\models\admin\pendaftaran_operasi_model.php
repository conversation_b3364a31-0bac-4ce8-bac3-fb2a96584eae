<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pendaftaran_operasi_model extends CI_Model
{
    protected $_table = 'medis.tb_pendaftaran_operasi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'asc';
    protected $_urutan = ['id' => 'asc'];

    public function __construct()
    {
        parent::__construct();
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
        return $this->db->insert_id();
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_pendaftaran_operasi.id', $id);
        $this->db->update($this->_table, $data);
    }
}

// End of File Pendaftaran_operasi_model.php
// Location: ./application/models/admin/Pendaftaran_operasi_model.php