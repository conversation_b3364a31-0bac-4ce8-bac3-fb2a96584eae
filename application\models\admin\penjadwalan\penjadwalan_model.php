<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Penjadwalan_model extends CI_Model
{
    function __construct()
    {
        parent::__construct();
    }

    function rules()
    {
        return [
            [
                'field' => 'kamar_operasi',
                'label' => 'Kamar operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
            [
                'field' => 'waktu_operasi',
                'label' => 'Waktu operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
            [
                'field' => 'durasi_operasi',
                'label' => 'Durasi operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ]
        ];
    }

    function detail($jenis, $id)
    {
        // $this->db->select(
        //     'ppo.id id_penjadwalan, rmp.id id_perjanjian, rr.id id_reservasi, rr.id_waiting_list_operasi,
        //     tpo.id id_tpo, rr.id_jk, rr.norm, rr.nama, rr.no_telp, rr.id_dokter, rmp.DIAGNOSA diagnosis,
        //     rmp.TINDAKAN tindakan, rr.tgl_rencanaMasuk tgl_rawat,
        //     IF(ppo.tgl_operasi IS NULL, rmp.TANGGAL, ppo.tgl_operasi) tgl_operasi, ppo.kamar_operasi,
        //     LEFT(ppo.waktu_operasi, 5) waktu_operasi, ppo.durasi_operasi, ppo.dr_anestesi,
        //     IF(ppo.jenis_anestesi IS NULL, tpo.rencana_jenis_pembiusan, ppo.jenis_anestesi) jenis_anestesi,
        //     tpo.rencana_jenis_pembiusan_lain, ppo.menunggu_konfirmasi_ruang, ppo.ruang_rawat, tpo.nokun,
        //     tpo.diagnosa_medis, tpo.tanggal_operasi, tpo.rencana_jenis_pembiusan, tpo.perkiraan_lama_operasi,
        //     tpo.potong_beku, tpo.join_operasi, ppo.tujuan_rs'
        // );
        // $this->db->from('perjanjian.penjadwalan_operasi ppo');
        // $this->db->join(
        //     'db_reservasi.tb_reservasi rr',
        //     'rr.id_waiting_list_operasi = ppo.id_waiting_list_operasi',
        //     'right'
        // );
        // $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID = rr.id_perjanjian', 'left');
        // $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = rr.id_pendaftaran_operasi', 'left');
        $this->db->select([
            'ppo.id AS id_penjadwalan',
            'rmp.id AS id_perjanjian',
            'rr.id AS id_reservasi',
            'wlo.id AS id_waiting_list_operasi',
            'tpo.id AS id_tpo',
            'mr.JENIS_KELAMIN AS id_jk',
            'mr.NORM AS norm',
            'master.getNamaLengkap(mr.NORM) AS nama',
            'kp.NOMOR AS no_telp',
            'wlo.id_dokter',
            'tpo.diagnosa_medis AS diagnosis',
            'tpo.rencana_tindakan_operasi AS tindakan',
            'rr.tgl_rencanaMasuk AS tgl_rawat',
            'IF(ppo.tgl_operasi IS NULL, rmp.TANGGAL, ppo.tgl_operasi) AS tgl_operasi',
            'ppo.kamar_operasi',
            // 'LEFT(ppo.waktu_operasi, 5) AS waktu_operasi',
            'TIME_FORMAT(ppo.waktu_operasi, "%H:%i") AS waktu_operasi',
            'ppo.durasi_operasi',
            'ppo.dr_anestesi',
            'IF(ppo.jenis_anestesi IS NULL, tpo.rencana_jenis_pembiusan, ppo.jenis_anestesi) AS jenis_anestesi',
            'tpo.rencana_jenis_pembiusan_lain',
            'ppo.menunggu_konfirmasi_ruang',
            'ppo.ruang_rawat',
            'tpo.nokun',
            'tpo.diagnosa_medis',
            'tpo.tanggal_operasi',
            'tpo.rencana_jenis_pembiusan',
            'tpo.perkiraan_lama_operasi',
            'tpo.potong_beku',
            'tpo.join_operasi',
            "IF(ppo.tujuan_rs IS NULL, IF(rr.id_cara_bayar = 2, 2, 16), ppo.tujuan_rs) AS tujuan_rs"
        ]);
        
        $this->db->from('medis.tb_waiting_list_operasi wlo');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
        $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
        $this->db->join('master.pasien mr', 'mr.NORM = wlo.norm', 'left');
        $this->db->join('master.kontak_pasien kp', 'kp.NORM = mr.NORM AND kp.JENIS = 3', 'left');

        if ($jenis == 'penjadwalan') {
            $this->db->where('ppo.id', $id);
        } elseif ($jenis == 'reservasi') {
            $this->db->where('rr.id', $id);
        }

        $query = $this->db->get();
        return $query->row_array();
    }

    function simpan($data)
    {
        $this->db->insert('perjanjian.penjadwalan_operasi', $data);
    }

    function ubah($id, $data)
    {
        $this->db->where('perjanjian.penjadwalan_operasi.id', $id);
        $this->db->update('perjanjian.penjadwalan_operasi', $data);
    }
}

// End of File Penjadwalan_model.php
// Location: ./application/models/Penjadwalan_model.php