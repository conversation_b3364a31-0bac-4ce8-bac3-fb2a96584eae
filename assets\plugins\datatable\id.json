{"emptyTable": "Tidak ada data yang tersedia pada tabel ini", "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri", "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri", "infoFiltered": "(disaring dari _MA<PERSON>_ entri <PERSON>)", "lengthMenu": "<PERSON><PERSON><PERSON><PERSON> _MENU_ entri", "loadingRecords": "Sedang memuat...", "processing": "Sedang memproses...", "search": "Cari:", "zeroRecords": "Tidak ditemukan data yang sesuai", "paginate": {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "next": "Selanjutnya", "previous": "Sebelumnya"}, "aria": {"sortAscending": ": aktifkan untuk mengurutkan kolom ke atas", "sortDescending": ": aktifkan untuk mengurutkan kolom menurun"}, "autoFill": {"fill": "<PERSON>i semua sel dengan <i>%d</i>", "fillHorizontal": "<PERSON>i sel secara horizontal", "fillVertical": "<PERSON>i sel secara vertikal", "cancel": "<PERSON><PERSON>", "info": "Info"}, "buttons": {"collection": "Kumpulan <span class='ui-button-icon-primary ui-icon ui-icon-triangle-1-s'/>", "colvis": "Visibilitas Kolom", "colvisRestore": "Kembalikan visibilitas", "copy": "<PERSON><PERSON>", "copySuccess": {"_": "%d baris disalin ke papan klip", "1": "satu baris disalin ke papan klip"}, "copyTitle": "<PERSON><PERSON> ke Papan klip", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "<PERSON><PERSON><PERSON><PERSON> semua baris", "_": "Tampilkan %d baris", "1": "<PERSON><PERSON><PERSON><PERSON> satu baris"}, "pdf": "PDF", "print": "Cetak", "copyKeys": "Tekan ctrl atau u2318 + C untuk menyalin tabel ke papan klip.<br /><br />Untuk membatalkan, klik pesan ini atau tekan esc.", "createState": "Tambahkan Data", "removeAllStates": "Hapus Semua Data", "removeState": "Hapus Data", "renameState": "<PERSON><PERSON><PERSON>", "savedStates": "SImpan Data", "stateRestore": "Publihkan Data", "updateState": "Perbaharui data"}, "searchBuilder": {"add": "Tambah Kondisi", "button": {"0": "Cari Builder", "_": "Cari Builder (%d)"}, "clearAll": "<PERSON><PERSON><PERSON><PERSON>", "condition": "<PERSON><PERSON><PERSON>", "data": "Data", "deleteTitle": "Hapus filter", "leftTitle": "<PERSON>", "logicAnd": "<PERSON>", "logicOr": "<PERSON><PERSON>", "rightTitle": "<PERSON>", "title": {"0": "Cari Builder", "_": "Cari Builder (%d)"}, "value": "<PERSON><PERSON>", "conditions": {"date": {"after": "Setela<PERSON>", "before": "Sebelum", "between": "Diantara", "empty": "Kosong", "equals": "<PERSON><PERSON> den<PERSON>", "not": "Tidak sama", "notBetween": "Tidak diantara", "notEmpty": "Tidak kosong"}, "number": {"empty": "Kosong", "equals": "<PERSON><PERSON> den<PERSON>", "gt": "<PERSON><PERSON><PERSON> besar dari", "gte": "<PERSON><PERSON><PERSON> besar atau sama dengan", "lt": "<PERSON><PERSON><PERSON> kecil dari", "lte": "<PERSON><PERSON><PERSON> kecil atau sama dengan", "not": "Tidak sama", "notEmpty": "Tidak kosong", "between": "Di <PERSON>ara", "notBetween": "Tidak di antara"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "Kosong", "endsWith": "<PERSON><PERSON><PERSON><PERSON>", "not": "Tidak sama", "notEmpty": "Tidak kosong", "startsWith": "<PERSON><PERSON><PERSON>", "equals": "<PERSON><PERSON> den<PERSON>", "notContains": "Tidak Berisi", "notStartsWith": "Tidak diawali <PERSON>", "notEndsWith": "Tidak <PERSON><PERSON>"}, "array": {"equals": "<PERSON><PERSON> den<PERSON>", "empty": "Kosong", "contains": "<PERSON><PERSON><PERSON>", "not": "Tidak", "notEmpty": "Tidak kosong", "without": "<PERSON><PERSON>"}}}, "searchPanes": {"count": "{total}", "countFiltered": "{shown} ({total})", "collapse": {"0": "<PERSON>ian", "_": "Panel Pencarian (%d)"}, "emptyPanes": "Tidak Ada Panel Pencarian", "loadMessage": "Memuat Panel Pencarian", "clearMessage": "<PERSON><PERSON><PERSON><PERSON>", "title": "Saringan Aktif - %d", "showMessage": "<PERSON><PERSON><PERSON><PERSON>", "collapseMessage": "Ciutkan"}, "infoThousands": ",", "datetime": {"previous": "Sebelumnya", "next": "Selanjutnya", "hours": "Jam", "minutes": "Menit", "seconds": "<PERSON><PERSON>", "unknown": "-", "amPm": ["am", "pm"], "weekdays": ["Min", "<PERSON>", "<PERSON>l", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>b"], "months": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "September", "Oktober", "November", "Desember"]}, "editor": {"close": "<PERSON><PERSON><PERSON>", "create": {"button": "Tambah", "submit": "Tambah", "title": "Tambah inputan baru"}, "remove": {"button": "Hapus", "submit": "Hapus", "confirm": {"_": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk menghapus %d baris?", "1": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk menghapus 1 baris?"}, "title": "<PERSON><PERSON><PERSON>"}, "multi": {"title": "<PERSON><PERSON><PERSON>", "info": "Item yang dipilih berisi nilai yang berbeda untuk input ini. Untuk mengedit dan mengatur semua item untuk input ini ke nilai yang sama, klik atau tekan di sini, jika tidak maka akan mempertahankan nilai masing-masing.", "restore": "Batalkan <PERSON>", "noMulti": "<PERSON><PERSON>kan ini dapat diubah satu per satu, tetapi bukan bagian dari grup."}, "edit": {"title": "<PERSON> inputan", "submit": "Edit", "button": "Edit"}, "error": {"system": "Te<PERSON><PERSON><PERSON> k<PERSON><PERSON>han pada system. (<a target=\"\\\" rel=\"\\ nofollow\" href=\"\\\">Informasi Selebihnya</a>)."}}, "stateRestore": {"creationModal": {"button": "Buat", "columns": {"search": "<PERSON><PERSON><PERSON>", "visible": "Visibilitas Kolom"}, "name": "Nama:", "order": "Penyortiran", "search": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "title": "Buat State Baru", "toggleLabel": "Termasuk:", "paging": "<PERSON><PERSON>", "scroller": "Posisi Skrol", "searchBuilder": "Cari Builder"}, "emptyError": "<PERSON>a tidak boleh kosong.", "removeConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus %s?", "removeJoiner": "dan", "removeSubmit": "Hapus", "renameButton": "G<PERSON>", "renameLabel": "Nama Baru untuk %s:", "duplicateError": "Nama State ini sudah ada.", "emptyStates": "Tidak ada State yang disimpan.", "removeError": "Gagal menghapus State.", "removeTitle": "Penghapusan State", "renameTitle": "Ganti nama State"}, "decimal": ",", "searchPlaceholder": "kata kunci pencarian", "select": {"cells": {"1": "1 sel dipilih", "_": "%d sel dipilih"}, "columns": {"1": "1 kolom dirpilih", "_": "%d kolom dipilih"}, "rows": {"1": "1 baris dipilih", "_": "%d baris dipilih"}}, "thousands": "."}