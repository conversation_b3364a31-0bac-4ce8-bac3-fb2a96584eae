<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Penjadwalan Operasi</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item active" aria-current="page">Penjadwalan Operasi</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <a class="btn btn-primary" href="<?= base_url('admin/Pen<PERSON>dwalan/form') ?>" role="button">B<PERSON><PERSON></a>
        </div>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <!-- mulai pilih tab -->
        <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" data-bs-toggle="tab" href="#tabDaftarPenjadwalan" role="tab" aria-selected="false">
                    <div class="d-flex align-items-center">
                        <div class="tab-title">Daftar Perjanjian Operasi</div>
                    </div>
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" data-bs-toggle="tab" href="#tabHariIniPenjadwalan" role="tab" aria-selected="false" id="tblHariIniPenjadwalan">
                    <div class="d-flex align-items-center">
                        <div class="tab-title">Operasi Hari Ini</div>
                    </div>
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" data-bs-toggle="tab" href="#tabHistoryPenjadwalan" role="tab" aria-selected="false">
                    <div class="d-flex align-items-center">
                        <div class="tab-title">History/Selesai Operasi</div>
                    </div>
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" data-bs-toggle="tab" href="#tabBatalReservasiPenjadwalan" role="tab" aria-selected="false">
                    <div class="d-flex align-items-center">
                        <div class="tab-title">Batal Operasi</div>
                    </div>
                </a>
            </li>
        </ul>
        <!-- akhir pilih tab -->

        <!-- mulai isi tab -->
        <div class="tab-content py-3">
            <div class="tab-pane fade show active" id="tabDaftarPenjadwalan" role="tabpanel">
                <!-- mulai form -->
                <form id="formDaftarPenjadwalan">
                    <!-- mulai periode -->
                    <div class="row mb-3 align-items-center">
                        <label for="periodeDaftarPenjadwalan" class="col-2 col-form-label">Periode tanggal operasi</label>
                        <div class="col">
                            <div class="input-group">
                                <span class="input-group-text">dari</span>
                                <input type="date" name="mulai" id="mulaiDaftarPenjadwalan" class="form-control form-control-sm" placeholder="Mulai" aria-label="Mulai periode" value="<?= $mulai ?? '' ?>">
                                <span class="input-group-text">sampai</span>
                                <input type="date" name="akhir" id="akhirDaftarPenjadwalan" class="form-control form-control-sm" placeholder="Akhir" aria-label="Akhir periode" value="<?= $akhir ?? '' ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3 align-items-center d-none">
                        <label for="ruangan" class="col-2 col-form-label">Ruangan</label>
                        <div class="col-10">
                        <select class="form-control form-control-sm select2" name="jruangan" id="ruangan" required>
                            <option value="">Pilih Ruangan</option>
                            <?php if(isset($ruangan) && !empty($ruangan)): ?>
                                <?php foreach($ruangan as $r): ?>
                                    <option value="<?= $r->ID ?>" <?= isset($id_ruangan) && $r->ID == $id_ruangan ? 'selected' : '' ?>>
                                        <?= $r->DESKRIPSI ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        </div>
                    </div>
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanOperasi" class="col-2 col-form-label">Tujuan RS</label>
                        <div class="col-10">
                            <select class="form-control form-control-sm select2" name="tujuanOperasi" id="tujuanOperasi" >
                            </select>
                        </div>
                    </div>

                    <!-- akhir periode -->
                    <div class="row row-cols-auto">
                        <div class="col me-auto">
                            <!-- mulai judul tab hari -->
                            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link tbl-daftar-penjadwalan" data-bs-toggle="pill" href="#tabSemuaDaftarPenjadwalan" role="tab" id="tblSemuaPenjadwalan" aria-selected="false" data-hari="All">Semua Hari</a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link tbl-daftar-penjadwalan" data-bs-toggle="pill" href="#tabSeninDaftarPenjadwalan" role="tab" id="tblSeninPenjadwalan" aria-selected="false" data-hari="Monday">Senin</a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link tbl-daftar-penjadwalan" data-bs-toggle="pill" href="#tabSelasaDaftarPenjadwalan" role="tab" id="tblSelasaPenjadwalan" aria-selected="false" data-hari="Tuesday">Selasa</a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link tbl-daftar-penjadwalan" data-bs-toggle="pill" href="#tabRabuDaftarPenjadwalan" role="tab" id="tblRabuPenjadwalan" aria-selected="false" data-hari="Wednesday">Rabu</a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link tbl-daftar-penjadwalan" data-bs-toggle="pill" href="#tabKamisDaftarPenjadwalan" role="tab" id="tblKamisPenjadwalan" aria-selected="false" data-hari="Thursday">Kamis</a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link tbl-daftar-penjadwalan" data-bs-toggle="pill" href="#tabJumatDaftarPenjadwalan" role="tab" id="tblJumatPenjadwalan" aria-selected="false" data-hari="Friday">Jumat</a>
                                </li>
                            </ul>
                            <!-- akhir judul tab hari -->
                        </div>
                        <div class="col">
                            <button type="button" class="btn btn-outline-secondary" id="cetakPenjadwalan">Cetak</button>
                            <div class="btn-group ms-2" role="group">
                                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bx bx-image"></i> Cetak Gambar
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" id="cetakGambarModal">
                                        <i class="bx bx-window"></i> Buka Modal
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" id="cetakGambarTab">
                                        <i class="bx bx-tab"></i> Buka Tab Baru
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- mulai isi tab hari -->
                    <div class="tab-content" id="pills-tabContent" style="width:100%; margin-right:15px;">
                        <div class="tab-pane fade overflow-x-auto mt-3 tabel-penjadwalan" id="tabSemuaDaftarPenjadwalan" role="tabpanel"></div>
                        <div class="tab-pane fade overflow-x-auto mt-3 tabel-penjadwalan" id="tabSeninDaftarPenjadwalan" role="tabpanel"></div>
                        <div class="tab-pane fade overflow-x-auto mt-3 tabel-penjadwalan" id="tabSelasaDaftarPenjadwalan" role="tabpanel"></div>
                        <div class="tab-pane fade overflow-x-auto mt-3 tabel-penjadwalan" id="tabRabuDaftarPenjadwalan" role="tabpanel"></div>
                        <div class="tab-pane fade overflow-x-auto mt-3 tabel-penjadwalan" id="tabKamisDaftarPenjadwalan" role="tabpanel"></div>
                        <div class="tab-pane fade overflow-x-auto mt-3 tabel-penjadwalan" id="tabJumatDaftarPenjadwalan" role="tabpanel"></div>
                    </div>
                    <!-- akhir isi tab hari -->
                </form>
                <!-- akhir form -->
            </div>
            <div class="tab-pane fade" id="tabHariIniPenjadwalan" role="tabpanel">
                <!-- mulai form -->
                <form id="formHariIniPenjadwalan">
                    <div class="row mb-3 align-items-center d-none">
                        <label for="ruanganHariIni" class="col-2 col-form-label">Ruangan</label>
                        <div class="col-10">
                            <select class="form-control form-control-sm select2" name="jruangan" id="ruanganHariIni" required>
                                <!-- <option value="0">Pilih Ruangan</option> -->
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanRS" class="col-2 col-form-label">Tujuan RS</label>
                        <div class="col-10">
                            <select class="form-control form-control-sm select2" name="tujuanRS" id="tujuanRS" >
                            </select>
                        </div>
                    </div>

                </form>
                <!-- akhir form -->
                <div class="row overflow-x-auto d-none tabel-penjadwalan" id="tampilTabelHariIniPenjadwalan"></div>
            </div>
            <div class="tab-pane fade" id="tabHistoryPenjadwalan" role="tabpanel">
                <!-- mulai form -->
                <form id="formHistoryPenjadwalan">
                    <!-- mulai periode -->
                    <div class="row mb-3 align-items-center">
                        <label for="periodeHistoryPenjadwalan" class="col-2 col-form-label">Periode tanggal operasi</label>
                        <div class="col">
                            <div class="input-group">
                                <span class="input-group-text">dari</span>
                                <input type="date" name="mulai" id="mulaiHistoryPenjadwalan" class="form-control form-control-sm" placeholder="Mulai" aria-label="Mulai periode">
                                <span class="input-group-text">sampai</span>
                                <input type="date" name="akhir" id="akhirHistoryPenjadwalan" class="form-control form-control-sm" placeholder="Akhir" aria-label="Akhir periode">
                            </div>
                        </div>
                    </div>
                    <!-- Tambah filter ruangan -->
                    <div class="row mb-3 align-items-center d-none">
                        <label for="ruanganHistory" class="col-2 col-form-label">Ruangan</label>
                        <div class="col-10">
                            <select class="form-control form-control-sm select2" name="jruangan" id="ruanganHistory" required>
                                <!-- <option value="0">Pilih Ruangan</option> -->
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanRSH" class="col-2 col-form-label">Tujuan RS</label>
                        <div class="col-10">
                            <select class="form-control form-control-sm select2" name="tujuanRSH" id="tujuanRSH" >
                            </select>
                        </div>
                    </div>
                    <!-- mulai aksi penjadwalan -->
                    <div class="row">
                        <div class="col-auto">
                        <div id="tooltipWrapper" data-bs-toggle="tooltip" title="Silakan isi periode tanggal terlebih dahulu">
                            <button type="button" class="btn btn-primary" id="tblHistoryPenjadwalan">Tampilkan</button>
                        </div>
                        </div>
                    </div>
                    <!-- akhir aksi penjadwalan -->
                    <!-- akhir periode -->
                </form>
                <!-- akhir form -->
                <div class="row overflow-x-auto mt-3 d-none tabel-penjadwalan" id="tampilTabelHistoryPenjadwalan"></div>
            </div>
            <div class="tab-pane fade" id="tabBatalReservasiPenjadwalan" role="tabpanel">
                <!-- mulai form -->
                <form id="formBatalPenjadwalan">
                    <!-- mulai periode -->
                    <div class="row mb-3 align-items-center">
                        <label for="periodeBatalPenjadwalan" class="col-2 col-form-label">Periode tanggal operasi</label>
                        <div class="col">
                            <div class="input-group">
                                <span class="input-group-text">dari</span>
                                <input type="date" name="mulai" id="mulaiBatalPenjadwalan" class="form-control form-control-sm" placeholder="Mulai" aria-label="Mulai periode">
                                <span class="input-group-text">sampai</span>
                                <input type="date" name="akhir" id="akhirBatalPenjadwalan" class="form-control form-control-sm" placeholder="Akhir" aria-label="Akhir periode">
                            </div>
                        </div>
                    </div>
                    <!-- Tambah filter ruangan -->
                    <div class="row mb-3 align-items-center d-none">
                        <label for="ruanganBatal" class="col-2 col-form-label">Ruangan</label>
                        <div class="col-10">
                            <select class="form-control form-control-sm select2" name="jruangan" id="ruanganBatal" required>
                                <!-- <option value="0">Pilih Ruangan</option> -->
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3 align-items-center">
                        <label for="tujuanRSB" class="col-2 col-form-label">Tujuan RS</label>
                        <div class="col-10">
                            <select class="form-control form-control-sm select2" name="tujuanRSHB" id="tujuanRSHB" >
                            </select>
                        </div>
                    </div>
                    <!-- mulai aksi penjadwalan -->
                    <div class="row">
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" id="tblBatalPenjadwalan">Tampilkan</button>
                        </div>
                    </div>
                    <!-- akhir aksi penjadwalan -->
                    <!-- akhir periode -->
                </form>
                <!-- akhir form -->
                <div class="row overflow-x-auto mt-3 d-none" id="tampilTabelBatalPenjadwalan"></div>
            </div>
        </div>
        <!-- akhir isi tab -->
    </div>
</div>
<!-- akhir kartu -->

<!-- mulai modal batal -->
<div class="modal fade" id="modalBatalPenjadwalan" aria-labelledby="judulModalBatalPenjadwalan" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalBatalPenjadwalan">Konfirmasi Batal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Operasi pasien ini akan dibatalkan, apa Anda yakin?
                <input type="hidden" name="id" id="idPenjadwalan">
                <div class="row mt-3 align-items-center">
                    <label for="alasanPenjadwalan" class="col-2 col-form-label">Alasan</label>
                    <div class="col">
                        <textarea name="alasan_batal" id="alasanPenjadwalan" class="form-control form-control-sm" placeholder="Alasan"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="tutupPenjadwalan" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-danger" id="yakinBatalPenjadwalan">Yakin batal</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal batal -->

<!-- mulai modal cetak gambar - DIPERLEBAR MEPET TEPI -->
<div class="modal fade" id="modalCetakGambar" aria-labelledby="judulModalCetakGambar" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen-lg-down" style="max-width: 95vw; margin: 1rem auto;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalCetakGambar">
                    <i class="bx bx-image"></i> Cetak Gambar - Daftar Operasi Elektif
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-1" id="modalCetakGambarBody" style="padding: 5px !important;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
<!-- akhir modal cetak gambar -->

<script>
    function checkPillsTabStatus() {
        const mulaiValue = $('#mulaiDaftarPenjadwalan').val();
        const akhirValue = $('#akhirDaftarPenjadwalan').val();
        const pillsTab = $('.tbl-daftar-penjadwalan');
        
        if (!mulaiValue || !akhirValue) {
            pillsTab.addClass('disabled');
            pillsTab.css('pointer-events', 'none');
            pillsTab.css('opacity', '0.6');
        } else {
            pillsTab.removeClass('disabled');
            pillsTab.css('pointer-events', 'auto');
            pillsTab.css('opacity', '1');
        }
    }
    $('#mulaiDaftarPenjadwalan, #akhirDaftarPenjadwalan').on('change', function() {
        checkPillsTabStatus();
    });
    $(document).ready(function () {
        checkPillsTabStatus();
        $('#tujuanOperasi, #tujuanRS, #tujuanRSH, #tujuanRSHB').select2({
            placeholder: 'Pilih Tujuan',
            allowClear: true,
            ajax: {
                url: '<?= base_url('admin/penjadwalan/get_tujuan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });
        // button kondisi
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Tombol awal dinonaktifkan
        $('#tblHistoryPenjadwalan').prop('disabled', true);

        // Tanggapi perubahan input tanggal
        $('#mulaiHistoryPenjadwalan, #akhirHistoryPenjadwalan').on('change', function () {
            const startDate = $('#mulaiHistoryPenjadwalan').val();
            const endDate = $('#akhirHistoryPenjadwalan').val();
            
            if (startDate && endDate) {
                $('#tblHistoryPenjadwalan').prop('disabled', false);
                bootstrap.Tooltip.getInstance(document.getElementById('tooltipWrapper')).disable();
            } else {
                $('#tblHistoryPenjadwalan').prop('disabled', true);
                bootstrap.Tooltip.getInstance(document.getElementById('tooltipWrapper')).enable();
            }
        });
        //bitton kondisi tutup
        var selectedTujuan = '<?= $tujuan ?>';
        var selectedTujuanRS = '<?= $tujuan ?>';

        if (selectedTujuan) {
            $.ajax({
                url: '<?= base_url('admin/penjadwalan/get_tujuan') ?>',
                dataType: 'json',
                success: function(data) {
                    if(data && data.length > 0) {
                        var selectedData = data.find(function(item) {
                            return item.ID == selectedTujuan;
                        });
                        if (selectedData) {
                            var option = new Option(selectedData.DESKRIPSI, selectedData.ID, true, true);
                            $('#tujuanOperasi').append(option);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        }

        // Set selected value untuk tab Daftar Perjanjian dari session tujuanOperasi
        if (selectedTujuanRS) {
            $.ajax({
                url: '<?= base_url('admin/penjadwalan/get_tujuan') ?>',
                dataType: 'json',
                success: function(data) {
                    if(data && data.length > 0) {
                        var selectedData = data.find(function(item) {
                            return item.ID == selectedTujuanRS;
                        });
                        if (selectedData) {
                            var option = new Option(selectedData.DESKRIPSI, selectedData.ID, true, true);
                            $('#tujuanOperasi').append(option).trigger('change');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        }

        
        // mulai tabel daftar perjanjian
        $('.tbl-daftar-penjadwalan').click(function () {
            // Destroy existing DataTable jika ada sebelum empty
            if ($.fn.DataTable.isDataTable('#tabelPenjadwalan')) {
                $('#tabelPenjadwalan').DataTable().destroy();
            }
            $('.tabel-penjadwalan').empty();
            let mulai = $('#mulaiDaftarPenjadwalan').val().length != 0 ? $('#mulaiDaftarPenjadwalan').val() : null;
            let akhir = $('#akhirDaftarPenjadwalan').val().length != 0 ? $('#akhirDaftarPenjadwalan').val() : null;
            let hari = $(this).data('hari');
            let id_ruangan = $('#ruangan').val();
            let tujuanOperasi = $('#tujuanOperasi').val();

            if (mulai > akhir) { // periksa tanggal
                Lobibox.notify('warning', {
                    icon: 'bx bx-error',
                    msg: 'Tanggal mulai tidak boleh lebih besar dari tanggal selesai',
                    sound: false,
                    title: 'Peringatan'
                });
            } else {
                $.ajax({
                    method: 'POST',
                    url: "<?= base_url('admin/Penjadwalan/tabel') ?>",
                    data: {
                        jenis: 'daftar_perjanjian',
                        mulai: mulai,
                        akhir: akhir,
                        hari: hari,
                        id_ruangan: id_ruangan,
                        tujuanOperasi: tujuanOperasi,

                    },
                    success: function (data) {
                        if (hari === 'All') {
                            $('#tabSemuaDaftarPenjadwalan').html(data);
                        } else if (hari === 'Monday') {
                            $('#tabSeninDaftarPenjadwalan').html(data);
                        } else if (hari === 'Tuesday') {
                            $('#tabSelasaDaftarPenjadwalan').html(data);
                        } else if (hari === 'Wednesday') {
                            $('#tabRabuDaftarPenjadwalan').html(data);
                        } else if (hari === 'Thursday') {
                            $('#tabKamisDaftarPenjadwalan').html(data);
                        } else if (hari === 'Friday') {
                            $('#tabJumatDaftarPenjadwalan').html(data);
                        }
                    }
                });
            }
        });
        // akhir tabel daftar perjanjian
        $('#ruangan').select2({
            placeholder: 'Pilih Ruangan',
            allowClear: true
        });

        $('#ruanganHistory, #ruanganBatal,#ruanganHariIni').select2({
            placeholder: 'Pilih Ruangan',
            allowClear: true,
            ajax: {
                url: '<?= base_url('admin/perjanjian/get_ruangan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                text: item.DESKRIPSI,
                                id: item.ID
                            }
                        })
                    };
                },
                cache: true
            }
        });

        // mulai cetak penjadwalan
        $('#cetakPenjadwalan').click(function () {
            let mulai = $('#mulaiDaftarPenjadwalan').val().length != 0 ? $('#mulaiDaftarPenjadwalan').val() : '1991-01-01';
            let akhir = $('#akhirDaftarPenjadwalan').val().length != 0 ? $('#akhirDaftarPenjadwalan').val() : '2099-12-31';
            let id_ruangan = $('#ruangan').val().length!= 0? $('#ruangan').val() : 0;

            let tujuan_rs = '';
            if ($('#tabDaftarPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanOperasi').val() || '';
            } else if ($('#tabHariIniPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanRS').val() || '';
            } else if ($('#tabHistoryPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanRSH').val() || '';
            } else if ($('#tabBatalReservasiPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanRSHB').val() || '';
            } else {
                tujuan_rs = $('#tujuanOperasi').val() || $('#tujuanRS').val() || $('#tujuanRSH').val() || $('#tujuanRSHB').val() || '';
            }

            // window.location.href = 'http://*************/reports/sippo/penjadwalan_operasi.php?format=pdf&TGLAWAL=' + mulai + '&TGLAKHIR=' + akhir + '&TUJUAN_RS=' + tujuan_rs;

            window.open('http://*************/reports/sippo/penjadwalan_operasi.php?format=pdf&TGLAWAL=' + mulai + '&TGLAKHIR=' + akhir + '&TUJUAN_RS=' + tujuan_rs, '_blank').focus();
        });
        // akhir cetak penjadwalan

        // Function to get parameters for cetak gambar
        function getCetakGambarParams() {
            let mulai = $('#mulaiDaftarPenjadwalan').val().length != 0 ? $('#mulaiDaftarPenjadwalan').val() : '1991-01-01';
            let akhir = $('#akhirDaftarPenjadwalan').val().length != 0 ? $('#akhirDaftarPenjadwalan').val() : '2099-12-31';

            let tujuan_rs = '';
            if ($('#tabDaftarPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanOperasi').val() || '';
            } else if ($('#tabHariIniPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanRS').val() || '';
            } else if ($('#tabHistoryPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanRSH').val() || '';
            } else if ($('#tabBatalReservasiPenjadwalan').hasClass('show active')) {
                tujuan_rs = $('#tujuanRSHB').val() || '';
            } else {
                tujuan_rs = $('#tujuanOperasi').val() || $('#tujuanRS').val() || $('#tujuanRSH').val() || $('#tujuanRSHB').val() || '';
            }

            return {
                mulai: mulai,
                akhir: akhir,
                tujuan_rs: tujuan_rs,
                url: "<?= base_url('admin/Penjadwalan/cetak_gambar') ?>?TGLAWAL=" + mulai + "&TGLAKHIR=" + akhir + "&TUJUAN_RS=" + tujuan_rs
            };
        }

        // Cetak gambar modal
        $('#cetakGambarModal').click(function (e) {
            e.preventDefault();
            const params = getCetakGambarParams();

            // Load content into modal
            $('#modalCetakGambarBody').html('<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div><br>Memuat halaman cetak...</div>');
            $('#modalCetakGambar').modal('show');

            // Load iframe content
            setTimeout(() => {
                $('#modalCetakGambarBody').html('<iframe src="' + params.url + '" style="width: 100%; height: 80vh; border: none;"></iframe>');
            }, 500);
        });

        // Handle modal close events
        $('#modalCetakGambar').on('hidden.bs.modal', function () {
            // Clear modal content when closed
            $('#modalCetakGambarBody').html('');
        });

        // Ensure modal close button works
        $('#modalCetakGambar .btn-close').click(function() {
            $('#modalCetakGambar').modal('hide');
        });

        // Cetak gambar tab baru
        $('#cetakGambarTab').click(function (e) {
            e.preventDefault();
            const params = getCetakGambarParams();
            window.open(params.url, '_blank').focus();
        });
        // akhir cetak gambar

        // mulai tabel hari ini
        $('#tblHariIniPenjadwalan').click(function () {
            // Destroy existing DataTable jika ada sebelum empty
            if ($.fn.DataTable.isDataTable('#tabelHariIniPenjadwalan')) {
                $('#tabelHariIniPenjadwalan').DataTable().destroy();
            }
            $('.tabel-penjadwalan').empty();
            loadPenjadwalanHariIni();
        });
        function loadPenjadwalanHariIni() {
            let id_ruangan = $('#ruanganHariIni').val();
            let tujuan_rs = $('#tujuanRS').val();

            $.ajax({
                method: 'POST',
                url: "<?= base_url('admin/Penjadwalan/tabel') ?>",
                data: {
                    jenis: 'hari_ini',
                    id_ruangan: id_ruangan,
                    tujuan_rs: tujuan_rs
                },
                success: function (data) {
                    $('#tampilTabelHariIniPenjadwalan').removeClass('d-none').html(data);
                },
                error: function () {
                    $('#tampilTabelHariIniPenjadwalan').html('<p class="text-center text-danger">Gagal memuat data. Silakan coba lagi.</p>');
                }
            });
        }

        $('#ruanganHariIni').change(function () {
            loadPenjadwalanHariIni();
        });
        $('#tujuanRS').change(function () {
            loadPenjadwalanHariIni();
        });

        // Event listener untuk perubahan filter tujuan RS di tab daftar perjanjian
        $('#tujuanOperasi').change(function () {
            // Reload tabel yang sedang aktif dengan delay untuk menghindari konflik
            setTimeout(function() {
                $('.tbl-daftar-penjadwalan.active').trigger('click');
            }, 100);
        });

       
        // $('#ruanganHariIni').change(function() {
        //     let id_ruangan = $(this).val(); 
        //     $.ajax({
        //         method: 'POST',
        //         url: "<?= base_url('admin/Penjadwalan/tabel') ?>",
        //         data: {
        //             jenis: 'hari_ini', 
        //             id_ruangan: id_ruangan 
        //         },
        //         success: function (data) {
        //             $('#tampilTabelHariIniPenjadwalan').removeClass('d-none').html(data);
        //         },
        //         error: function() {
        //             $('#tampilTabelHariIniPenjadwalan').html('<p class="text-center text-danger">Gagal memuat data. Silakan coba lagi.</p>');
        //         }
        //     });
        // });

        // $('#tblHariIniPenjadwalan').click(function () {
        //     $('.tabel-penjadwalan').empty();
        //     // let id_ruangan = $('#ruanganHariIni').val();
        //     $.ajax({
        //         method: 'POST',
        //         url: "<?= base_url('admin/Penjadwalan/tabel') ?>",
        //         data: {
        //             jenis: 'hari_ini',
        //             // id_ruangan: id_ruangan
        //         },
        //         success: function (data) {
        //             $('#tampilTabelHariIniPenjadwalan').removeClass('d-none').html(data);
        //         }
        //     });
        // });
        // akhir tabel hari ini

        // mulai batal
        $(document).on('click', '.tbl-batal-penjadwalan', function () {
            let id = $(this).data('id');
            $('#idPenjadwalan').val(id);
        });
        // akhir batal

        // mulai yakin batal
        $('#yakinBatalPenjadwalan').click(function () {
            $.ajax({
                url: "<?= base_url('admin/Penjadwalan/batal') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id: $('#idPenjadwalan').val(),
                    alasan_batal: $('#alasanPenjadwalan').val()
                },
                success: function (data) {
                    if (data.status === 200) {
                        $('#modalBatalPenjadwalan').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Daftar perjanjian dibatalkan',
                            sound: false,
                            title: 'Berhasil'
                        });
                        location.reload();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Gagal membatalkan',
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir yakin batal

        // mulai tabel history
        $('#tblHistoryPenjadwalan').click(function () {
            // Destroy existing DataTable jika ada sebelum empty
            if ($.fn.DataTable.isDataTable('#tabelPenjadwalan')) {
                $('#tabelPenjadwalan').DataTable().destroy();
            }
            $('.tabel-penjadwalan').empty();
            let id_ruangan = $('#ruanganHistory').val();
            let mulai = $('#mulaiHistoryPenjadwalan').val().length != 0 ? $('#mulaiHistoryPenjadwalan').val() : null;
            let akhir = $('#akhirHistoryPenjadwalan').val().length != 0 ? $('#akhirHistoryPenjadwalan').val() : null;
            let tujuanOperasi = $('#tujuanRSH').val();

            if (mulai > akhir) { // periksa tanggal
                Lobibox.notify('warning', {
                    icon: 'bx bx-error',
                    msg: 'Tanggal mulai tidak boleh lebih besar dari tanggal selesai',
                    sound: false,
                    title: 'Peringatan'
                });
            } else {
                $.ajax({
                    method: 'POST',
                    url: "<?= base_url('admin/Penjadwalan/tabel') ?>",
                    data: {
                        jenis: 'history',
                        mulai: mulai,
                        akhir: akhir,
                        id_ruangan: id_ruangan,
                        tujuanOperasi: tujuanOperasi,
                    },
                    success: function (data) {
                        $('#tampilTabelHistoryPenjadwalan').removeClass('d-none').html(data);
                    }
                });
            }
        });
        // akhir tabel history

        // mulai tabel batal
        $('#tblBatalPenjadwalan').click(function () {
            // Destroy existing DataTable jika ada sebelum empty
            if ($.fn.DataTable.isDataTable('#tabelBatalPenjadwalan')) {
                $('#tabelBatalPenjadwalan').DataTable().destroy();
            }
            $('#tabelBatalPenjadwalan').empty();
            let id_ruangan = $('#ruanganBatal').val();
            let mulai = $('#mulaiBatalPenjadwalan').val().length != 0 ? $('#mulaiBatalPenjadwalan').val() : null;
            let akhir = $('#akhirBatalPenjadwalan').val().length != 0 ? $('#akhirBatalPenjadwalan').val() : null;
            let tujuanOperasi = $('#tujuanRSHB').val();

            if (mulai > akhir) { // periksa tanggal
                Lobibox.notify('warning', {
                    icon: 'bx bx-error',
                    msg: 'Tanggal mulai tidak boleh lebih besar dari tanggal selesai',
                    sound: false,
                    title: 'Peringatan'
                });
            } else {
                $.ajax({
                    method: 'POST',
                    url: "<?= base_url('admin/Penjadwalan/tabel') ?>",
                    data: {
                        jenis: 'batal',
                        mulai: mulai,
                        akhir: akhir,
                         id_ruangan: id_ruangan,
                        tujuanOperasi: tujuanOperasi,
                    },
                    success: function (data) {
                        $('#tampilTabelBatalPenjadwalan').removeClass('d-none').html(data);
                    }
                });
            }
        });
        // akhir tabel batal
    });

    $(document).ready(function () {
        const hariSession = '<?= $hari ?? null ?>';
        
        if (hariSession) {
            const tabSelector = getTabSelectorFromHari(hariSession);
            
            if (tabSelector) {
                // Aktifkan tab
                $(tabSelector).tab('show');
                
                // Trigger click untuk load data
                setTimeout(() => {
                    $(tabSelector).trigger('click');
                }, 100);
            }
        }
    });
    function getTabSelectorFromHari(hari) {
        switch(hari) {
            case 'All': return '#tblSemuaPenjadwalan';
            case 'Monday': return '#tblSeninPenjadwalan'; 
            case 'Tuesday': return '#tblSelasaPenjadwalan';
            case 'Wednesday': return '#tblRabuPenjadwalan';
            case 'Thursday': return '#tblKamisPenjadwalan';
            case 'Friday': return '#tblJumatPenjadwalan';
            default: return null;
        }
    }
</script>
