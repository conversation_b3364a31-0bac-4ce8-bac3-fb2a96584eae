<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Jadwal_model extends CI_Model
{
    protected $_table = 'remun_medis.jadwal';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'asc';

    public function __construct()
    {
        parent::__construct();
    }

    function rules()
    {
        return [
            [
                'field' => 'jtanggal',
                'label' => 'Tanggal',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
            [
                'field' => 'awal',
                'label' => 'Waktu mulai',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
            [
                'field' => 'akhir',
                'label' => 'Waktu selesai',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
            [
                'field' => 'kuota',
                'label' => 'Kuota',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ],
            [
                'field' => 'jruangan',
                'label' => 'Ruangan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib dipilih',
                ],
            ]
        ];
    }

    public function cek($dokter, $ruang, $tanggal)
    {
        $result = [];
        $this->db->select('ID');
        $this->db->from($this->_table);
        $this->db->where('DOKTER', $dokter);
        $this->db->where('RUANGAN', $ruang);
        $this->db->where('TANGGAL', $tanggal);
        $this->db->where('STATUS !=', 0);

        $query = $this->db->get();
        $row = $query->row();
        $num = $query->num_rows();

        if ($num > 0) {
            $result = [
                'status' => 503,
                'message' => 'Data sudah ada',
                'id' => $row->ID
            ];
        } else {
            $result = [
                'status' => 200,
                'message' => 'Berhasil'
            ];
        }
        return $result;
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('remun_medis.jadwal.ID', $id);
        $this->db->update('remun_medis.jadwal', $data);
    }

    public function get_penjamin()
    {
        $this->db->select('penj.ID, penj.DESKRIPSI, penpen.NORM');
        $this->db->from('pendaftaran.pendaftaran penpen');
        $this->db->join('pendaftaran.penjamin penpenj', 'penpen.NOMOR = penpenj.NOPEN', 'left');
        $this->db->join('master.referensi penj', 'penpenj.JENIS = penj.ID AND penj.JENIS = 10', 'left');
        $this->db->where('penpen.NORM', $this->input->post('norm'));
        $this->db->order_by('penpen.TANGGAL', 'desc');
        $this->db->limit(1);

        $query = $this->db->get()->row();
        if ($query == null) {
            return 1;
        } else {
            return $query->ID;
        }
    }

    public function ambil()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        $this->db->select(
            "rj.ID, master.getNamaLengkapPegawai(md.NIP) PEGAWAI, mr.ID ID_RUANGAN, mr.DESKRIPSI,
            DATE_FORMAT(rj.TANGGAL, '%d') DAY, rj.TANGGAL,CONCAT(rj.AWAL, '-', rj.AKHIR) WAKTU,
            SUM(CASE WHEN rp.status != 0 AND rp.STATUS_SORE = 0 THEN 1 ELSE 0 END) AS JUMLAH, rj.KUOTA,
            CONCAT(rj.AWAL_SORE, '-', rj.AKHIR_SORE) WAKTU_SORE,
            SUM(CASE WHEN rp.status != 0 AND rp.STATUS_SORE = 1 THEN 1 ELSE 0 END) AS JUMLAH_SORE, rj.KUOTASORE,
            rr.COLOR, DAYNAME(rj.TANGGAL) HARI"
        );
        $this->db->from('remun_medis.jadwal rj');
        $this->db->join('master.dokter md', 'rj.DOKTER = md.ID');
        $this->db->join('master.ruangan mr', 'rj.RUANGAN = mr.ID');
        $this->db->join('remun_medis.ruangan rr', 'mr.ID = rr.ID', 'left');

        // mulai periksa dokter
        if ($post['dokter'] == 114) {
            $this->db->join(
                'remun_medis.perjanjian rp',
                'rj.DOKTER = rp.ID_DOKTER AND rj.RUANGAN = rp.ID_RUANGAN AND rj.TANGGAL = rp.TANGGAL',
                'left'
            );
        } else {
            $this->db->join(
                'remun_medis.perjanjian rp',
                'rj.DOKTER = rp.ID_DOKTER AND rj.RUANGAN = rp.ID_RUANGAN AND rj.TANGGAL = rp.TANGGAL
                AND rp.RENCANA IN(11)',
                'left'
            );
        }
        // akhir periksa dokter

        $this->db->where("DATE_FORMAT(rj.TANGGAL, '%Y-%m') = '" . $post["bulan"] . "'");
        $this->db->where('rj.TANGGAL >= CURDATE()');
        $this->db->where('rj.STATUS !=', 0);
        $this->db->where_in('mr.ID', ['105090101', '105090104']);

        // mulai periksa dokter
        if ($post['dokter']) {
            $this->db->where('md.ID', $post['dokter']);
        }
        // akhir periksa dokter

        $this->db->order_by('rj.RUANGAN DESC, rj.TANGGAL ASC');
        $this->db->group_by(['rj.TANGGAL', 'rj.RUANGAN']);

        $query = $this->db->get();
        return $query->result();
    }
}

// End of File Jadwal_model.php
// Location: ./application/models/Jadwal_model.php