<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Manajemen Pengguna</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item">Manajemen Pengguna</li>
                <li class="breadcrumb-item active" aria-current="page">Per SMF</li>
            </ol>
        </nav>
    </div>
    <div class="ms-auto">
        <div class="btn-group">
            <a class="btn btn-primary" href="<?= base_url('admin/manajemen_pengguna/Smf/form') ?>" role="button">Form</a>
        </div>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu tabel -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Tabel SMF</h5>
        <hr>
        <div class="row mb-3 align-items-center">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover w-100" id="tabelManajemenSMF">
                    <thead>
                        <tr>
                            <th class="text-center no-sort" scope="col" rowspan="2">No.</th>
                            <th class="text-center" scope="col" rowspan="2">SMF</th>
                            <th class="text-center no-sort" scope="col" colspan="<?= $hitung_akses['jumlah'] ?>">Hak Akses</th>
                            <th class="text-center no-sort" scope="col" rowspan="2">Aksi</th>
                        </tr>
                        <tr>
                            <?php foreach ($akses as $a) : ?>
                                <th class="text-center no-sort" scope="col"><?= $a['nama'] ?></th>
                            <?php endforeach ?>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th class="text-center" scope="col" rowspan="2">No.</th>
                            <th class="text-center" scope="col" rowspan="2">SMF</th>
                            <?php foreach ($akses as $a) : ?>
                                <th class="text-center" scope="col"><?= $a['nama'] ?></th>
                            <?php endforeach ?>
                            <th class="text-center" scope="col" rowspan="2">Aksi</th>
                        </tr>
                        <tr>
                            <th class="text-center" scope="col" colspan="<?= $hitung_akses['jumlah'] ?>">Hak Akses</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- akhir kartu tabel -->

<!-- mulai modal hapus -->
<div class="modal fade" id="modalHapusManajemenSMF" aria-labelledby="judulModalHapusManajemenSMF" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="judulModalHapusManajemenSMF">Konfirmasi Hapus</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Akses pengguna ini akan dihapus, apa Anda yakin?
                <input type="hidden" name="id_smf" id="idManajemenSMF">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="tutupManajemenSMF" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-danger" id="hapusManajemenSMF">Yakin hapus</button>
            </div>
        </div>
    </div>
</div>
<!-- akhir modal hapus -->

<script>
    $(document).ready(function() {
        // mulai tabel
        $('#tabelManajemenSMF').DataTable({
            autoWidth: true,
            order: [1, 'asc'],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('admin/manajemen_pengguna/Smf/isi_tabel') ?>",
                type: 'POST',
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: ['no-sort'],
                orderable: false,
            }],
            // akhir pendefinisian kolom
        });
        // akhir tabel

        // mulai hapus
        $(document).on('click', '.tbl-hapus-manajemen-smf', function() {
            $('#idManajemenSMF').val($(this).data('id'));
        });
        // akhir hapus

        // mulai yakin hapus
        $('#hapusManajemenSMF').click(function() {
            $.ajax({
                url: "<?= base_url('admin/manajemen_pengguna/Smf/hapus') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id: $('#idManajemenSMF').val(),
                },
                success: function(data) {
                    if (data.status === 200) {
                        $('#modalHapusManajemenSMF').modal('hide');
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Akses pengguna sudah dihapus',
                            sound: false,
                            title: 'Berhasil'
                        });
                        location.reload();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: 'Gagal menghapus',
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir yakin hapus
    });
</script>