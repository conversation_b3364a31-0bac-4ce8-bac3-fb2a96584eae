<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Pembatalan Operasi Hari Ini</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item"><em>Dashboard</em></li>
                <li class="breadcrumb-item active" aria-current="page">Pembatalan Operasi Hari Ini</li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Ta<PERSON>n</h5>
        <hr>

        <!-- mulai filter tujuan RS -->
        <div class="row mb-3 align-items-center">
            <div class="col-lg-2">
                <label for="tujuanRsBatalOperasi" class="form-label mb-0">Filter Tujuan RS</label>
            </div>
            <div class="col-lg-4">
                <select class="form-select" id="tujuanRsBatalOperasi" style="width: 100%;">
                    <option value="">Semua Tujuan RS</option>
                </select>
            </div>
        </div>
        <!-- akhir filter tujuan RS -->

        <!-- mulai tabel batal operasi -->
        <div class="row mb-3 align-items-center">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover w-100" id="tableBatalOperasi">
                    <thead>
                        <tr>
                            <th class="text-center" scope="col">No.</th>
                            <th class="text-center" scope="col">Nama Pasien</th>
                            <th class="text-center" scope="col">No. RM</th>
                            <th class="text-center" scope="col">Diagnosis</th>
                            <th class="text-center" scope="col">Tindakan</th>
                            <th class="text-center" scope="col">Operator</th>
                            <th class="text-center" scope="col">Ruang Rawat</th>
                            <th class="text-center" scope="col">Alasan Pembatalan</th>
                            <th class="text-center" scope="col">Tgl.Pembatalan</th>
                            <th class="text-center" scope="col">jam Pembatalan</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th class="text-center" scope="col">No.</th>
                            <th class="text-center" scope="col">Nama Pasien</th>
                            <th class="text-center" scope="col">No. RM</th>
                            <th class="text-center" scope="col">Diagnosis</th>
                            <th class="text-center" scope="col">Tindakan</th>
                            <th class="text-center" scope="col">Operator</th>
                            <th class="text-center" scope="col">Ruang Rawat</th>
                            <th class="text-center" scope="col">Alasan Pembatalan</th>
                            <th class="text-center" scope="col">Tgl.Pembatalan</th>
                            <th class="text-center" scope="col">jam Pembatalan</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function() {
        // mulai filter tujuan RS
        $('#tujuanRsBatalOperasi').select2({
            placeholder: 'Pilih tujuan RS',
            allowClear: true,
            ajax: {
                url: '<?= base_url('operasi/Daftar_tunggu/get_tujuan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        }).change(function () {
            loadTabel();
        });
        // akhir filter tujuan RS

        // mulai tabel
        var table;

        function loadTabel() {
            var tujuan = $('#tujuanRsBatalOperasi').val() || '';

            // Destroy existing table if exists
            if (table) {
                table.destroy();
            }

            table = $('#tableBatalOperasi').DataTable({
                autoWidth: true,
                order: [1, 'asc'],
                language: {
                    url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
                },

                processing: true,
                serverSide: true,
                iDisplayLength: 25,
                search: {
                    regex: true
                },

                // mulai ambil data
                ajax: {
                    url: "<?= base_url('operasi/Batal_operasi/isi_tabel') ?>",
                    type: 'POST',
                    data: {
                        PTUJUAN_RS: tujuan
                    }
                },
                // akhir ambil data

                // mulai pendefinisian kolom
                columnDefs: [{
                    targets: [0],
                    orderable: false,
                }],
                // akhir pendefinisian kolom
            });
        }

        // Load initial table
        loadTabel();

        // akhir tabel
    });
</script>