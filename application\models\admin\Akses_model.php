<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Akses_model extends CI_Model
{
    protected $_table = 'aplikasi.akses_sippo';
    protected $_primary_key = 'id';

    public function __construct()
    {
        parent::__construct();
    }

    public function list($jumlah = null)
    {
        // mulai periksa jumlah
        if ($jumlah == 'jumlah') {
            $this->db->select('COUNT(id) jumlah');
        } else {
            $this->db->select('id, nama');
        }
        // akhir periksa jumlah

        $this->db->from($this->_table);
        $this->db->where('status', 1);
        $this->db->order_by('id');
        $query = $this->db->get();

        // mulai periksa jumlah
        if ($jumlah == 'jumlah') {
            return $query->row_array();
        } else {
            return $query->result_array();
        }
        // akhir periksa jumlah
    }
}

// End of File Akses_model.php
// Location: ./application/models/Akses_model.php