<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pengguna_model extends CI_Model
{
    protected $_table = 'aplikasi.pengguna';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'desc';

    public function __construct()
    {
        parent::__construct();
    }

    function rules()
    {
        return [
            [
                'field' => 'username',
                'label' => 'Nama pengguna',
                'rules' => 'trim|required|max_length[20]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 20 karakter',
                ],
            ],
            [
                'field' => 'password',
                'label' => 'Kata sandi',
                'rules' => 'trim|required|max_length[255]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 255 karakter',
                ],
            ],
            [
                'field' => 'captcha',
                'label' => 'Captcha',
                'rules' => 'trim|required|min_length[4]|max_length[4]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'min_length' => '%s minimal 4 karakter',
                    'max_length' => '%s maksimal 4 karakter',
                ],
            ],
        ];
    }

    public function masuk($username, $password)
    {
        $this->db->select('COUNT(ap.ID) jumlah');
        $this->db->from('aplikasi.pengguna ap');
        $this->db->where('ap.LOGIN', $username);
        $this->db->where("ap.PASSWORD = PASSWORD('$password')");
        $this->db->or_where("ap.PASSWORD = MD5(CONCAT('KDFLDMSTHBWWSGCBH', MD5('$password'), 'KDFLDMSTHBWWSGCBH'))");
        $this->db->where('ap.STATUS', 1);
        $query = $this->db->get();
        return $query->row_array();
    }

    public function data_pengguna($username)
    {
        $this->db->select(
            "ap.ID id, ap.NAMA nama, IFNULL(aps.akses_sippo, '[0]') akses_sippo, IFNULL(aps.gelap, 1) gelap,
            mp.SMF id_smf, smf.DESKRIPSI dpjp"
        );
        $this->db->from('aplikasi.pengguna ap');
        $this->db->join('aplikasi.pengguna_sippo aps', 'aps.id_pengguna = ap.ID AND aps.status = 1', 'left');
        $this->db->join('master.pegawai mp', 'mp.NIP = ap.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = mp.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('ap.LOGIN', $username);
        $query = $this->db->get();
        return $query->row_array();
    }

    public function list()
    {
        $q = $this->input->get('q');
        $this->db->select('ID id_pengguna, NIP nip, master.getNamaLengkapPegawai(NIP) nama');
        $this->db->from($this->_table);
        $this->db->where('STATUS', 1);
        $this->db->order_by('master.getNamaLengkapPegawai(NIP)');

        // mulai pencarian
        if ($q) {
            $this->db->like('master.getNamaLengkapPegawai(NIP)', $q);
        }
        // akhir pencarian

        $query = $this->db->get();
        return $query->result_array();
    }
}

// End of File Pengguna_model.php
// Location: ./application/models/Pengguna_model.php