<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Reservasi_model extends CI_Model
{
    protected $_table = 'db_reservasi.tb_reservasi rr';
    protected $_primary_key = 'rr.id';
    protected $_order_by = 'rr.tgl_rencanaMasuk';
    protected $_order_by_type = 'desc';

    protected $_urutan_kolom = [
        null,
        'nama',
        'norm',
        'tgl_lahir',
        'kelas_rawat',
        'cara_bayar',
        'diagnosis',
        'tindakan',
        'dokter',
        'tgl_rawat_inap',
        'tgl_operasi',
        'no_telp',
        'status',
        null
    ];

    protected $_pencarian_kolom = [
        'rr.nama',
        'rr.norm',
        'rr.tgl_lahir',
        'kr.DESKRIPSI',
        'cb.DESKRIPSI',
        'rmp.DIAGNOSA',
        'rmp.TINDAKAN',
        'master.getNamaLengkapPegawai(md.NIP)',
        'rr.tgl_rencanaMasuk',
        'rmp.TANGGAL',
        'rr.no_telp',
        'mr.DESKRIPSI' 
    ];

    function rules()
    {
        return [
            [
                'field' => 'norm',
                'label' => 'Nomor RM',
                'rules' => 'trim|required|max_length[7]|numeric',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 7 karakter',
                    'numeric' => '%s harus berupa angka',
                ]
            ],
            [
                'field' => 'nama',
                'label' => 'Nama',
                'rules' => 'trim|required|max_length[100]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 100 karakter'
                ],
            ],
            [
                'field' => 'no_telp',
                'label' => 'Nomor telepon',
                'rules' => 'trim|required|max_length[50]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 50 karakter'
                ],
            ],
            [
                'field' => 'tgl_rencanaMasuk',
                'label' => 'Tanggal rencana masuk',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi'
                ],
            ],
            [
                'field' => 'id_cara_bayar',
                'label' => 'Cara bayar',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi'
                ],
            ],
            [
                'field' => 'id_kelas',
                'label' => 'Kelas rawat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi'
                ],
            ],
            [
                'field' => 'kode_booking',
                'label' => 'Kode <em>booking</em>',
                'rules' => 'trim|required|max_length[100]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 100 karakter'
                ],
            ],
            [
                'field' => 'dokter',
                'label' => 'Dokter',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi'
                ]
            ],
            [
                'field' => 'rencana_operasi',
                'label' => 'Tujuan masuk RS',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib dipilih'
                ]
            ],
            [
                'field' => 'id_ruang',
                'label' => 'Ruangan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => 'Ruangan belum terisi, Silahkan pilih tanggal operasi berdasarkan jadwal agar ruangan terisi'
                ]
            ],
        ];
    }

    public function simpan($data)
    {
        $this->db->insert('db_reservasi.tb_reservasi', $data);
        return $this->db->insert_id();
    }

    public function ubah($id, $data)
    {
        $this->db->where('db_reservasi.tb_reservasi.id', $id);
        $this->db->update('db_reservasi.tb_reservasi', $data);
    }

    function tabel($mulai = null, $akhir = null, $id_dokter = null, $id_ruangan = null, $tujuan= null)
    {
        $this->db->select(
            "rr.id id_reservasi, rmp.ID ID_PERJANJIAN, rr.nama, rr.norm, rr.tgl_lahir, kr.DESKRIPSI kelas_rawat,
            cb.DESKRIPSI cara_bayar, rmp.DIAGNOSA diagnosis, rmp.TINDAKAN tindakan, rr.id_dokter,
            master.getNamaLengkapPegawai(md.NIP) dokter, rr.tgl_rencanaMasuk tgl_rawat_inap, rmp.TANGGAL tgl_operasi,
            rr.no_telp, rr.status, rmp.ID_RUANGAN, mr.DESKRIPSI ruang,
            CASE WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler' ELSE 'Operasi Swasta' END AS tujuan_rs"
        );
        $this->db->from($this->_table);
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID = rr.id_perjanjian', 'left');
        $this->db->join('master.referensi kr', 'kr.ID = rr.id_kelas AND kr.JENIS = 19');
        $this->db->join('master.referensi cb', 'cb.ID = rr.id_cara_bayar AND cb.JENIS = 10');
        $this->db->join('master.dokter md', 'rr.id_dokter = md.ID');
        $this->db->join('master.ruangan mr', 'mr.ID = rmp.ID_RUANGAN', 'left');
        // $this->db->where('rmp.ID_RUANGAN', 105090101); // Instalasi Bedah Pusat
        $this->db->where_in('rmp.ID_RUANGAN', ['105090101', '105090104']);
        // mulai periksa tanggal
        if ($mulai != null) {
            $this->db->where('rmp.TANGGAL >=', $mulai);
        }
        if ($akhir != null) {
            $this->db->where('rmp.TANGGAL <=', $akhir);
        }
        // akhir periksa tanggal
        if (empty($id_ruangan)) {
            // $this->db->where('rmp.ID_RUANGAN', '105090101');
            $this->db->where_in('rmp.ID_RUANGAN', ['105090101', '105090104']);
        } else {
            $this->db->where('rmp.ID_RUANGAN', $id_ruangan);
        }
        
        if ($tujuan !== null && $tujuan !== '') {
            if ($tujuan == 2) {
                $this->db->where('rr.id_cara_bayar', 2);            
            } else {
                $this->db->where('rr.id_cara_bayar !=', 2);
            }
        }

        // mulai periksa dokter
        if ($id_dokter != null) {
            $this->db->where('rr.id_dokter', $id_dokter);
        }
        // akhir periksa dokter

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil($mulai = null, $akhir = null, $id_dokter = null, $id_ruangan = null, $tujuan= null)
    {
        $this->tabel($mulai, $akhir, $id_dokter, $id_ruangan, $tujuan);
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring($mulai = null, $akhir = null, $id_dokter = null, $id_ruangan = null, $tujuan= null)
    {
        $this->tabel($mulai, $akhir, $id_dokter, $id_ruangan, $tujuan);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua($mulai = null, $akhir = null, $id_dokter = null, $id_ruangan = null, $tujuan= null)
    {
        $this->tabel($mulai, $akhir, $id_dokter, $id_ruangan, $tujuan);
        return $this->db->count_all_results();
    }

    function detail($id_perjanjian)
    {
        $this->db->select(
            "rr.id id_reservasi, rr.id_jk, rr.tgl_lahir, rmp.ID_PENDAFTARAN_OPERASI, rr.norm,
            rr.id_waiting_list_operasi, wlo.nokun, DATE_FORMAT(wlo.created_at, '%d/%m/%Y, %H.%i.%s') tgl_dibuat,
            rr.id_dokter, rmp.TUJUANOPERASI, rr.id_tujuan_dirawat, rr.tgl_rencanaMasuk, rr.id_cara_bayar, rr.id_kelas,
            rr.kode_booking, rmp.DIAGNOSA diagnosis, rmp.TINDAKAN tindakan, rmp.TANGGAL tgl_operasi, rmp.ID_RUANGAN,
            ru.DESKRIPSI ruang, rmp.PERSETUJUAN"
        );
        $this->db->from($this->_table);
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rr.id_waiting_list_operasi', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID = rr.id_perjanjian', 'left');
        $this->db->join('master.ruangan ru', 'ru.ID = rmp.ID_RUANGAN AND ru.JENIS = 5 AND ru.STATUS = 1');
        $this->db->where('rmp.ID', $id_perjanjian);

        $query = $this->db->get();
        return $query->row_array();
    }
}

// End of File Reservasi_model.php
// Location: ./application/models/admin/Reservasi_model.php