<div class="card overflow-x-auto ">
    <div class="card-body">
        <table class="table table-striped table-bordered table-hover w-100" id="tabel<PERSON><PERSON><PERSON><PERSON><PERSON>">
            <thead>
                <tr>
                    <th class="text-center" scope="col">No.</th>
                    <th class="text-center" scope="col"><PERSON><PERSON></th>
                    <th class="text-center" scope="col">No. RM</th>
                    <th class="text-center" scope="col">Tanggal Lahir</th>
                    <th class="text-center" scope="col">Tujuan RS</th>
                    <th class="text-center" scope="col"><PERSON><PERSON></th>
                    <th class="text-center" scope="col">Penjamin</th>
                    <th class="text-center" scope="col">Diagnosis</th>
                    <th class="text-center" scope="col">Ruangan</th>   
                    <th class="text-center" scope="col">Tindakan</th>
                    <th class="text-center" scope="col">Dokter</th>
                    <th class="text-center" scope="col">Tanggal <PERSON>at</th>
                    <th class="text-center" scope="col">Tanggal Opera<PERSON></th>
                    <th class="text-center" scope="col">No. Telepon</th>
                    <th class="text-center" scope="col">Status Reservasi</th>
                    <th class="text-center" scope="col">Aksi</th>
                </tr>
            </thead>
            <tbody></tbody>
            <tfoot>
                <tr>
                    <th class="text-center" scope="col">No.</th>
                    <th class="text-center" scope="col">Nama Pasien</th>
                    <th class="text-center" scope="col">No. RM</th>
                    <th class="text-center" scope="col">Tanggal Lahir</th>
                    <th class="text-center" scope="col">Tujuan RS</th>
                    <th class="text-center" scope="col">Kelas Rawat</th>
                    <th class="text-center" scope="col">Penjamin</th>
                    <th class="text-center" scope="col">Diagnosis</th>
                    <th class="text-center" scope="col">Ruangan</th>
                    <th class="text-center" scope="col">Tindakan</th>
                    <th class="text-center" scope="col">Dokter</th>
                    <th class="text-center" scope="col">Tanggal Rawat</th>
                    <th class="text-center" scope="col">Tanggal Operasi</th>
                    <th class="text-center" scope="col">No. Telepon</th>
                    <th class="text-center" scope="col">Status Reservasi</th>
                    <th class="text-center" scope="col">Aksi</th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

<script>
    $(document).ready(function () {
        // mulai tabel
        $('#tabelPerjanjian').DataTable({
            autoWidth: true,
            order: [9, 'desc'],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('admin/Perjanjian/isi_tabel') ?>",
                type: 'POST',
                data: {
                    mulai: $('#mulaiPerjanjianOperasi').val().length != 0 ? $('#mulaiPerjanjianOperasi').val() : null,
                    akhir: $('#akhirPerjanjianOperasi').val().length != 0 ? $('#akhirPerjanjianOperasi').val() : null,
                    id_dokter: $('#dokterPerjanjianOperasi').val().length != 0 ? $('#dokterPerjanjianOperasi').val() : null,
                    id_ruangan: $('#ruangan').val().length != 0 ? $('#ruangan').val() : null,
                    tujuan : $('#tujuan').val() || null  
                }
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [0, 15],
                orderable: false,
            }],
            // akhir pendefinisian kolom
        });
        // akhir tabel
    });
</script>
