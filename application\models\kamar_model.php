<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Kamar_model extends CI_Model
{
    protected $_table = 'db_master.tb_kamar';
    protected $_primary_key = 'id';
    protected $_order_by = 'nama';
    protected $_order_by_type = 'asc';

    function ambil($id_ruang)
    {
        $this->db->select('id, nama, status');
        $this->db->from($this->_table);
        $this->db->where('id_ruang', $id_ruang);
        $this->db->where('status', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }
}

// End of File Kamar_model.php
// Location: ./application/models/Kamar_model.php