var Lobibox=Lobibox||{};!function(){function LobiboxPrompt(type,options){this.$input=null,this.$type="prompt",this.$promptType=type,options=$.extend({},Lobibox.prompt.DEFAULT_OPTIONS,options),this.$options=this._processInput(options),this._init(),this.debug(this)}function LobiboxConfirm(options){this.$type="confirm",this.$options=this._processInput(options),this._init(),this.debug(this)}function LobiboxAlert(type,options){this.$type=type,this.$options=this._processInput(options),this._init(),this.debug(this)}function LobiboxProgress(options){this.$type="progress",this.$progressBarElement=null,this.$options=this._processInput(options),this.$progress=0,this._init(),this.debug(this)}function LobiboxWindow(type,options){this.$type=type,this.$options=this._processInput(options),this._init(),this.debug(this)}Lobibox.counter=0,Lobibox.prompt=function(type,options){return new LobiboxPrompt(type,options)},Lobibox.confirm=function(options){return new LobiboxConfirm(options)},Lobibox.progress=function(options){return new LobiboxProgress(options)},Lobibox.error={},Lobibox.success={},Lobibox.warning={},Lobibox.info={},Lobibox.alert=function(type,options){return["success","error","warning","info"].indexOf(type)>-1?new LobiboxAlert(type,options):void 0},Lobibox.window=function(options){return new LobiboxWindow("window",options)};var LobiboxBase={$type:null,$el:null,$options:null,debug:function(){this.$options.debug&&window.console.debug.apply(window.console,arguments)},_processInput:function(options){if($.isArray(options.buttons)){for(var btns={},i=0;i<options.buttons.length;i++)btns[options.buttons[i]]=Lobibox.base.OPTIONS.buttons[options.buttons[i]];options.buttons=btns}options.customBtnClass=options.customBtnClass?options.customBtnClass:Lobibox.base.DEFAULTS.customBtnClass;for(var i in options.buttons)if(options.buttons.hasOwnProperty(i)){var btn=options.buttons[i];btn=$.extend({},Lobibox.base.OPTIONS.buttons[i],btn),btn["class"]||(btn["class"]=options.customBtnClass),options.buttons[i]=btn}return options=$.extend({},Lobibox.base.DEFAULTS,options),void 0===options.showClass&&(options.showClass=Lobibox.base.OPTIONS.showClass),void 0===options.hideClass&&(options.hideClass=Lobibox.base.OPTIONS.hideClass),void 0===options.baseClass&&(options.baseClass=Lobibox.base.OPTIONS.baseClass),void 0===options.delayToRemove&&(options.delayToRemove=Lobibox.base.OPTIONS.delayToRemove),options.iconClass||(options.iconClass=Lobibox.base.OPTIONS.icons[options.iconSource][this.$type]),options},_init:function(){var me=this;me._createMarkup(),me.setTitle(me.$options.title),me.$options.draggable&&!me._isMobileScreen()&&(me.$el.addClass("draggable"),me._enableDrag()),me.$options.closeButton&&me._addCloseButton(),me.$options.closeOnEsc&&$(document).on("keyup.lobibox",function(ev){27===ev.which&&me.destroy()}),me.$options.baseClass&&me.$el.addClass(me.$options.baseClass),me.$options.showClass&&(me.$el.removeClass(me.$options.hideClass),me.$el.addClass(me.$options.showClass)),me.$el.data("lobibox",me)},_calculatePosition:function(position){var top,me=this;top="top"===position?30:"bottom"===position?$(window).outerHeight()-me.$el.outerHeight()-30:($(window).outerHeight()-me.$el.outerHeight())/2;var left=($(window).outerWidth()-me.$el.outerWidth())/2;return{left:left,top:top}},_createButton:function(type,op){var me=this,btn=$("<button></button>").addClass(op["class"]).attr("data-type",type).html(op.text);return me.$options.callback&&"function"==typeof me.$options.callback&&btn.on("click.lobibox",function(ev){var bt=$(this);me._onButtonClick(me.$options.buttons[type],type),me.$options.callback(me,bt.data("type"),ev)}),btn.click(function(){me._onButtonClick(me.$options.buttons[type],type)}),btn},_onButtonClick:function(buttonOptions,type){var me=this;("ok"===type&&"prompt"===me.$type&&me.isValid()||"prompt"!==me.$type||"ok"!==type)&&buttonOptions&&buttonOptions.closeOnClick&&me.destroy()},_generateButtons:function(){var me=this,btns=[];for(var i in me.$options.buttons)if(me.$options.buttons.hasOwnProperty(i)){var op=me.$options.buttons[i],btn=me._createButton(i,op);btns.push(btn)}return btns},_createMarkup:function(){var me=this,lobibox=$('<div class="lobibox"></div>');lobibox.attr("data-is-modal",me.$options.modal);var header=$('<div class="lobibox-header"></div>').append('<span class="lobibox-title"></span>'),body=$('<div class="lobibox-body"></div>');if(lobibox.append(header),lobibox.append(body),me.$options.buttons&&!$.isEmptyObject(me.$options.buttons)){var footer=$('<div class="lobibox-footer"></div>');footer.append(me._generateButtons()),lobibox.append(footer),Lobibox.base.OPTIONS.buttonsAlign.indexOf(me.$options.buttonsAlign)>-1&&footer.addClass("text-"+me.$options.buttonsAlign)}me.$el=lobibox.addClass(Lobibox.base.OPTIONS.modalClasses[me.$type])},_setSize:function(){var me=this;me.setWidth(me.$options.width),me.setHeight("auto"===me.$options.height?me.$el.outerHeight():me.$options.height)},_calculateBodyHeight:function(height){var me=this,headerHeight=me.$el.find(".lobibox-header").outerHeight(),footerHeight=me.$el.find(".lobibox-footer").outerHeight();return height-(headerHeight?headerHeight:0)-(footerHeight?footerHeight:0)},_addBackdrop:function(){0===$(".lobibox-backdrop").length&&$("body").append('<div class="lobibox-backdrop"></div>')},_triggerEvent:function(type){var me=this;me.$options[type]&&"function"==typeof me.$options[type]&&me.$options[type](me)},_calculateWidth:function(width){var me=this;return width=Math.min(Math.max(width,me.$options.width),$(window).outerWidth()),width===$(window).outerWidth()&&(width-=2*me.$options.horizontalOffset),width},_calculateHeight:function(height){var me=this;return console.log(me.$options.height),height=Math.min(Math.max(height,me.$options.height),$(window).outerHeight()),height===$(window).outerHeight()&&(height-=2*me.$options.verticalOffset),height},_addCloseButton:function(){var me=this,closeBtn=$('<span class="btn-close">&times;</span>');me.$el.find(".lobibox-header").append(closeBtn),closeBtn.on("mousedown",function(ev){ev.stopPropagation()}),closeBtn.on("click.lobibox",function(){me.destroy()})},_position:function(){var me=this;me._setSize();var pos=me._calculatePosition();me.setPosition(pos.left,pos.top)},_isMobileScreen:function(){return $(window).outerWidth()<768},_enableDrag:function(){var el=this.$el,heading=el.find(".lobibox-header");heading.on("mousedown.lobibox",function(ev){el.attr("offset-left",ev.offsetX),el.attr("offset-top",ev.offsetY),el.attr("allow-drag","true")}),$(document).on("mouseup.lobibox",function(){el.attr("allow-drag","false")}),$(document).on("mousemove.lobibox",function(ev){if("true"===el.attr("allow-drag")){var left=ev.clientX-parseInt(el.attr("offset-left"),10)-parseInt(el.css("border-left-width"),10),top=ev.clientY-parseInt(el.attr("offset-top"),10)-parseInt(el.css("border-top-width"),10);el.css({left:left,top:top})}})},_setContent:function(msg){var me=this;return me.$el.find(".lobibox-body").html(msg),me},_beforeShow:function(){var me=this;me._triggerEvent("onShow")},_afterShow:function(){var me=this;Lobibox.counter++,me.$el.attr("data-nth",Lobibox.counter),me.$options.draggable||$(window).on("resize.lobibox-"+me.$el.attr("data-nth"),function(){me.refreshWidth(),me.refreshHeight(),me.$el.css("left","50%").css("margin-left","-"+me.$el.width()/2+"px"),me.$el.css("top","50%").css("margin-top","-"+me.$el.height()/2+"px")}),me._triggerEvent("shown")},_beforeClose:function(){var me=this;me._triggerEvent("beforeClose")},_afterClose:function(){var me=this;me.$options.draggable||$(window).off("resize.lobibox-"+me.$el.attr("data-nth")),me._triggerEvent("closed")},hide:function(){function callback(){me.$el.addClass("lobibox-hidden"),0===$(".lobibox[data-is-modal=true]:not(.lobibox-hidden)").length&&($(".lobibox-backdrop").remove(),$("body").removeClass(Lobibox.base.OPTIONS.bodyClass))}var me=this;return me.$options.hideClass?(me.$el.removeClass(me.$options.showClass),me.$el.addClass(me.$options.hideClass),setTimeout(function(){callback()},me.$options.delayToRemove)):callback(),this},destroy:function(){function callback(){me.$el.remove(),0===$(".lobibox[data-is-modal=true]").length&&($(".lobibox-backdrop").remove(),$("body").removeClass(Lobibox.base.OPTIONS.bodyClass)),me._afterClose()}var me=this;return me._beforeClose(),me.$options.hideClass?(me.$el.removeClass(me.$options.showClass).addClass(me.$options.hideClass),setTimeout(function(){callback()},me.$options.delayToRemove)):callback(),this},setWidth:function(width){var me=this;return me.$el.css("width",me._calculateWidth(width)),me},refreshWidth:function(){this.setWidth(this.$el.width())},refreshHeight:function(){this.setHeight(this.$el.height())},setHeight:function(height){var me=this;return me.$el.css("height",me._calculateHeight(height)).find(".lobibox-body").css("height",me._calculateBodyHeight(me.$el.innerHeight())),me},setSize:function(width,height){var me=this;return me.setWidth(width),me.setHeight(height),me},setPosition:function(left,top){var pos;return"number"==typeof left&&"number"==typeof top?pos={left:left,top:top}:"string"==typeof left&&(pos=this._calculatePosition(left)),this.$el.css(pos),this},setTitle:function(title){return this.$el.find(".lobibox-title").html(title)},getTitle:function(){return this.$el.find(".lobibox-title").html()},show:function(){var me=this,$body=$("body");if(me._beforeShow(),me.$el.removeClass("lobibox-hidden"),$body.append(me.$el),me.$options.buttons){var buttons=me.$el.find(".lobibox-footer").children();buttons[0].focus()}return me.$options.modal&&($body.addClass(Lobibox.base.OPTIONS.bodyClass),me._addBackdrop()),me.$options.delay!==!1&&setTimeout(function(){me.destroy()},me.$options.delay),me._afterShow(),me}};Lobibox.base={},Lobibox.base.OPTIONS={bodyClass:"lobibox-open",modalClasses:{error:"lobibox-error",success:"lobibox-success",info:"lobibox-info",warning:"lobibox-warning",confirm:"lobibox-confirm",progress:"lobibox-progress",prompt:"lobibox-prompt","default":"lobibox-default",window:"lobibox-window"},buttonsAlign:["left","center","right"],buttons:{ok:{"class":"lobibox-btn lobibox-btn-default",text:"OK",closeOnClick:!0},cancel:{"class":"lobibox-btn lobibox-btn-cancel",text:"Cancel",closeOnClick:!0},yes:{"class":"lobibox-btn lobibox-btn-yes",text:"Yes",closeOnClick:!0},no:{"class":"lobibox-btn lobibox-btn-no",text:"No",closeOnClick:!0}},icons:{bootstrap:{confirm:"glyphicon glyphicon-question-sign",success:"glyphicon glyphicon-ok-sign",error:"glyphicon glyphicon-remove-sign",warning:"glyphicon glyphicon-exclamation-sign",info:"glyphicon glyphicon-info-sign"},fontAwesome:{confirm:"fa fa-question-circle",success:"fa fa-check-circle",error:"fa fa-times-circle",warning:"fa fa-exclamation-circle",info:"fa fa-info-circle"}}},Lobibox.base.DEFAULTS={horizontalOffset:5,verticalOffset:5,width:600,height:"auto",closeButton:!0,draggable:!1,customBtnClass:"lobibox-btn lobibox-btn-default",modal:!0,debug:!1,buttonsAlign:"center",closeOnEsc:!0,delayToRemove:200,delay:!1,baseClass:"animated-super-fast",showClass:"zoomIn",hideClass:"zoomOut",iconSource:"bootstrap",onShow:null,shown:null,beforeClose:null,closed:null},LobiboxPrompt.prototype=$.extend({},LobiboxBase,{constructor:LobiboxPrompt,_processInput:function(options){var me=this,mergedOptions=LobiboxBase._processInput.call(me,options);return mergedOptions.buttons={ok:Lobibox.base.OPTIONS.buttons.ok,cancel:Lobibox.base.OPTIONS.buttons.cancel},options=$.extend({},mergedOptions,LobiboxPrompt.DEFAULT_OPTIONS,options)},_init:function(){var me=this;LobiboxBase._init.call(me),me.show()},_afterShow:function(){var me=this;me._setContent(me._createInput())._position(),me.$input.focus(),LobiboxBase._afterShow.call(me)},_createInput:function(){var label,me=this;return me.$input=me.$options.multiline?$("<textarea></textarea>").attr("rows",me.$options.lines):$('<input type="'+me.$promptType+'"/>'),me.$input.addClass("lobibox-input").attr(me.$options.attrs),me.$options.value&&me.setValue(me.$options.value),me.$options.label&&(label=$("<label>"+me.$options.label+"</label>")),$("<div></div>").append(label,me.$input)},setValue:function(val){return this.$input.val(val),this},getValue:function(){return this.$input.val()},isValid:function(){var me=this,$error=me.$el.find(".lobibox-input-error-message");return me.$options.required&&!me.getValue()?(me.$input.addClass("invalid"),0===$error.length&&(me.$el.find(".lobibox-body").append('<p class="lobibox-input-error-message">'+me.$options.errorMessage+"</p>"),me._position(),me.$input.focus()),!1):(me.$input.removeClass("invalid"),$error.remove(),me._position(),me.$input.focus(),!0)}}),LobiboxPrompt.DEFAULT_OPTIONS={width:400,attrs:{},value:"",multiline:!1,lines:3,type:"text",label:"",required:!0,errorMessage:"The field is required"},LobiboxConfirm.prototype=$.extend({},LobiboxBase,{constructor:LobiboxConfirm,_processInput:function(options){var me=this,mergedOptions=LobiboxBase._processInput.call(me,options);return mergedOptions.buttons={yes:Lobibox.base.OPTIONS.buttons.yes,no:Lobibox.base.OPTIONS.buttons.no},options=$.extend({},mergedOptions,Lobibox.confirm.DEFAULTS,options)},_init:function(){var me=this;LobiboxBase._init.call(me),me.show()},_afterShow:function(){var me=this,d=$("<div></div>");me.$options.iconClass&&d.append($('<div class="lobibox-icon-wrapper"></div>').append('<i class="lobibox-icon '+me.$options.iconClass+'"></i>')),d.append('<div class="lobibox-body-text-wrapper"><span class="lobibox-body-text">'+me.$options.msg+"</span></div>"),me._setContent(d.html()),me._position(),LobiboxBase._afterShow.call(me)}}),Lobibox.confirm.DEFAULTS={title:"Question",width:500},LobiboxAlert.prototype=$.extend({},LobiboxBase,{constructor:LobiboxAlert,_processInput:function(options){var me=this,mergedOptions=LobiboxBase._processInput.call(me,options);return mergedOptions.buttons={ok:Lobibox.base.OPTIONS.buttons.ok},options=$.extend({},mergedOptions,Lobibox.alert.OPTIONS[me.$type],Lobibox.alert.DEFAULTS,options)},_init:function(){var me=this;LobiboxBase._init.call(me),me.show()},_afterShow:function(){var me=this,d=$("<div></div>");me.$options.iconClass&&d.append($('<div class="lobibox-icon-wrapper"></div>').append('<i class="lobibox-icon '+me.$options.iconClass+'"></i>')),d.append('<div class="lobibox-body-text-wrapper"><span class="lobibox-body-text">'+me.$options.msg+"</span></div>"),me._setContent(d.html()),me._position(),LobiboxBase._afterShow.call(me)}}),Lobibox.alert.OPTIONS={warning:{title:"Warning"},info:{title:"Information"},success:{title:"Success"},error:{title:"Error"}},Lobibox.alert.DEFAULTS={},LobiboxProgress.prototype=$.extend({},LobiboxBase,{constructor:LobiboxProgress,_processInput:function(options){var me=this,mergedOptions=LobiboxBase._processInput.call(me,options);return options=$.extend({},mergedOptions,Lobibox.progress.DEFAULTS,options)},_init:function(){var me=this;LobiboxBase._init.call(me),me.show()},_afterShow:function(){var me=this;me.$progressBarElement=me.$options.progressTpl?$(me.$options.progressTpl):me._createProgressbar();var label;me.$options.label&&(label=$("<label>"+me.$options.label+"</label>"));var innerHTML=$("<div></div>").append(label,me.$progressBarElement);me._setContent(innerHTML),me._position(),LobiboxBase._afterShow.call(me)},_createProgressbar:function(){var me=this,outer=$('<div class="lobibox-progress-bar-wrapper lobibox-progress-outer"></div>').append('<div class="lobibox-progress-bar lobibox-progress-element"></div>');return me.$options.showProgressLabel&&outer.append('<span class="lobibox-progress-text" data-role="progress-text"></span>'),outer},setProgress:function(progress){var me=this;if(100!==me.$progress)return progress=Math.min(100,Math.max(0,progress)),me.$progress=progress,me._triggerEvent("progressUpdated"),100===me.$progress&&me._triggerEvent("progressCompleted"),me.$el.find(".lobibox-progress-element").css("width",progress.toFixed(1)+"%"),me.$el.find('[data-role="progress-text"]').html(progress.toFixed(1)+"%"),me},getProgress:function(){return this.$progress}}),Lobibox.progress.DEFAULTS={width:500,showProgressLabel:!0,label:"",progressTpl:!1,progressUpdated:null,progressCompleted:null},LobiboxWindow.prototype=$.extend({},LobiboxBase,{constructor:LobiboxWindow,_processInput:function(options){var me=this,mergedOptions=LobiboxBase._processInput.call(me,options);return options.content&&"function"==typeof options.content&&(options.content=options.content()),options.content instanceof jQuery&&(options.content=options.content.clone()),options=$.extend({},mergedOptions,Lobibox.window.DEFAULTS,options)},_init:function(){var me=this;LobiboxBase._init.call(me),me.setContent(me.$options.content),me.$options.url&&me.$options.autoload?(me.$options.showAfterLoad||me.show(),me.load(function(){me.$options.showAfterLoad&&me.show()})):me.show()},_afterShow:function(){var me=this;me._position(),LobiboxBase._afterShow.call(me)},setParams:function(params){var me=this;return me.$options.params=params,me},getParams:function(){var me=this;return me.$options.params},setLoadMethod:function(method){var me=this;return me.$options.loadMethod=method,me},getLoadMethod:function(){var me=this;return me.$options.loadMethod},setContent:function(content){var me=this;return me.$options.content=content,me.$el.find(".lobibox-body").html("").append(content),me},getContent:function(){var me=this;return me.$options.content},setUrl:function(url){return this.$options.url=url,this},getUrl:function(){return this.$options.url},load:function(callback){var me=this;return me.$options.url?($.ajax(me.$options.url,{method:me.$options.loadMethod,data:me.$options.params}).done(function(res){me.setContent(res),callback&&"function"==typeof callback&&callback(res)}),me):me}}),Lobibox.window.DEFAULTS={width:480,height:600,content:"",url:"",draggable:!0,autoload:!0,loadMethod:"GET",showAfterLoad:!0,params:{}}}();