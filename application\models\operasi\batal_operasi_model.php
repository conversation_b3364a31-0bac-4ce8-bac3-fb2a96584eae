<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Batal_operasi_model extends CI_Model
{
    protected $procedure_data = [];

    protected $_urutan_kolom = [
        null,
        'master.getNamaLengkap(wlo.norm)',
        'wlo.norm',
        'wlo.diagnosis',
        'wlo.tindakan', 
        'master.getNamaLengkapPegawai(dok.NIP)',
        'mr.DESKRIPSI',
        'po.alasan_batal',
        'date_format(po.cancel_at, "%d-%m-%Y")',
        'TIME(po.cancel_at)',
        'MASTER.getNamaLengkapPegawai(pgn.NIP)'
    ];

    protected $_pencarian_kolom = [
        'master.getNamaLengkap(wlo.norm)',
        'wlo.norm',
        'wlo.diagnosis',
        'wlo.tindakan',
        'master.getNamaLengkapPegawai(dok.NIP)',
        'mr.DESKRIPSI',
        'po.alasan_batal',
        'date_format(po.cancel_at, "%d-%m-%Y")',
        'TIME(po.cancel_at)',
        'MASTER.getNamaLengkapPegawai(pgn.NIP)'
    ];

    /**
     * Ambil data dari stored procedure perjanjian.batal_operasi_hari_ini
     */
    private function get_data_from_procedure($PTUJUAN_RS = '')
    {
        try {
            // Panggil stored procedure dengan parameter PTUJUAN_RS
            $sql = "CALL perjanjian.batal_operasi_hari_ini(?)";
            $query = $this->db->query($sql, [$PTUJUAN_RS]);

            if (!$query) {
                return [];
            }

            $result = $query->result();

            // Handle stored procedure result sets
            while ($this->db->conn_id->more_results()) {
                $this->db->conn_id->next_result();
                if ($res = $this->db->conn_id->store_result()) {
                    $res->free();
                }
            }

            // Jika tidak ada data atau result bukan array, return array kosong
            if (empty($result) || !is_array($result)) {
                return [];
            }

            return $result;

        } catch (Exception $e) {
            // Log error jika diperlukan
            log_message('error', 'Error calling stored procedure batal_operasi_hari_ini: ' . $e->getMessage());
            return [];
        }
    }

    public function batal_operasi($PTUJUAN_RS = '')
    {
        // Ambil data dari stored procedure
        $this->procedure_data = $this->get_data_from_procedure($PTUJUAN_RS);

        // Jika tidak ada data, return empty
        if (empty($this->procedure_data)) {
            return;
        }

        // Apply search filter jika ada
        if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
            $search_value = $_POST['search']['value'];
            $this->procedure_data = array_filter($this->procedure_data, function($row) use ($search_value) {
                // Search dalam semua field yang relevan
                $searchable_fields = [
                    'nama_pasien', 'nomr', 'DIAGNOSA', 'TINDAKAN',
                    'DPJP', 'ruang_rawat', 'alasan_batal', 'tgl_batal', 'jam_batal'
                ];

                foreach ($searchable_fields as $field) {
                    if (isset($row->$field) && stripos($row->$field, $search_value) !== false) {
                        return true;
                    }
                }
                return false;
            });
        }

        // Apply ordering jika ada
        if (isset($_POST['order'])) {
            $order_column = $_POST['order']['0']['column'];
            $order_dir = $_POST['order']['0']['dir'];

            // Map column index to field name
            $column_map = [
                1 => 'nama_pasien',
                2 => 'nomr',
                3 => 'DIAGNOSA',
                4 => 'TINDAKAN',
                5 => 'DPJP',
                6 => 'ruang_rawat',
                7 => 'alasan_batal',
                8 => 'tgl_batal',
                9 => 'jam_batal'
            ];

            if (isset($column_map[$order_column])) {
                $sort_field = $column_map[$order_column];
                usort($this->procedure_data, function($a, $b) use ($sort_field, $order_dir) {
                    $val_a = isset($a->$sort_field) ? $a->$sort_field : '';
                    $val_b = isset($b->$sort_field) ? $b->$sort_field : '';

                    if ($order_dir == 'asc') {
                        return strcasecmp($val_a, $val_b);
                    } else {
                        return strcasecmp($val_b, $val_a);
                    }
                });
            }
        }
    }

    function ambil($PTUJUAN_RS = '')
    {
        $this->batal_operasi($PTUJUAN_RS);

        // Jika tidak ada data dari procedure, return empty array
        if (empty($this->procedure_data)) {
            return [];
        }

        // Apply pagination
        $start = isset($_POST['start']) ? (int)$_POST['start'] : 0;
        $length = isset($_POST['length']) && $_POST['length'] > 0 ? (int)$_POST['length'] : 10;

        return array_slice($this->procedure_data, $start, $length);
    }

    function hitung_tersaring($PTUJUAN_RS = '')
    {
        $this->batal_operasi($PTUJUAN_RS);
        return count($this->procedure_data);
    }

    function hitung_semua($PTUJUAN_RS = '')
    {
        try {
            // Untuk stored procedure, ambil data tanpa filter pencarian
            $sql = "CALL perjanjian.batal_operasi_hari_ini(?)";
            $query = $this->db->query($sql, [$PTUJUAN_RS]);

            if ($query) {
                $result = $query->result();

                // Pastikan result adalah array
                if (!is_array($result)) {
                    return 0;
                }

                $count = count($result);

                // Handle stored procedure result sets
                while ($this->db->conn_id->more_results()) {
                    $this->db->conn_id->next_result();
                    if ($res = $this->db->conn_id->store_result()) {
                        $res->free();
                    }
                }

                return $count;
            }

            return 0;

        } catch (Exception $e) {
            return 0;
        }
    }
}

// End of File Batal_operasi_model.php
// Location: ./application/models/operasi/Batal_operasi_model.php