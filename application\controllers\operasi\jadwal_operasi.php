<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Jadwal_operasi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'admin/Perjanjian_model',
                'operasi/Jadwal_operasi_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Jadwal Operasi Elektif Terkini',
            'isi' => 'operasi/jadwal_operasi/index',
            'session' => $this->session->get_userdata(),
            // 'dokter' => $this->Dokter_model->dokter_operasi(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    public function senin()
    {
        $data = array(
            'title'                => 'Senin',
            // 'isi' 				=> 'dashboard',
        );

        $this->load->view('operasi/jadwal_operasi/senin', $data);
    }

    public function selasa()
    {
        $data = array(
            'title'                => 'Selasa',
            // 'isi' 				=> 'dashboard',
        );

        $this->load->view('operasi/jadwal_operasi/selasa', $data);
    }

    public function rabu()
    {
        $data = array(
            'title'                => 'Rabu',
            // 'isi' 				=> 'dashboard',
        );

        $this->load->view('operasi/jadwal_operasi/rabu', $data);
    }

    public function kamis()
    {
        $data = array(
            'title'                => 'Kamis',
            // 'isi' 				=> 'dashboard',
        );

        $this->load->view('operasi/jadwal_operasi/kamis', $data);
    }

    public function jumat()
    {
        $data = array(
            'title'                => 'Jumat',
            // 'isi' 				=> 'dashboard',
        );

        $this->load->view('operasi/jadwal_operasi/jumat', $data);
    }


    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        $hari = $post['hari'];
        $draw = intval($post['draw']);
        $PTUJUAN_RS = isset($post['PTUJUAN_RS']) ? $post['PTUJUAN_RS'] : '';

        $tabel = $this->Jadwal_operasi_model->ambil($hari, $PTUJUAN_RS);
        $no = $post['start'];
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            $data[] = [
                ++$no . '.',
                $t->ruang_op,
                $t->waktu_operasi,
                $t->NAMAPASIEN,
                $t->NOMR,
                $t->Tgl_lahir_umur,
                $t->diagnosa,
                $t->tindakan,
                $t->OPERATOR,
                $t->DPJP_ANASTESI,
                $t->JENIS_ANASTESI,
                $t->durasi_operasi
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Jadwal_operasi_model->hitung_semua($hari, $PTUJUAN_RS),
            'recordsFiltered' => $this->Jadwal_operasi_model->hitung_tersaring($hari, $PTUJUAN_RS),
            'data' => $data
        ];

        echo json_encode($output);
    }
}

// End of File Jadwal_operasi.php
// Location: ./application/controllers/operasi/Jadwal_operasi.php