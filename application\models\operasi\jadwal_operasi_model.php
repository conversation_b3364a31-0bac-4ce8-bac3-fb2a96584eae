<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Jadwal_operasi_model extends CI_Model
{
    protected $procedure_data = [];

    protected $_urutan_kolom = [
        null,
        'ruang_op',
        'waktu_operasi',
        'NAMAPASIEN',
        'NOMR',
        'Tgl_lahir_umur',
        'diagnosa',
        'tindakan',
        'OPERATOR',
        'DPJP_ANASTESI',
        'JENIS_ANASTESI',
        'durasi_operasi'
    ];

    protected $_pencarian_kolom = [
        'ruang_op',
        'waktu_operasi',
        'master.getNamaLengkap(ps.NORM)',
        'ps.NORM',
        "CONCAT(DATE_FORMAT(ps.TANGGAL_LAHIR, '%d-%m-%Y'), ' (', master.getCariUmur(CURDATE(), DATE(ps.TANGGAL_LAHIR)), ')')",
        'wlo.diagnosis',
        'wlo.tindakan',
        'master.getNamaLengkapPegawai(dok.NIP)',
        'master.getNamaLengkapPegawai(doka.NIP)',
        'var.variabel',
        'po.durasi_operasi'
    ];

    /**
     * Ambil data dari stored procedure perjanjian.jadwal_operasi_perminggu
     */
    private function get_data_from_procedure($PHARI, $PTUJUAN_RS = '')
    {
        try {
            // Panggil stored procedure dengan parameter PHARI dan PTUJUAN_RS
            $sql = "CALL perjanjian.jadwal_operasi_perminggu(?, ?)";
            $query = $this->db->query($sql, [$PHARI, $PTUJUAN_RS]);

            if (!$query) {
                return [];
            }

            $result = $query->result();

            // Handle stored procedure result sets
            while ($this->db->conn_id->more_results()) {
                $this->db->conn_id->next_result();
                if ($res = $this->db->conn_id->store_result()) {
                    $res->free();
                }
            }

            // Jika tidak ada data atau result bukan array, return array kosong
            if (empty($result) || !is_array($result)) {
                return [];
            }

            return $result;

        } catch (Exception $e) {
            // Log error jika diperlukan
            log_message('error', 'Error calling stored procedure jadwal_operasi_perminggu: ' . $e->getMessage());
            return [];
        }
    }

    public function jadwal_operasi($hari, $PTUJUAN_RS = '')
    {
        // Ambil data dari stored procedure
        $this->procedure_data = $this->get_data_from_procedure($hari, $PTUJUAN_RS);

        // Jika tidak ada data, return empty
        if (empty($this->procedure_data)) {
            return;
        }

        // Apply search filter jika ada
        if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
            $search_value = $_POST['search']['value'];
            $this->procedure_data = array_filter($this->procedure_data, function($row) use ($search_value) {
                // Search dalam semua field yang relevan
                $searchable_fields = [
                    'ruang_op', 'waktu_operasi', 'NAMAPASIEN', 'NOMR',
                    'Tgl_lahir_umur', 'diagnosa', 'tindakan', 'OPERATOR',
                    'DPJP_ANASTESI', 'JENIS_ANASTESI', 'durasi_operasi'
                ];

                foreach ($searchable_fields as $field) {
                    if (isset($row->$field) && stripos($row->$field, $search_value) !== false) {
                        return true;
                    }
                }
                return false;
            });
        }

        // Apply ordering jika ada
        if (isset($_POST['order'])) {
            $order_column = $_POST['order']['0']['column'];
            $order_dir = $_POST['order']['0']['dir'];

            // Map column index to field name
            $column_map = [
                1 => 'ruang_op',
                2 => 'waktu_operasi',
                3 => 'NAMAPASIEN',
                4 => 'NOMR',
                5 => 'Tgl_lahir_umur',
                6 => 'diagnosa',
                7 => 'tindakan',
                8 => 'OPERATOR',
                9 => 'DPJP_ANASTESI',
                10 => 'JENIS_ANASTESI',
                11 => 'durasi_operasi'
            ];

            if (isset($column_map[$order_column])) {
                $sort_field = $column_map[$order_column];
                usort($this->procedure_data, function($a, $b) use ($sort_field, $order_dir) {
                    $val_a = isset($a->$sort_field) ? $a->$sort_field : '';
                    $val_b = isset($b->$sort_field) ? $b->$sort_field : '';

                    if ($order_dir == 'asc') {
                        return strcasecmp($val_a, $val_b);
                    } else {
                        return strcasecmp($val_b, $val_a);
                    }
                });
            }
        }
    }

    function ambil($hari, $PTUJUAN_RS = '')
    {
        $this->jadwal_operasi($hari, $PTUJUAN_RS);

        // Jika tidak ada data dari procedure, return empty array
        if (empty($this->procedure_data)) {
            return [];
        }

        // Apply pagination
        $start = isset($_POST['start']) ? (int)$_POST['start'] : 0;
        $length = isset($_POST['length']) && $_POST['length'] > 0 ? (int)$_POST['length'] : 10;

        return array_slice($this->procedure_data, $start, $length);
    }

    function hitung_tersaring($hari, $PTUJUAN_RS = '')
    {
        $this->jadwal_operasi($hari, $PTUJUAN_RS);
        return count($this->procedure_data);
    }

    function hitung_semua($hari, $PTUJUAN_RS = '')
    {
        try {
            // Untuk stored procedure, ambil data tanpa filter pencarian
            $sql = "CALL perjanjian.jadwal_operasi_perminggu(?, ?)";
            $query = $this->db->query($sql, [$hari, $PTUJUAN_RS]);

            if ($query) {
                $result = $query->result();

                // Pastikan result adalah array
                if (!is_array($result)) {
                    return 0;
                }

                $count = count($result);

                // Handle stored procedure result sets
                while ($this->db->conn_id->more_results()) {
                    $this->db->conn_id->next_result();
                    if ($res = $this->db->conn_id->store_result()) {
                        $res->free();
                    }
                }

                return $count;
            }

            return 0;

        } catch (Exception $e) {
            return 0;
        }
    }
}

// End of File Jadwal_operasi_model.php
// Location: ./application/models/operasi/Jadwal_operasi_model.php