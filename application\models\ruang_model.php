<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Ruang_model extends CI_Model
{
    protected $_table = 'master.ruangan';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'asc';

    public function __construct()
    {
        parent::__construct();
    }

    function ambil()
    {
        $this->db->select('ID, JENIS, JENIS_KUNJUNGAN, DESKRIPSI, STATUS');
        $this->db->from($this->_table);
        $this->db->where('JENIS', 5);
        $this->db->where('STATUS', 1);
        $this->db->where_in('JENIS_KUNJUNGAN', ['1', '2', '3', '5', '6', '13', '14']);
        $this->db->order_by('DESKRIPSI', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }
}

// End of File Ruang_model.php
// Location: ./application/models/Ruang_model.php