<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pasien_baru_model extends CI_Model
{
    protected $_table = 'medis.tb_pendaftaran_operasi tpo';
    protected $_primary_key = 'tpo.id';
    protected $_order_by = 'tpo.created_at';
    protected $_order_by_type = 'desc';
    protected $_urutan = ['tpo.created_at' => 'desc'];

    protected $_urutan_kolom = [
        null,
        'nama',
        'nomr',
        'tgl_lahir',
        'diagnosis',
        'rencana_tindakan',
        'dokter_bedah',
        'dokter_bedah_lain',
        'catatan_khusus',
        'waktu_daftar'
    ];

    protected $_pencarian_kolom = [
        'mp.NAMA',
        'pp.NORM',
        'mp.TANGGAL_LAHIR',
        'tpo.diagnosa_medis',
        'tpo.rencana_tindakan_operasi',
        'master.getNamaLengkapPegawai(md.NIP)',
        "CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('`', master.getNamaLengkapPegawai(asisten_bedah.NIP), '`')), ']')",
        'tpo.catatan_khusus',
        'tpo.created_at'
    ];

    public function tabel()
    {
        $this->db->select(
            "mp.NAMA nama, tpo.id, pp.NORM nomr, mp.TANGGAL_LAHIR tgl_lahir, tpo.diagnosa_medis diagnosis,
            tpo.rencana_tindakan_operasi rencana_tindakan, master.getNamaLengkapPegawai(md.NIP) dokter_bedah,
            CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('`', master.getNamaLengkapPegawai(asisten_bedah.NIP), '`')), ']') dokter_bedah_lain,
            tpo.catatan_khusus, tpo.created_at waktu_daftar"
        );
        $this->db->from($this->_table);
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = tpo.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
        $this->db->join('master.dokter md', 'md.ID = tpo.dokter_bedah', 'left');
        $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM', 'left');
        $this->db->join(
            'medis.tb_asisten_pendaftaran_operasi tapo',
            'tapo.id_pendaftaran = tpo.id',
            'left'
        );
        $this->db->join('master.dokter asisten_bedah', 'asisten_bedah.ID = tapo.asisten_bedah', 'left');
        $this->db->where('tpo.status', 1);
        $this->db->where('(IF (tapo.id IS NOT NULL, tapo.status = 1, 1))');
        $this->db->where(
            "tpo.created_at BETWEEN '" . date('Y-m-d') . " 00:00:00' AND '" . date('Y-m-d') . " 23:59:59'"
        );
        $this->db->where_in('tpo.sifat_operasi', [2129, 6080, 6125, 2131]);
        $this->db->group_by('tpo.id');

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil()
    {
        $this->tabel();
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring()
    {
        $this->tabel();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua()
    {
        $this->tabel();
        return $this->db->count_all_results();
    }

    public function pilih_pendaftaran_operasi($nomr)
    {
        $q = $this->input->get('q');
        // echo '<pre>';print_r($q);exit();
        $this->db->select(
            "tpo.id, tpo.nokun, tpo.diagnosa_medis, DATE_FORMAT(tpo.tanggal_operasi, '%d/%m/%Y') tanggal_operasi,
            tpo.created_at waktu_daftar"
        );
        $this->db->from($this->_table);
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = tpo.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
        $this->db->where('tpo.status', 1);
        $this->db->where('pp.NORM', $nomr);
        $this->db->order_by('tpo.tanggal_operasi', 'desc');

        // mulai pencarian
        if ($q) {
            $this->db->like('tpo.nokun', $q);
            $this->db->or_like('tpo.diagnosa_medis', $q);
            $this->db->or_like('tpo.created_at', $q);
        }
        // akhir pencarian

        $query = $this->db->get();
        return $query->result_array();
    }

    public function ambil_pendaftaran_operasi($id)
    {
        $this->db->select(
            "tpo.nokun, tpo.dokter_bedah, tpo.diagnosa_medis, tpo.rencana_tindakan_operasi, tpo.tujuan_operasi,
            tpo.tanggal_operasi, tpo.sifat_operasi, DATE_FORMAT(tpo.created_at, '%Y-%m-%d') waktu_daftar,
            tpo.rencana_jenis_pembiusan, tpo.join_operasi, wlo.id id_waiting_list, wlo.diagnosis, wlo.tindakan,
            wlo.id_dokter, master.getNamaLengkapPegawai(d.NIP) nama_dokter"
        );
        $this->db->from($this->_table);
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter AND d.STATUS = 1', 'left');
        $this->db->where('tpo.id', $id);

        $query = $this->db->get();
        return $query->row_array();
    }
}

// End of File Pasien_baru_model.php
// Location: ./application/models/operasi/pendaftaran_pasien/Pasien_baru_model.php