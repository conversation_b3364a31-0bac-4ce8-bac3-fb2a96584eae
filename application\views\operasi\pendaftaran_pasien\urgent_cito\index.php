<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Pendaftaran Pasien Tindakan&nbsp;<em>Urgent</em>/CITO/Prioritas</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item"><em>Dashboard</em></li>
                <li class="breadcrumb-item">Pendaftaran</li>
                <li class="breadcrumb-item active" aria-current="page">Pasien Tindakan&nbsp;<em>Urgent</em>/CITO/Prioritas</li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card border-danger border-top border-3 border-0">
    <div class="card-body">
        <h5 class="card-title text-danger">Data 1 Bulan Terakhir</h5>
        <hr />
        <!-- mulai tabel -->
        <div class="row mb-3 align-items-center overflow-x-auto">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover w-100" id="tabelPasienUrgentCito">
                    <thead>
                        <tr>
                            <th class="text-center" rowspan="2" scope="col">No.</th>
                            <th class="text-center" rowspan="2" scope="col">Nama Pasien</th>
                            <th class="text-center" rowspan="2" scope="col">No. RM</th>
                            <th class="text-center" rowspan="2" scope="col">Tanggal Lahir (Umur)</th>
                            <th class="text-center" rowspan="2" scope="col">Diagnosis</th>
                            <th class="text-center" rowspan="2" scope="col">Dokter Bedah</th>
                            <th class="text-center" rowspan="2" scope="col">Rencana Tindakan</th>
                            <th class="text-center" rowspan="2" scope="col">Tanggal Daftar</th>
                            <th class="text-center" rowspan="2" scope="col">Sifat Operasi</th>
                            <th class="text-center" rowspan="2" scope="col">Alasan</th>
                            <th class="text-center" rowspan="2" scope="col">Catatan Khusus</th>
                            <th class="text-center" rowspan="2" scope="col">Ruang Rawat</th>
                            <th class="text-center" colspan="3" scope="col">Waktu Tindakan</th>
                        </tr>
                        <tr>
                            <th class="text-center" scope="col">Tanggal</th>
                            <th class="text-center" scope="col">Jam</th>
                            <th class="text-center" scope="col">Durasi (Menit)</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th class="text-center" rowspan="2" scope="col">No.</th>
                            <th class="text-center" rowspan="2" scope="col">Nama Pasien</th>
                            <th class="text-center" rowspan="2" scope="col">No. RM</th>
                            <th class="text-center" rowspan="2" scope="col">Tanggal Lahir (Umur)</th>
                            <th class="text-center" rowspan="2" scope="col">Diagnosis</th>
                            <th class="text-center" rowspan="2" scope="col">Dokter Bedah</th>
                            <th class="text-center" rowspan="2" scope="col">Rencana Tindakan</th>
                            <th class="text-center" rowspan="2" scope="col">Tanggal Daftar</th>
                            <th class="text-center" rowspan="2" scope="col">Sifat Operasi</th>
                            <th class="text-center" rowspan="2" scope="col">Alasan</th>
                            <th class="text-center" rowspan="2" scope="col">Catatan Khusus</th>
                            <th class="text-center" rowspan="2" scope="col">Ruang Rawat</th>
                            <th class="text-center" scope="col">Tanggal</th>
                            <th class="text-center" scope="col">Jam</th>
                            <th class="text-center" scope="col">Durasi (Menit)</th>
                        </tr>
                        <tr>
                            <th class="text-center" colspan="3" scope="col">Waktu Tindakan</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <!-- akhir tabel -->
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function() {
        // mulai tabel
        $('#tabelPasienUrgentCito').DataTable({
            autoWidth: true,
            order: [1, 'asc'],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('operasi/pendaftaran_pasien/Urgent_cito/isi_tabel') ?>",
                type: 'POST'
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [0],
                orderable: false,
            }],
            // akhir pendefinisian kolom
        });
        // akhir tabel
    });
</script>