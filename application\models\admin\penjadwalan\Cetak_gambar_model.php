<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cetak_gambar_model extends CI_Model
{
    public function get_data_operasi($tglawal, $tglakhir, $tujuan_rs)
    {
        // Query yang sama dengan iReport XML
        $sql = "SELECT
            ppo.id id_penjadwalan,wlo.id id_wlo,
            rr.id id_reservasi,
            tpo.id id_pendaftaran_op,
            ppo.tgl_operasi,
            case 	when DAYNAME(ppo.tgl_operasi) = 'Monday' then 'Senin'
            when DAYNAME(ppo.tgl_operasi) = 'Tuesday' then 'Selasa'
            when DAYNAME(ppo.tgl_operasi) = 'Wednesday' then 'Rabu'
            when DAYNAME(ppo.tgl_operasi) = 'Thursday' then 'Kamis'
            when DAYNAME(ppo.tgl_operasi) = 'Friday' then 'Jumat'
            when DAYNAME(ppo.tgl_operasi) = 'Saturday' then 'Sabtu'
            when DAYNAME(ppo.tgl_operasi) = 'Sunday' then 'Minggu'
            ELSE '' end AS hari,
            DATE_FORMAT(ppo.waktu_operasi, '%H:%i') jam_operasi,
            '' tgl_operasi_perjanjian,
            CONCAT('KAMAR OPERASI ', REPLACE(dk.nama, 'OK', ''), ' ( ', dk.nama, ' )') AS kamar_operasi_full,
            dk.nama kamar_operasi,
            rr.tgl_input,
            ppo.created_at,
            master.getNamaLengkap(wlo.norm) nama_pasien,
            wlo.norm,
            date(ps.TANGGAL_LAHIR) tgl_lahir,
            master.getCariUmurTahun(
                if(CURDATE()>ppo.tgl_operasi, ppo.tgl_operasi, CURDATE()) ,
                DATE(ps.TANGGAL_LAHIR)
            ) umur,
            ppo.menunggu_konfirmasi_ruang,
            mr.DESKRIPSI ruang_rawat,
            (SELECT mr.DESKRIPSI ruangan
                FROM pendaftaran.pendaftaran pp
                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                WHERE  ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                AND pp.NORM =wlo.norm AND mr.JENIS_KUNJUNGAN = 3 ORDER BY pk.MASUK ASC LIMIT 1) AS kamar_rawat_real,
            wlo.diagnosis,
            wlo.tindakan,
            master.getNamaLengkapPegawai(d.NIP) dokter_operator,
            master.getNamaLengkapPegawai(da.NIP) dokter_anestesi,
            ppo.jenis_anestesi, REPLACE(anes.variabel,'Anestesi ','') nama_anestesi,
            tpo.catatan_khusus,
            rr.status,
            ppo.status status_penjadwalan,
            GROUP_CONCAT(CONCAT('- ',master.getNamaLengkapPegawai(d1.NIP),'\n','Tindakan: ',apo.rencana_tindakan) SEPARATOR '\n')join_op
            , IFNULL(ppo.tujuan_rs, rr.id_tujuan_dirawat) id_penjamin_operasi, pjop.DESKRIPSI penjamin_operasi
            , IFNULL(pjop1.DESKRIPSI, 'Operasi Reguler dan Swasta') pilih_penjamin_op
        FROM
            perjanjian.penjadwalan_operasi ppo
        LEFT JOIN db_reservasi.tb_reservasi rr ON rr.id = ppo.id_reservasi
        LEFT JOIN db_master.tb_kamar dk ON dk.id = ppo.kamar_operasi
        LEFT JOIN master.ruangan mr ON mr.ID = ppo.ruang_rawat AND mr.JENIS = 5 AND mr.STATUS = 1
        LEFT JOIN master.dokter da ON da.ID = ppo.dr_anestesi
        LEFT JOIN medis.tb_waiting_list_operasi wlo ON wlo.id = ppo.id_waiting_list_operasi
        LEFT JOIN medis.tb_pendaftaran_operasi tpo ON wlo.id_pendaftaran_operasi = tpo.id
        LEFT JOIN master.pasien ps ON ps.NORM = wlo.norm
        LEFT JOIN master.dokter d ON d.ID = wlo.id_dokter
        LEFT JOIN db_master.variabel anes ON anes.id_variabel = ppo.jenis_anestesi AND anes.id_referensi = '622'
        LEFT JOIN  medis.tb_asisten_pendaftaran_operasi apo ON apo.id_pendaftaran = tpo.id AND apo.`status`=1
        LEFT JOIN master.dokter d1 ON d1.ID = apo.asisten_bedah
        LEFT JOIN master.referensi pjop ON pjop.ID = IFNULL(ppo.tujuan_rs, rr.id_tujuan_dirawat) AND pjop.JENIS=81
        LEFT JOIN master.referensi pjop1 ON pjop1.ID = ? AND pjop1.jenis=81
        WHERE
            ppo.tgl_operasi >= ?
            AND ppo.tgl_operasi <= ?
            AND ppo.status IN (1,2,3,4,5)
        GROUP BY ppo.id
        HAVING case when ?=2 then id_penjamin_operasi=2
            when ?=16 then id_penjamin_operasi =16
            when ?=0 then 1
            ELSE 1 END
        ORDER BY tgl_operasi, kamar_operasi,jam_operasi";

        $query = $this->db->query($sql, [$tujuan_rs, $tglawal, $tglakhir, $tujuan_rs, $tujuan_rs, $tujuan_rs]);
        
        if ($query) {
            return $query->result();
        }
        
        return [];
    }

    public function get_penjamin_operasi($tujuan_rs)
    {
        if (empty($tujuan_rs)) {
            return 'Operasi Reguler dan Swasta';
        }

        $sql = "SELECT DESKRIPSI FROM master.referensi WHERE ID = ? AND jenis = 81";
        $query = $this->db->query($sql, [$tujuan_rs]);
        
        if ($query && $query->num_rows() > 0) {
            $result = $query->row();
            return $result->DESKRIPSI;
        }
        
        return 'Operasi Reguler dan Swasta';
    }

    public function get_tindakan_operasi($id_pendaftaran_op)
    {
        // Query untuk subreport tindakan operasi - sesuai dengan sub_ireport.xml
        $sql = "SELECT tpo.id, master.getNamaLengkapPegawai(md1.NIP) dokter_operator, tpo.rencana_tindakan_operasi tindakan, 'Utama' as jenis
            from medis.tb_pendaftaran_operasi tpo
            LEFT JOIN master.dokter md1 ON md1.ID = tpo.dokter_bedah
            WHERE tpo.id = ?

            UNION

            SELECT apo.id_pendaftaran, master.getNamaLengkapPegawai(md.NIP), apo.rencana_tindakan, 'Join'
            FROM medis.tb_asisten_pendaftaran_operasi apo
            LEFT JOIN master.dokter md ON md.ID = apo.asisten_bedah
            WHERE apo.status=1 AND apo.id_pendaftaran=?";

        $query = $this->db->query($sql, [$id_pendaftaran_op, $id_pendaftaran_op]);

        if ($query) {
            return $query->result();
        }

        return [];
    }

    public function get_data_operasi_by_date($tglawal, $tglakhir, $tujuan_rs)
    {
        // Get all data first using the main query
        $all_data = $this->get_data_operasi($tglawal, $tglakhir, $tujuan_rs);

        // Enrich data with subreport information
        foreach ($all_data as $row) {
            if (!empty($row->id_pendaftaran_op)) {
                $row->subreport_data = $this->get_tindakan_operasi($row->id_pendaftaran_op);
            } else {
                $row->subreport_data = [];
            }
        }

        // Group by date
        $grouped_data = [];
        foreach ($all_data as $row) {
            $date = $row->tgl_operasi;
            if (!isset($grouped_data[$date])) {
                $grouped_data[$date] = [];
            }
            $grouped_data[$date][] = $row;
        }

        // Sort by date to ensure consistent ordering
        ksort($grouped_data);

        return $grouped_data;
    }

    public function get_unique_dates($tglawal, $tglakhir, $tujuan_rs)
    {
        // Simplified approach: get unique dates from the main data
        // This avoids complex JOIN issues and ensures consistency
        $all_data = $this->get_data_operasi($tglawal, $tglakhir, $tujuan_rs);

        $unique_dates = [];
        foreach ($all_data as $row) {
            if (!in_array($row->tgl_operasi, $unique_dates)) {
                $unique_dates[] = $row->tgl_operasi;
            }
        }

        // Sort dates
        sort($unique_dates);

        return $unique_dates;
    }
}

// End of File Cetak_gambar_model.php
// Location: ./application/models/admin/penjadwalan/Cetak_gambar_model.php
