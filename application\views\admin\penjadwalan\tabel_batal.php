<table class="table table-striped table-bordered table-hover w-100" id="tabelBatalPenjadwalan">
    <thead>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col"><PERSON><PERSON> (Umur)</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tin<PERSON></th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col"><PERSON>atan <PERSON></th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col">W<PERSON> Pembatalan</th>
            <th class="text-center" scope="col">Dibatalkan oleh</th>
            <th class="text-center" scope="col">Aksi</th>
        </tr>
    </thead>
    <tbody></tbody>
    <tfoot>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Rencana Tanggal Tindakan</th>
            <th class="text-center" scope="col">Kamar Operasi</th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col">Nama Pasien</th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col">Tanggal Lahir (Umur)</th>
            <th class="text-center" scope="col">Ruang Rawat</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col">Catatan Khusus</th>
            <th class="text-center" scope="col">Alasan Pembatalan</th>
            <th class="text-center" scope="col">Waktu Pembatalan</th>
            <th class="text-center" scope="col">Dibatalkan oleh</th>
            <th class="text-center" scope="col">Aksi</th>
        </tr>
    </tfoot>
</table>

<script>
    $(document).ready(function () {
        // mulai tabel
        $('#tabelBatalPenjadwalan').DataTable({
            autoWidth: true,
            order: [4, 'asc'],
            language: {
                url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
            },

            clear: true,
            destroy: true,
            processing: true,
            serverSide: true,
            iDisplayLength: 25,
            search: {
                regex: true
            },

            // mulai ambil data
            ajax: {
                url: "<?= base_url('admin/Penjadwalan/isi_tabel_batal') ?>",
                type: 'POST',
                data: {
                    mulai: '<?= $mulai ?? null ?>',
                    akhir: '<?= $akhir ?? null ?>',
                    id_ruangan: '<?= $id_ruangan?? null?>',
                    tujuanOperasi: '<?= $tujuanOperasi?? null?>',
                }
            },
            // akhir ambil data

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [0, 14],
                orderable: false
            }],
            // akhir pendefinisian kolom
        });
        // akhir tabel

        // mulai aktifkan
        $(document).on('click', '.tombol-aktifkan-penjadwalan', function () {
            let id_penjadwalan = $(this).data('id');

            $.ajax({
                url: "<?= base_url('admin/Penjadwalan/aktifkan') ?>",
                dataType: 'json',
                type: 'POST',
                data: {
                    id: id_penjadwalan
                },
                success: function (data) {
                    if (data.status === 200) {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: data.message,
                            sound: false,
                            title: 'Berhasil'
                        });

                        $('#tabelBatalPenjadwalan').DataTable().ajax.reload();
                    } else {
                        Lobibox.notify('error', {
                            icon: 'bx bx-error',
                            msg: data.message,
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir aktifkan
    });
</script>