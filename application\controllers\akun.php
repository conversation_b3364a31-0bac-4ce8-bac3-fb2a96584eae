<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>kun extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'Pengguna_model',
                'admin/manajemen_pengguna/Pengguna_sippo_model',
            ]
        );
        $this->load->helper('captcha');
    }

    public function index()
    {
        // mulai captcha
        $vals = [
            'word' => substr(str_shuffle('0123456789abcdefghijklmnopqrstuvwxyz'), 0, 4),
            'font_path' => '/assets/fonts/downloaded/tahoma.ttf',
            'img_path' => './assets/images/captcha/',
            'img_url' => base_url('assets/images/captcha/'),
            'img_width' => 120,
            'img_height' => 38,
            'expiration' => 120,
            'word_length' => 4,
            'font_size' => 50,
            'img_id' => 'captcha',
            'pool' => '0123456789abcdefghijklmnopqrstuvwxyz',
            'class' => 'img-fluid rounded',

            'colors' => [
                'background' => [13, 110, 253],
                'border' => [73, 80, 87],
                'text' => [255, 255, 255],
                'grid' => [0, 140, 255]
            ]
        ];
        $captcha = create_captcha($vals);
        // echo '<pre>';print_r($captcha);exit();

        $data = ['captcha' => $captcha['image'] ?? null];
        $this->session->unset_userdata('captcha');
        $this->session->set_userdata('captcha', $captcha['word'] ?? null);
        // akhir captcha

        // echo '<pre>';print_r($data);exit();
        $this->load->view('masuk', $data);
    }

    public function ganti_captcha()
    {
        // Mulai captcha
        $vals = [
            'word' => substr(str_shuffle('0123456789abcdefghijklmnopqrstuvwxyz'), 0, 4),
            'font_path' => '/assets/fonts/downloaded/tahoma.ttf',
            'img_path' => './assets/images/captcha/',
            'img_url' => base_url('assets/images/captcha/'),
            'img_width' => 120,
            'img_height' => 38,
            'expiration' => 120,
            'word_length' => 4,
            'font_size' => 50,
            'img_id' => 'captcha',
            'pool' => '0123456789abcdefghijklmnopqrstuvwxyz',
            'class' => 'img-fluid rounded',

            'colors' => [
                'background' => [13, 110, 253],
                'border' => [73, 80, 87],
                'text' => [255, 255, 255],
                'grid' => [0, 140, 255]
            ]
        ];
        $captcha = create_captcha($vals);

        $this->session->unset_userdata('captcha');
        $this->session->set_userdata('captcha', $captcha['word'] ?? null);
        echo $captcha['image'];
        // Akhir captcha
    }

    public function masuk()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $username = $post['username'];
        $password = $post['password'];
        $this->form_validation->set_rules($this->Pengguna_model->rules());

        if ($this->form_validation->run() == true) {
            $captcha = $this->session->userdata('captcha');
            // echo '<pre>';print_r($captcha);exit();
            if ($post['captcha'] == $captcha) { // periksa captcha
                $result = $this->Pengguna_model->masuk($username, $password);
                // echo '<pre>';print_r($result);exit();

                if ($result['jumlah'] > 0) {
                    $dataPengguna = $this->Pengguna_model->data_pengguna($username);
                    // echo '<pre>';print_r($dataPengguna);exit();

                    if (isset($dataPengguna['id'])) {
                        $session = [
                            'id' => $dataPengguna['id'],
                            'nama' => $dataPengguna['nama'],
                            'username' => $username,
                            'hak_akses' => json_decode($dataPengguna['akses_sippo']),
                            'mode_gelap' => $dataPengguna['gelap'],
                            'id_smf' => $dataPengguna['id_smf'],
                            'jenis' => $dataPengguna['dpjp'],
                            'sudah_masuk' => true,
                        ];
                        // echo '<pre>';print_r($session);exit();
                        $this->session->set_userdata($session);

                        $result = ['status' => 'success'];
                    } else {
                        $result = [
                            'status' => 'error',
                            'errors' => [
                                'akses' => 'Pengguna tidak ditemukan'
                            ],
                        ];
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'errors' => [
                            'akses' => 'Akses ditolak'
                        ],
                    ];
                }
            } else { // captcha tidak sesuai
                $result = [
                    'status' => 'error',
                    'errors' => [
                        'captcha' => 'Captcha tidak sesuai'
                    ],
                ];
            }
        } else {
            $result = [
                'status' => 'failed',
                'errors' => $this->form_validation->error_array(),
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }

    public function keluar()
    {
        unset($_SESSION);
        $this->session->sess_destroy();
        redirect(site_url());
    }

    function pengguna()
    {
        $result = $this->Pengguna_model->list();
        $data = [];
        foreach ($result as $row) {
            $sub_array = [];
            $sub_array['id'] = $row['id_pengguna'];
            $sub_array['text'] = $row['nama'];
            $data[] = $sub_array;
        }
        // echo '<pre>';print_r($data);exit();
        echo json_encode($data);
    }

    function ubah_tema()
    {
        $session = $this->session->get_userdata();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $temaSekarang = $post['tema_sekarang'];
        $modeGelap = null;

        $this->db->trans_begin();

        // mulai ganti tema
        if ($temaSekarang == 0) {
            $modeGelap = 1;
        } elseif ($temaSekarang == 1) {
            $modeGelap = 0;
        }
        // akhir ganti tema

        $data = ['gelap' => $modeGelap];
        // echo '<pre>';print_r($data);exit();
        $this->Pengguna_sippo_model->ubah($session['id'], $data);

        // mulai ubah session
        $this->session->unset_userdata('mode_gelap');
        $this->session->set_userdata('mode_gelap', $modeGelap);
        // akhir ubah session

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal mengubah',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }
}

// End of File Akun.php
// Location: ./application/controllers/Akun.php