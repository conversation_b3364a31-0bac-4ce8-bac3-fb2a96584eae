<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Manajemen Pengguna</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item">Admin</li>
                <li class="breadcrumb-item">Manajemen Pengguna</li>
                <li class="breadcrumb-item">
                    <a href="<?= base_url('admin/manajemen_pengguna/Smf/index') ?>">Per SMF</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Form</li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Atur Manajemen Pengguna</h5>
        <hr>
        <form id="formManajemenSMF">
            <!-- mulai SMF -->
            <div class="row mb-3 align-items-center">
                <label for="smfManajemenSMF" class="col-sm-12 col-md-4 col-form-label">Nama SMF</label>
                <div class="col-sm-12 col-md-8">
                    <select name="smf" id="smfManajemenSMF" class="form-select single-select">
                        <option value=""></option>
                        <?php foreach ($smf as $s) : ?>
                            <option id="smfManajemenSMF<?= $s['id_smf'] ?>" value="<?= $s['id_smf'] ?>">
                                <?= $s['smf'] ?>
                            </option>
                        <?php endforeach ?>
                    </select>
                </div>
            </div>
            <!-- akhir SMF -->
            <!-- mulai akses -->
            <div class="row mb-3 align-items-center">
                <label for="aksesManajemenSMF" class="col-sm-12 col-md-4 col-form-label">Akses</label>
                <div class="col-sm-12 col-md-8">
                    <div class="row mx-0">
                        <?php foreach ($akses as $a) : ?>
                            <div class="form-check form-check-inline col-sm">
                                <input type="checkbox" name="akses[]" class="form-check-input akses-manajemen-smf" id="aksesManajemenSMF<?= $a['id'] ?>" value="<?= $a['id'] ?>">
                                <label for="aksesManajemenSMF<?= $a['id'] ?>"><?= $a['nama'] ?></label>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- akhir akses -->
            <!-- mulai aksi -->
            <div class="row">
                <div class="col-1">
                    <button type="button" class="btn btn-primary" id="simpanManajemenSMF">Simpan</button>
                </div>
            </div>
            <!-- akhir aksi -->
        </form>
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function() {
        // mulai SMF'
        $('#smfManajemenSMF').select2({
            placeholder: 'Pilih SMF'
        });
        // akhir SMF

        <?php if (isset($id_smf)) : ?>
            // mulai ambil data
            $('#smfManajemenSMF').val('<?= $id_smf ?>').trigger('change');

            // mulai akses
            $('.akses-manajemen-smf').prop('checked', false);
            let akses_tersimpan = JSON.parse('<?= $detail["akses_sippo"] ?>');
            jQuery.each(akses_tersimpan, function(index, value) {
                $('#aksesManajemenSMF' + value).prop('checked', true);
            });
            // akhir akses
            // akhir ambil data
        <?php endif ?>

        // mulai simpan
        $('#simpanManajemenSMF').click(function() {
            $.ajax({
                url: "<?= base_url('admin/manajemen_pengguna/Smf/simpan') ?>",
                dataType: 'json',
                type: 'post',
                data: $('#formManajemenSMF').serialize(),
                success: function(data) {
                    if (data.status === 'success') {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: 'Data berhasil disimpan',
                            sound: false,
                            title: 'Berhasil',
                        });
                        window.location.href = "<?= base_url('admin/manajemen_pengguna/Smf/index') ?>";
                    } else {
                        $.each(data.errors, function(index, element) {
                            Lobibox.notify('warning', {
                                icon: 'bx bx-error',
                                msg: element,
                                sound: false,
                                title: 'Peringatan'
                            });
                        });
                    }
                },
            });
        });
        // akhir simpan
    });
</script>