<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Daftar_tunggu extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'Referensi_model',
                'admin/Waiting_list_model',
                'admin/Waiting_list_operasi_rencana_model',
                'operasi/Dokter_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Daftar tunggu',
            'isi' => 'admin/daftar_tunggu/index',
            'session' => $this->session->get_userdata(),
            'dokter' => $this->Dokter_model->dokter(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function tabel()
    {
        $post = $this->input->post();
        $data = [
            'id_dokter' => $post['id_dokter'],           
            'status' => $post['status'] ?? null,
            'id_ruang' => $post['id_ruang']?? null,
            'tujuan' => $post['tujuan']?? null,
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('admin/daftar_tunggu/tabel', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $tujuan = $post['tujuan']?? null;
        $id_dokter = $post['id_dokter'] ?? null;
        $status = $post['status'] ?? null;
        $id_ruang = $post['id_ruang']?? null;
        $draw = intval($post['draw']);
        $tabel = $this->Waiting_list_model->ambil($id_dokter, $status,$id_ruang, $tujuan);
        $no = $post['start'];
        $tgl_rencana = null;
        $isi_tgl_rencana = null;
        $disabled = null;
        $disabled_link = null;
        $tombol_status = null;
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            // mulai tanggal rencana operasi
            if (isset($t->tgl_rencana)) {
                $isi_tgl_rencana = "<ul class='ps-3'></ul>";
                $tgl_rencana = str_replace("`", '"', $t->tgl_rencana);
                foreach (json_decode($tgl_rencana) as $tr) {
                    if ($tr == '0000-00-00') {
                        $isi_tgl_rencana .= '<li>-</li>';
                    } else {
                        $isi_tgl_rencana .= '<li>' . date('d/m/Y', strtotime($tr)) . '</li>';
                    }
                }
                $isi_tgl_rencana .= '</ul>';
            } else {
                $isi_tgl_rencana = '-';
            }
            // akhir tanggal rencana operasi

            // mulai status
            if ($t->status_pasien == 2) { // meninggal
                $disabled = 'disabled';
                $disabled_link = "tabindex='-1' aria-disabled='true'";
                $tombol_status = "<p class='text-danger'>Batal, pasien meninggal</p>";
            } elseif (isset($t->id_laporan_operasi)) { // sudah operasi
                $disabled = 'disabled';
                $disabled_link = "tabindex='-1' aria-disabled='true'";
                $tombol_status = "<p class='text-primary'>Sudah selesai operasi</p>";
            } else {
                if ($t->status == 0) {
                    $disabled = 'disabled';
                    $disabled_link = "tabindex='-1' aria-disabled='true'";
                    $tombol_status = "<p class='text-danger'>Batal</p>";
                } else {
                    $tombol_status = "<div class='btn-group-vertical btn-group-sm' role='group'>";

                    // Handle different status states
                    switch ($t->status) {
                        case 1: // menunggu jadwal
                            $disabled = null;
                            $disabled_link = null;
                            $tombol_status .= '<button class="btn btn-info">Menunggu jadwal</button>';
                            $tombol_status .= "<a class='btn btn-outline-info tombol-status-wl' href='javaScript:;' role='button' data-waiting='$t->id' data-jenis='2' data-status='1'><i class='bx bx-chevron-right'></i></a>";
                            break;

                        case 2: // pra operasi
                            $disabled = null;
                            $disabled_link = null;
                            $tombol_status .= "<a class='btn btn-outline-primary tombol-status-wl' href='javaScript:;' role='button' data-waiting='$t->id' data-jenis='1' data-status='2'><i class='bx bx-chevron-left'></i></a>";
                            $tombol_status .= "<button class='btn btn-primary'>Pra operasi</button>";
                            $tombol_status .= "<a class='btn btn-outline-primary tombol-status-wl' href='javaScript:;' role='button' data-waiting='$t->id' data-jenis='2' data-status='2'><i class='bx bx-chevron-right'></i></a>";
                            break;

                        case 3: // pasca operasi
                            $disabled = 'disabled';
                            $disabled_link = "tabindex='-1' aria-disabled='true'";
                            $tombol_status .= "<a class='btn btn-outline-success tombol-status-wl' href='javaScript:;' role='button' data-waiting='$t->id' data-jenis='1' data-status='3'><i class='bx bx-chevron-left'></i></a>";
                            $tombol_status .= "<button class='btn btn-success'>Pasca operasi</button>";
                            break;
                    }

                    $tombol_status .= '</div>';
                }
            }
            // akhir status

            $data[] = [
                ++$no . '.',
                $t->tujuan_rs,
                $t->nama,
                $t->norm,
                $t->jk,
                date('d/m/Y', strtotime($t->tgl_lahir)),
                // $t->nokun,
                // $t->ruangan,
                $t->diagnosis ?? '-',
                $t->tindakan ?? '-',
                $t->sifat_operasi ?? '-',
                date('d/m/Y', strtotime($t->tgl_daftar)),
                $isi_tgl_rencana,
                date('d/m/Y, H.i.s', strtotime($t->created_at)),
                $t->keterangan ?? '-',
                $t->catatan_khusus ?? '-',
                $tombol_status,
                "<div class='btn-group-vertical' role='group'>
                    <a href='" . base_url('admin/daftar_tunggu/form/' . $t->id) . "' role='button' class='btn btn-warning tblUbahWl $disabled' $disabled_link>
                        Ubah
                    </a>
                    <button type='button' class='btn btn-danger tbl-batal-wl' data-id='$t->id' data-bs-toggle='modal' data-bs-target='#modalBatalWl' $disabled>
                        Batal
                    </button>
                </div>"
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Waiting_list_model->hitung_semua($id_dokter, $status, $id_ruang, $tujuan),
            'recordsFiltered' => $this->Waiting_list_model->hitung_tersaring($id_dokter, $status, $id_ruang, $tujuan),
            'data' => $data
        ];

        echo json_encode($output);
    }

    function form($id = null)
    {
        $data = [
            'judul' => 'Tambah Daftar tunggu',
            'isi' => 'admin/daftar_tunggu/form',
            'session' => $this->session->get_userdata(),
            'dokter' => $this->Dokter_model->dokter(),
            'tujuan_operasi' => $this->Referensi_model->referensi(1456),
            'sifat_operasi' => $this->Referensi_model->referensi(621),
            'join_operasi' => $this->Referensi_model->referensi(1851),
        ];

        // mulai periksa id
        if (isset($id)) {
            $data['id'] = $id;
            $data['detail'] = $this->Waiting_list_model->detail($id);
            $data['tindakan'] = $this->Waiting_list_model->ambil_tindakan($id);
        }
        // akhir periksa id

        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function isi_tabel_rencana()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'];
        $draw = intval($post['draw']);
        $tabel = $this->Waiting_list_operasi_rencana_model->ambil($id);
        $no = $post['start'];
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            $data[] = [
                ++$no . '.',
                date('d/m/Y', strtotime($t->tanggal_rencana)),
                $t->keterangan != '' ? $t->keterangan : '-',
                date('d/m/Y', strtotime($t->created_at))
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Waiting_list_operasi_rencana_model->hitung_semua($id),
            'recordsFiltered' => $this->Waiting_list_operasi_rencana_model->hitung_tersaring($id),
            'data' => $data
        ];

        echo json_encode($output);
    }

    function simpan()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $session = $this->session->get_userdata();
        $id_pengguna = $session['id'];

        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->Waiting_list_model->rules());
            if (isset($post['status'])) {
                if ($post['status'] == 2) {
                    $this->form_validation->set_rules($this->Waiting_list_operasi_rencana_model->rules()); // Jika status ditunda
                }
            }

            if ($this->form_validation->run() == true) {
                if ($post['id'] != null) {
                    // mulai data daftar tunggu
                    $id_wl = $post['id'];
                    $data_wl = [
                        'diagnosis' => $post['diagnosis'],
                        'tindakan' => $post['tindakan'],
                        'tujuan_operasi' => $post['tujuan_operasi'],
                        'sifat_operasi' => $post['sifat_operasi'],
                    ];

                    // mulai periksa status
                    if (isset($post['status'])) {
                        $data_wl['status'] = $post['status'];
                    }
                    // akhir periksa status

                    // echo '<pre>';print_r($data_wl);exit();
                    $this->Waiting_list_model->ubah($id_wl, $data_wl);
                    // akhir data daftar tunggu

                    // mulai ubah data
                    if ($post['status'] == 2) {
                        // mulai hapus data sebelumnya jika jadwal berubah
                        $data_wl = ['status' => 0];
                        // echo '<pre>';print_r($data_wl);exit();
                        $this->Waiting_list_operasi_rencana_model->ubah($id_wl, $data_wl);
                        // akhir hapus data sebelumnya jika jadwal berubah
                    } elseif (isset($post['id_wlor'])) { // Jika ada id rencana
                        $data_wl = [
                            'keterangan' => $post['keterangan'] ?? null,
                            'status' => $post['status'],
                        ];
                        // echo '<pre>';print_r($data_wl);exit();
                        $this->Waiting_list_operasi_rencana_model->ubah_dari_id($post['id_wlor'], $data_wl);
                    }
                    // akhir ubah data
                } else {
                    // mulai data daftar tunggu
                    $data_wl = [
                        'norm' => $post['norm'],
                        'nokun' => $post['nokun'],
                        'id_pendaftaran_operasi' => $post['ppo'] ?? null,
                        'id_dokter' => $post['dokter'] ?? null,
                        'diagnosis' => $post['diagnosis'],
                        'tindakan' => $post['tindakan'],
                        'tujuan_operasi' => $post['tujuan_operasi'],
                        'sifat_operasi' => $post['sifat_operasi'],
                        'tanggal' => $post['tanggal'],
                        'status' => 1,
                        'oleh' => $id_pengguna,
                    ];
                    // echo '<pre>';print_r($data_wl);exit();
                    $id_wl = $this->Waiting_list_model->simpan($data_wl);
                    // akhir data daftar tunggu
                }

                // mulai data daftar tunggu operasi rencana
                if ($post['status'] == 1 || $post['status'] == 2) { // Jika simpan atau ubah operasi
                    $data_wlor = [
                        'id_wlo' => $id_wl,
                        'tanggal_rencana' => $post['tgl_rencana'],
                        'keterangan' => $post['keterangan'] ?? null,
                        'status' => 1,
                        'oleh' => $id_pengguna,
                    ];
                    // echo '<pre>';print_r($data_wlor);exit();
                    $this->Waiting_list_operasi_rencana_model->simpan($data_wlor);
                }
                // akhir data daftar tunggu operasi rencana

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array(),
                ];
            }
            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    function batal()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'];
        $data_wl = ['status' => 0];
        // echo '<pre>';print_r($data_wl);exit();
        $this->Waiting_list_model->ubah($id, $data_wl);

        $data_wlor = [
            'status' => 0,
            'keterangan' => $post['keterangan']
        ];
        // echo '<pre>';print_r($data_wlor);exit();
        $this->Waiting_list_operasi_rencana_model->ubah($id, $data_wlor);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal mengubah status',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil mengubah status!',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }

    function ubah_status()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $jenis = $post['jenis'];

        // mulai status
        $status = $post['status'];
        if ($jenis == 1) {
            $status--;
        } elseif ($jenis == 2) {
            $status++;
        }
        // akhir status

        $data = ['status' => $status];
        $this->Waiting_list_model->ubah($post['id'], $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal mengubah status',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil mengubah status!',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }

    function pilih_daftar_tunggu($norm)
    {
        $result = $this->Waiting_list_model->pilih($norm);
        $data = [];
        foreach ($result as $r) {
            $sub_array = [];
            $sub_array['id'] = $r['id'];
            $sub_array['text'] = $r['nokun'] . ' - ' . $r['tgl_dibuat'];
            $data[] = $sub_array;
        }
        // echo '<pre>';print_r($data);exit();
        echo json_encode($data);
    }

    function ambil_pilihan()
    {
        $id = $this->input->post('id');
        // echo '<pre>';print_r($post);exit();
        $data = [
            'detail' => $this->Waiting_list_model->detail($id),
            'tindakan' => $this->Waiting_list_model->ambil_tindakan($id)
        ];
        // echo '<pre>';print_r($data);exit();
        echo json_encode($data);
    }
}

// End of File Daftar_tunggu.php
// Location: ./application/controllers/admin/Daftar_tunggu.php