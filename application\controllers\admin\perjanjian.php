<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON><PERSON><PERSON> extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'Jadwal_model',
                'Pasien_model',
                'Referensi_model',
                'admin/Perjanjian_model',
                'admin/Reservasi_model',
                'admin/Reservasi_antrian_model',
                'admin/Pendaftaran_operasi_model',
                'admin/Waiting_list_model',
                'operasi/Dokter_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => '<PERSON><PERSON><PERSON><PERSON> Operasi',
            'isi' => 'admin/perjanjian/index',
            'session' => $this->session->get_userdata(),
            'dokter' => $this->Dokter_model->dokter(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function form($id_perjanjian = null)
    {
        $data = [
            'judul' => 'Form Perjanjian Operasi',
            'isi' => 'admin/perjanjian/form',
            'session' => $this->session->get_userdata(),
            'ambil_norm' => $this->Waiting_list_model->ambil_norm(),
            'dokter' => $this->Dokter_model->dokter(),
            'tujuan_operasi' => $this->Referensi_model->referensi(1456),
            'tujuan_masuk' => $this->Referensi_model->ambil(81, [2, 16]),
            'kelas_rawat' => $this->Referensi_model->ambil(19, null),
            'join_operasi' => $this->Referensi_model->referensi(1851),
        ];

        // mulai periksa ID perjanjian
        if (isset($id_perjanjian)) {
            $data['id_perjanjian'] = $id_perjanjian;
            $data['detail'] = $this->Reservasi_model->detail($id_perjanjian);
        }
        // akhir periksa ID perjanjian
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    public function get_ruangan() 
    {
        $ruangan = $this->Referensi_model->get_ruangan_operasi();
        echo json_encode($ruangan);
    }

    function cara_bayar()
    {
        $result = $this->Referensi_model->ambil(10, null);
        $data = [];
        foreach ($result as $row) {
            $sub_array = [];
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        // echo '<pre>';print_r($data);exit();
        echo json_encode($data);
    }

    function kode_booking()
    {
        $kode_booking = $this->Perjanjian_model->ambil_kode_booking();
        echo $kode_booking->kode_booking;
    }

    function ambil_data_pasien()
    {
        $norm = $this->input->post('norm');
        $pasien = $this->Pasien_model->ambil_data($norm);
        echo json_encode($pasien);
    }

    function ambil_data_pasien_daftar_tunggu()
    {
        $norm = $this->input->post('norm');
        $pasien = $this->Pasien_model->ambil_data_daftar_tunggu($norm);
        // echo '<pre>';print_r($pasien);exit();
        echo json_encode($pasien);
    }

    function jadwal()
    {
        $result = $this->Jadwal_model->ambil();
        // echo '<pre>';print_r($result);exit();
        $data = [];
        $hari = [
            'Monday' => 'Senin',
            'Tuesday' => 'Selasa',
            'Wednesday' => 'Rabu',
            'Thursday' => 'Kamis',
            'Friday' => 'Jumat',
            'Saturday' => 'Sabtu',
            'Sunday' => 'Minggu',
        ];

        foreach ($result as $row) {
            $sub_array = [];
            $sub_array['id'] = $row->ID;
            $sub_array['tanggal'] = $row->TANGGAL;
            $sub_array['day'] = $row->DAY;
            $sub_array['waktu'] = $row->WAKTU;
            $sub_array['jumlah'] = $row->JUMLAH;
            $sub_array['kuota'] = $row->KUOTA;
            $sub_array['waktu_sore'] = $row->WAKTU_SORE;
            $sub_array['jumlah_sore'] = $row->JUMLAH_SORE;
            $sub_array['kuota_sore'] = $row->KUOTASORE;
            $sub_array['ruangan'] = $row->DESKRIPSI;
            $sub_array['id_ruangan'] = $row->ID_RUANGAN;
            $sub_array['color'] = $row->COLOR == null ? '#218496' : $row->COLOR;
            $sub_array['hari'] = $hari[$row->HARI];
            $data[] = $sub_array;
        }
        echo json_encode($data);
        // echo '<pre>';print_r($data);exit();
    }

    function tambah_jadwal()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['jid'];
        $result = [];
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->Jadwal_model->rules());
            $cek = $this->Jadwal_model->cek($post['jdokter'], $post['jruangan'], $post['jtanggal']);

            if ($this->form_validation->run() == true) {
                $data = [
                    'DOKTER' => $post['jdokter'] ?? null,
                    // 'RUANGAN' => '105090101',
                    'RUANGAN' => $post['jruangan']?? null,
                    'TANGGAL' => $post['jtanggal'] ?? null,
                    'AWAL' => !empty($post['awal']) ? $post['awal'] : null,
                    'AKHIR' => !empty($post['akhir']) ? $post['akhir'] : null,
                    'KUOTA' => $post['kuota'] != '' ? $post['kuota'] : null,
                    'OLEH' => $this->session->userdata('id'),
                ];
                // echo '<pre>';print_r($data);exit();

                if (!empty($id)) {
                    $hari_ini = date('Y-m-d');
                    $hari_sebelumnya = date('Y-m-d', strtotime($post['jtanggal'] . '-1 day'));

                    if ($hari_ini >= $hari_sebelumnya) {
                        $result = [
                            'status' => 'failed',
                            'message' => 'Gagal mengubah data jadwal dokter',
                        ];
                    } else {
                        $this->Jadwal_model->ubah($id, $data);
                        if ($this->db->trans_status() === false) {
                            $this->db->trans_rollback();
                            $result = [
                                'status' => 'failed',
                                'message' => 'Gagal mengubah data jadwal dokter',
                            ];
                        } else {
                            // $this->db->trans_commit();
                            $result = [
                                'status' => 200,
                                'message' => 'Berhasil mengubah data jadwal dokter'
                            ];
                        }
                    }
                } else {
                    if ($cek['status'] == 200) {
                        $this->Jadwal_model->simpan($data);

                        if ($this->db->trans_status() === false) {
                            $this->db->trans_rollback();
                            $result = [
                                'status' => 'failed',
                                'message' => 'Gagal menyimpan data jadwal dokter',
                            ];
                        } else {
                            $this->db->trans_commit();
                            $result = [
                                'status' => 200,
                                'message' => 'Berhasil menyimpan data jadwal dokter'
                            ];
                        }
                    } else {
                        $result = [
                            'status' => 'failed',
                            'message' => 'Data jadwal dokter sudah ada',
                        ];
                    }
                }
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array(),
                ];
            }

            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    function cek()
    {
        $result = [];
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->Reservasi_model->rules());
            if ($this->form_validation->run() == true) {
                $result = $this->Perjanjian_model->cek();
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array(),
                ];
            }

            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    function aksi($param)
    {
        // echo '<pre>';print_r($param);exit();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $session = $this->session->get_userdata();
        $id_pengguna = $session['id'];
        $prosedur = 0;
        $result = [];
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->Reservasi_model->rules());

            if ($this->form_validation->run() == true) {
                if ($param == 'tambah' || $param == 'ubah') {
                    $cek = $this->Perjanjian_model->cek();
                    // echo '<pre>';print_r($cek);exit();
                    if ($cek['status'] == 200) {
                        if (isset($cek['data'])) {
                            $status_sore = $cek['data']['status_sore'];
                            $_POST['statusSore'] = $status_sore;
                        }

                        // mulai ubah tanggal operasi
                        $data_tpo = [
                            'diagnosa_medis' => $post['diagnosis'],
                            'rencana_tindakan_operasi' => $post['tindakan'],
                            'tanggal_operasi' => $post['tgl_operasi'],
                            'join_operasi' => $post['join_operasi'],
                        ];
                        // echo '<pre>';print_r($data_tpo);exit();
                        $this->Pendaftaran_operasi_model->ubah($post['id_pendaftaran_operasi'], $data_tpo);
                        // akhir ubah tanggal operasi

                        // mulai data perjanjian
                        $data_perjanjian = [
                            'NOMR' => $post['norm'],
                            'NAMAPASIEN' => $post['nama'],
                            'ID_DOKTER' => $post['dokter'],
                            'ID_RUANGAN' => $post['id_ruang'],
                            'ID_WAITING_LIST_OPERASI' => $post['id_waiting_list'],
                            'ID_PENDAFTARAN_OPERASI' => $post['id_pendaftaran_operasi'],
                            'TANGGAL' => $post['tgl_operasi'],
                            'KETERANGAN' => '-',
                            'RENCANA' => 11,
                            'TUJUANOPERASI' => $post['tujuan_operasi'] ?? null,
                            'NOMOR' => $post['no_telp'],
                            'TANGGALRAWATINAP' => $post['tgl_rencanaMasuk'],
                            'DIAGNOSA' => $post['diagnosis'],
                            'TINDAKAN' => $post['tindakan'],
                            'OLEH' => $id_pengguna,
                            'STATUS' => 1,
                            'NOKONTROL' => $this->Perjanjian_model->no_kontrol(),
                            'NOKONTROLDOKTER' => null,
                            'PERSETUJUAN' => $post['persetujuan_instalasi'] ?? 0,
                            'STATUS_SORE' => 0,
                        ];
                        // echo '<pre>';print_r($data_perjanjian);exit();
                        $id_perjanjian = $this->Perjanjian_model->simpan($data_perjanjian);
                        // akhir data perjanjian

                        // mulai data reservasi
                        $data_ereservasi = [
                            'id_perjanjian' => $id_perjanjian,
                            'norm' => $post['norm'],
                            'id_waiting_list_operasi' => $post['id_waiting_list'],
                            'id_pendaftaran_operasi' => $post['id_pendaftaran_operasi'],
                            'nama' => $post['nama'],
                            'id_jk' => $post['id_jk'],
                            'tgl_lahir' => $post['tgl_lahir'],
                            'no_telp' => $post['no_telp'],
                            'tgl_rencanaMasuk' => $post['tgl_rencanaMasuk'],
                            'tgl_pcr' => '',
                            'keterangan' => null,
                            'id_cara_bayar' => $post['id_cara_bayar'],
                            'id_kelas' => $post['id_kelas'],
                            'kode_booking' => $post['kode_booking'],
                            'id_dokter' => $post['dokter'],
                            'id_tujuan_dirawat' => $post['rencana_operasi'],
                            'pengguna' => $id_pengguna,
                        ];
                        // echo '<pre>';print_r($data_ereservasi);exit();
                        $this->Reservasi_model->simpan($data_ereservasi);
                        // akhir data reservasi

                        $cek_id_ruang = [105020704, 105020705, 105120101, 105020201, 105060101];
                        if (in_array($post['id_ruang'], $cek_id_ruang) && $this->Reservasi_antrian_model->cek_antrean() == 0) {
                            if ($post['id'] == 105060101) {
                                $ruang = 'A';
                            } elseif ($post['id'] == 105020201) {
                                $ruang = 'B';
                            } elseif ($post['id'] == 105020705) {
                                $ruang = 'C'; // Poliklinik Onkologi 2
                            } elseif ($post['id'] == 105020704) {
                                $ruang = 'D'; // Poliklinik Onkologi 1
                            } elseif ($post['id_ruang'] == 105120101) {
                                $ruang = 'E'; // Instalasi Radio Terapi
                            }

                            // mulai simpan reservasi antrean
                            $data_antrean = [
                                'PASIEN' => $post['norm'],
                                'NAMA' => $post['nama'],
                                'KONTAK' => $post['no_telp'],
                                'TANGGAL_KUNJUNGAN' => $post['tgl_operasi'],
                                'TANGGAL_KUNJUNGAN_ANTRIAN' => $post['tgl_operasi'],
                                'RUANGAN' => $ruang,
                                'DOKTER' => null,
                                'STATUS' => 1,
                            ];
                            // echo '<pre>';print_r($data_antrean);exit();
                            $this->Reservasi_antrian_model->simpan($data_antrean);
                            // akhir simpan reservasi antrian
                        }
                    } else {
                        $result = [
                            'status' => 'failed',
                            'errors' => $this->form_validation->error_array()
                        ];
                    }
                } else {
                    $result = [
                        'status' => 'failed',
                        'errors' => $this->form_validation->error_array()
                    ];
                }
                // echo json_encode($result);

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = [
                        'status' => 'failed',
                        'errors' => $this->form_validation->error_array(),
                    ];
                } else {
                    $this->db->trans_commit();
                    $result = [
                        'status' => 200,
                        'message' => 'Berhasil',
                        'id' => $id_perjanjian,
                        'status_prosedur' => $prosedur
                    ];
                }
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array(),
                ];
            }

            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    function tabel()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $data = [
            'mulai' => $post['mulai'] ?? null,
            'akhir' => $post['akhir'] ?? null,
            'id_dokter' => $post['id_dokter'] ?? null,
            'id_ruangan' => $post['id_ruangan']?? null,
            'tujuan' => $post['tujuan']?? null,

        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('admin/perjanjian/tabel', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $mulai = $post['mulai'] ?? null;
        $akhir = $post['akhir'] ?? null;
        $id_dokter = $post['id_dokter'] ?? null;
        $id_ruangan = $post['id_ruangan']?? null;
        $tujuan =  $post['tujuan']?? null;
        $draw = intval($post['draw']);
        $tabel = $this->Reservasi_model->ambil($mulai, $akhir, $id_dokter, $id_ruangan, $tujuan);
        $no = $post['start'];
        $status = null;
        $disabled = null;
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            // mulai status
            if ($t->status == 0) {
                $status = "<p class='text-danger'>Batal</p>";
                $disabled = 'disabled';
            } elseif ($t->status == 1) {
                $status = "Belum diterima";
                $disabled = null;
            } elseif ($t->status == 2) {
                $status = "<p class='text-success'>Diterima</p>";
                $disabled = null;
            }
            // akhir status

            $data[] = [
                ++$no . '.',
                $t->nama,
                $t->norm,
                date('d/m/Y', strtotime($t->tgl_lahir)),
                $t->tujuan_rs,
                $t->kelas_rawat,
                $t->cara_bayar,
                $t->diagnosis,
                $t->ruang,
                $t->tindakan,
                $t->dokter,
                "<button type='button' href='#' class='btn btn-link ubah-tgl-rawat-perjanjian-operasi' data-reservasi='" . $t->id_reservasi . "' data-tanggal='" . $t->tgl_rawat_inap . "' data-bs-toggle='modal' data-bs-target='#modalTanggalRawatPerjanjianOperasi'>
                    " . date('d/m/Y', strtotime($t->tgl_rawat_inap)) . "
                </button>",
                // "<button type='button' href='#' class='btn btn-link ubah-tgl-operasi-perjanjian-operasi' data-perjanjian='" . $t->ID_PERJANJIAN . "' data-tanggal='" . $t->tgl_operasi . "' data-dokter='" . $t->id_dokter . "' data-bs-toggle='modal' data-bs-target='#modalTanggalOperasiPerjanjianOperasi'>
                //     " . date('d/m/Y', strtotime($t->tgl_operasi)) . "
                // </button>",
                "<button type='button' href='#' class='btn btn-link ubah-tgl-operasi-perjanjian-operasi' data-perjanjian='" . $t->ID_PERJANJIAN . "' data-tanggal='" . $t->tgl_operasi . "' data-dokter='" . $t->id_dokter . "' data-id_ruangan='" . $t->ID_RUANGAN . "' data-bs-toggle='modal' data-bs-target='#modalTanggalOperasiPerjanjianOperasi'>
                    " . date('d/m/Y', strtotime($t->tgl_operasi)) . "
                </button>",
                $t->no_telp,
                $status,
                "<div class='btn-group-vertical' role='group'>
                    <a href='" . base_url('admin/perjanjian/form/' . $t->ID_PERJANJIAN) . "' role='button' class='btn btn-primary tblUbahPerjanjian' data-bs-toggle='tooltip' data-bs-placement='right' title='Lihat' " . $disabled . ">
                        Lihat
                    </a>
                    <button type='button' class='btn btn-info tbl-cetak-perjanjian-operasi' data-perjanjian='$t->ID_PERJANJIAN'>
                        Cetak
                    </button>
                    <button type='button' class='btn btn-danger tbl-batal-perjanjian-operasi' data-reservasi='$t->id_reservasi' data-perjanjian='$t->ID_PERJANJIAN' data-bs-toggle='modal' data-bs-target='#modalBatalPerjanjianOperasi' " . $disabled . ">
                        Batal
                    </button>
                </div>"
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Reservasi_model->hitung_semua($mulai, $akhir, $id_dokter, $id_ruangan, $tujuan),
            'recordsFiltered' => $this->Reservasi_model->hitung_tersaring($mulai, $akhir, $id_dokter, $id_ruangan, $tujuan),
            'data' => $data
        ];

        echo json_encode($output);
    }

    function ubah_tanggal_rawat()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        $data = ['tgl_rencanaMasuk' => $post['tanggal']];
        $this->Reservasi_model->ubah($post['id_reservasi'], $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal membatalkan',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }

    function ubah_tanggal_operasi()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        $data = [
            'TANGGAL' => $post['tanggal'],
            'ID_RUANGAN' => $post['ruangan']
        ];        
        $this->Perjanjian_model->ubah($post['idPerjanjian'], $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal membatalkan',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }

    function batal()
    {
        $this->db->trans_begin();
        $session = $this->session->get_userdata();
        $id_pengguna = $session['id'];

        $data = ['status' => 0];
        $this->Reservasi_model->ubah($this->input->post('id'), $data);

        $dataperjanjian = [
            'DELETED_AT' => date('Y-m-d H:i:s'),
            'DELETED_BY' => $id_pengguna,
            'STATUS' => 0
        ];
        $this->Perjanjian_model->batal($this->input->post('idPerjanjian'), $dataperjanjian);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal membatalkan',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }
}

// End of File Perjanjian.php
// Location: ./application/controllers/admin/Perjanjian.php