<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Perjanjian_model extends CI_Model
{
    protected $_table = 'remun_medis.perjanjian rmp';
    protected $_primary_key = 'rmp.id';
    protected $_order_by = 'rmp.id';
    protected $_order_by_type = 'asc';
    protected $_urutan = ['rmp.NOMR' => 'asc'];

    public function __construct()
    {
        parent::__construct();
    }

    public function no_kontrol()
    {
        $query = $this->db->query(
            "SELECT generator.generateNoKontrol('" . $this->input->post('tgl_operasi') . "') KODE"
        )->row();
        return $query->KODE;
    }

    public function simpan($data)
    {
        $this->db->insert('remun_medis.perjanjian', $data);
        return $this->db->insert_id();
    }

    public function ubah($id, $data)
    {
        $this->db->where('remun_medis.perjanjian.id', $id);
        $this->db->update('remun_medis.perjanjian', $data);
    }

    public function batal($id, $data)
    {
        $this->db->where('remun_medis.perjanjian.ID', $id);
        $this->db->update('remun_medis.perjanjian', $data);
    }

    function ambil_kode_booking()
    {
        $query = $this->db->query('SELECT db_reservasi.generateNoReservasiAdm() AS kode_booking');
        return $query->row();
    }

    function ambil_penjamin()
    {
        $this->db->select('mr.ID, mr.DESKRIPSI, pp.NORM');
        $this->db->from('pendaftaran.pendaftaran pp');
        $this->db->join('pendaftaran.penjamin pj', 'pp.NOMOR = pj.NOPEN', 'left');
        $this->db->join('master.referensi mr', 'pj.JENIS = mr.ID AND mr.JENIS = 10', 'left');
        $this->db->where('pp.NORM', $this->input->post('nomr'));
        $this->db->order_by('pp.TANGGAL', 'desc');
        $this->db->limit(1);

        $query = $this->db->get()->row();
        if ($query == null) {
            return 1;
        } else {
            return $query->ID;
        }
    }

    function cek_kuota($dokter = null, $tgl_operasi = null, $id_ruang = null)
    {
        $post = $this->input->post();
        $this->db->select(
            "IF(
                COUNT(*) >= (
                    SELECT rmj.KUOTA
                    FROM remun_medis.jadwal rmj
                    WHERE rmj.DOKTER = '" . $dokter . "'
                    AND rmj.TANGGAL = '" . $tgl_operasi . "'
                    AND rmj.RUANGAN = '" . $id_ruang . "'
                    AND rmj.STATUS != 0
                ), 0 ,1
            ) STATUS,
            IF(
                COUNT(*) = (
                    SELECT rmj.KUOTA
                    FROM remun_medis.jadwal rmj
                    WHERE rmj.DOKTER = '" . $dokter . "'
                    AND rmj.TANGGAL = '" . $tgl_operasi . "'
                    AND rmj.RUANGAN = '" . $id_ruang . "'
                    AND rmj.STATUS != 0
                ) - 1, 0, 1
            ) SISA"
        );
        $this->db->from($this->_table);
        $this->db->where(
            [
                'rmp.ID_DOKTER' => $dokter,
                'rmp.TANGGAL' => $tgl_operasi,
                'rmp.ID_RUANGAN' => $id_ruang,
                'rmp.STATUS !=' => 0
            ]
        );
        $this->db->where('rmp.STATUS_SORE', 0);
        $this->db->where('rmp.RENCANA', 11);

        $query = $this->db->get();
        return $query->row();
    }

    function cek_kuota_sore($dokter = null, $tgl_operasi = null, $id_ruang = null)
    {
        $this->db->select(
            "IF(
                COUNT(*) >= (
                    SELECT IF(rmj.KUOTASORE IS NULL, 0, rmj.KUOTASORE)
                    FROM remun_medis.jadwal rmj
                    WHERE rmj.DOKTER = '" . $dokter . "'
                    AND rmj.TANGGAL = '" . $tgl_operasi . "'
                    AND rmj.RUANGAN = '" . $id_ruang . "'
                    AND rmj.STATUS != 0
                ), 0, 1
            ) STATUS"
        );
        $this->db->from($this->_table);
        $this->db->where(
            [
                'rmp.ID_DOKTER' => $dokter,
                'rmp.TANGGAL' => $tgl_operasi,
                'rmp.ID_RUANGAN' => $id_ruang,
                'rmp.STATUS !=' => 0
            ]
        );

        $query = $this->db->get();
        return $query->row();
    }

    function cek_penjamin($dokter, $tgl_operasi, $id_ruang)
    {
        $post = $this->input->post();
        $this->db->select(
            'COUNT(*) TOTAL, (
                SELECT IF(pj.JENIS = 2, 2, 1)
                FROM pendaftaran.pendaftaran pp
                LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = pp.NOMOR
                WHERE pp.NORM = rmp.NOMR
                AND pp.STATUS != 0
                ORDER BY pp.TANGGAL DESC
                LIMIT 1
            ) PENJAMIN'
        );
        $this->db->from($this->_table);
        $this->db->where('rmp.ID_DOKTER', $dokter);
        $this->db->where('rmp.TANGGAL', $tgl_operasi);
        $this->db->where('rmp.ID_RUANGAN', $id_ruang);
        $this->db->where('rmp.STATUS !=', 0);
        $this->db->where('rmp.RENCANA', 1);
        $this->db->where(
            '(
                SELECT IF(pj.JENIS = 2, 2, 1)
                FROM pendaftaran.pendaftaran pp
                LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = pp.NOMOR
                WHERE pp.NORM = rmp.NOMR
                AND pp.STATUS != 0
                ORDER BY pp.TANGGAL DESC
                LIMIT 1
            ) = 1'
        );

        $query = $this->db->get();
        return $query->row();
    }

    function cek_rencana($flag = 0)
    {
        $post = $this->input->post();
        $tanggal = $post['tgl_operasi'];

        $this->db->select('COUNT(*) TOTAL');
        $this->db->from($this->_table);
        $this->db->where('rmp.STATUS !=', 0);
        $this->db->where('rmp.RENCANA', $post['id_rencana']);

        // mulai periksa flag
        switch ($flag) {
            case 1:
                $this->db->where('rmp.ID_RUANGAN', $post['id_ruang']);
                $this->db->where('rmp.ID_DOKTER', $post['dokter']);
                break;
            case 2:
                $this->db->where('rmp.TANGGAL > DATE(NOW())');
                $this->db->where("rmp.TANGGAL BETWEEN '$tanggal' - INTERVAL  3 DAY AND '$tanggal'");
                $this->db->where('rmp.ID_RUANGAN', $post['id_ruang']);
                $this->db->where('rmp.ID_DOKTER', $post['dokter']);
                $this->db->where('rmp.NOMR', $post['norm']);
                break;
            case 3:
                $this->db->where('rmp.TANGGAL > DATE(NOW())');
                $this->db->where('rmp.ID_RUANGAN', $post['id_ruang']);
                $this->db->where('rmp.ID_DOKTER', $post['dokter']);
                $this->db->where('rmp.NOMR', $post['norm']);
                break;
            default:
                $this->db->where('rmp.TANGGAL', $tanggal);
                break;
        }
        // akhir periksa flag

        $query = $this->db->get()->row();
        return $query->TOTAL;
    }

    function cek_reservasi($id)
    {
        // echo '<pre>';print_r($id);exit();
        $this->db->select('id_perjanjian');
        $this->db->from('db_reservasi.tb_reservasi');
        $this->db->where('id_pendaftaran_operasi', $id);
        $this->db->where('status', 1);

        $query = $this->db->get();
        return $query;
    }

    function cek()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $ambil_penjamin = $this->ambil_penjamin();

        $this->db->select(
            "rmp.ID, rmp.TANGGAL,
            CONCAT('Perjanjian dengan ', master.getNamaLengkapPegawai(md.NIP) ,' di ruang ', mr.DESKRIPSI) INFO"
        );
        $this->db->from($this->_table);
        $this->db->join('master.dokter md', 'rmp.ID_DOKTER = md.ID', 'LEFT');
        $this->db->join('master.ruangan mr', 'rmp.ID_RUANGAN = mr.ID', 'LEFT');

        if ($post['norm'] != 0 || isset($post['norm'])) {
            if ($ambil_penjamin != 2 || $post['id_ruang']) {
                $this->db->where('rmp.ID_PENDAFTARAN_OPERASI', $post['id_pendaftaran_operasi']);
                $this->db->where('rmp.TANGGAL', $post['tgl_operasi']);
                $this->db->where('rmp.ID_DOKTER', $post['dokter']);
                $this->db->where('rmp.STATUS !=', 0);
            } else {
                $this->db->where('rmp.ID_PENDAFTARAN_OPERASI', $post['id_pendaftaran_operasi']);
                $this->db->where('rmp.TANGGAL', $post['tgl_operasi']);
                $this->db->where('rmp.STATUS !=', 0);
            }
        } elseif (isset($post['nomor'])) {
            $this->db->where('rmp.ID_PENDAFTARAN_OPERASI', $post['id_pendaftaran_operasi']);
            $this->db->where('rmp.TANGGAL', $post['tgl_operasi']);
            $this->db->where('rmp.NOMOR', $post['nomor']);
            $this->db->where('rmp.STATUS !=', 0);
        }
        $this->db->order_by('rmp.ID', 'DESC');
        $this->db->limit(1);

        $query = $this->db->get();
        $row = $query->row_array();
        // echo '<pre>';print_r($row);exit();
        $num = $query->num_rows();
        // echo '<pre>';print_r($num);exit();

        $cek_kuota = $this->cek_kuota($post['dokter'], $post['tgl_operasi'], $post['id_ruang']);
        $cek_reservasi = $this->cek_reservasi($post['id_pendaftaran_operasi']);
        // echo '<pre>';print_r($cek_reservasi);exit();
        $reservasi_num = $cek_reservasi->num_rows();
        // echo '<pre>';print_r($reservasi_num);exit();
        $sore = $post['sore'];

        if ($num > 0) {
            return [
                'status' => 503,
                'message' => 'Mohon maaf, Anda tidak dapat melakukan perjanjian ini karena Anda sudah memiliki perjanjian konsultasi dengan dokter yang dituju',
                'id' => $row['ID'],
                'info' => $row['INFO']
            ];
        } elseif (isset($row['TANGGAL'])) {
            if (date('Y-m-d') <= $row['TANGGAL']) {
                return [
                    'status' => 503,
                    'message' => 'Mohon maaf, Anda tidak dapat melakukan perjanjian ini karena Anda sudah memiliki perjanjian konsultasi dengan dokter yang dituju',
                    'id' => $row['ID'],
                    'info' => $row['INFO']
                ];
            }
        } elseif ($cek_kuota->STATUS == 0 && !isset($post['persetujuan_instalasi'])) {
            return [
                'status' => 406,
                'message' => 'Mohon maaf, Anda tidak dapat melakukan perjanjian ini karena jadwal perjanjian yang dituju sudah penuh. Silakan pilih hari lain.'
            ];
        } elseif ($reservasi_num > 0) {
            return [
                'status' => 503,
                'message' => 'Mohon maaf, Anda tidak dapat melakukan perjanjian ini karena reservasi sebelumnya masih aktif.',
                'id' => $cek_reservasi->row_array()['id_perjanjian']
            ];
        } else {
            return [
                'status' => 200,
                'message' => 'Success',
                'data' => [
                    'status_sore' => $sore
                ]
            ];
        }
    }
}

// End of File Perjanjian_model.php
// Location: ./application/models/admin/Perjanjian_model.php