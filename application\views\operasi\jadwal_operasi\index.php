<!-- mulai breadcrumb -->
<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Jadwal Operasi Elektif Terkini</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('beranda') ?>" title="Beranda"><i class="bx bx-home-alt"></i></a>
                </li>
                <li class="breadcrumb-item"><em>Dashboard</em></li>
                <li class="breadcrumb-item active" aria-current="page">Jadwal Operasi Elektif Terkini</li>
            </ol>
        </nav>
    </div>
</div>
<!-- akhir breadcrumb -->

<!-- mulai kartu -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Tabel Pasien</h5>

        <!-- mulai filter tujuan RS -->
        <div class="row mb-3 align-items-center">
            <div class="col-lg-2">
                <label for="tujuanRsJadwalOperasi" class="form-label mb-0">Filter Tujuan RS</label>
            </div>
            <div class="col-lg-4">
                <select class="form-select" id="tujuanRsJadwalOperasi" style="width: 100%;">
                    <option value="">Semua Tujuan RS</option>
                </select>
            </div>
        </div>
        <!-- akhir filter tujuan RS -->

        <!-- mulai judul tab hari -->
        <ul class="nav nav-pills mb-3 ms-1" id="pills-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <a href="#tab-senin" class="nav-link" id="senin-tab" data-bs-toggle="pill" href="#tab-senin" role="tab" aria-selected="false">Senin</a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="#tab-selasa" class="nav-link" id="selasa-tab" data-bs-toggle="pill" href="#tab-selasa" role="tab" aria-selected="false">Selasa</a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="#tab-rabu" class="nav-link" id="rabu-tab" data-bs-toggle="pill" href="#tab-rabu" role="tab" aria-selected="false">Rabu</a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="#tab-kamis" class="nav-link" id="kamis-tab" data-bs-toggle="pill" href="#tab-kamis" role="tab" aria-selected="false">Kamis</a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="#tab-jumat" class="nav-link" id="jumat-tab" data-bs-toggle="pill" href="#tab-jumat" role="tab" aria-selected="false">Jumat</a>
            </li>
        </ul>
        <!-- akhir judul tab hari -->

        <!-- mulai isi tab hari -->
        <div class="tab-content" id="pills-tabContent" style="width:100%; margin-right:15px;">
            <div class="tab-pane fade" id="tab-senin" role="tabpanel">
                <div id="senin"></div>
            </div>
            <div class="tab-pane fade" id="tab-selasa" role="tabpanel">
                <div id="selasa"></div>
            </div>
            <div class="tab-pane fade" id="tab-rabu" role="tabpanel">
                <div id="rabu"></div>
            </div>
            <div class="tab-pane fade" id="tab-kamis" role="tabpanel">
                <div id="kamis"></div>
            </div>
            <div class="tab-pane fade" id="tab-jumat" role="tabpanel">
                <div id="jumat"></div>
            </div>
        </div>
        <!-- akhir isi tab hari -->
    </div>
</div>
<!-- akhir kartu -->

<script>
    $(document).ready(function() {
        // Global variable untuk menyimpan filter tujuan RS
        window.tujuanRsFilter = '';

        // mulai filter tujuan RS
        $('#tujuanRsJadwalOperasi').select2({
            placeholder: 'Pilih tujuan RS',
            allowClear: true,
            ajax: {
                url: '<?= base_url('operasi/Daftar_tunggu/get_tujuan') ?>',
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        }).change(function () {
            window.tujuanRsFilter = $(this).val() || '';
            // Reload tab yang sedang aktif
            reloadActiveTab();
        });
        // akhir filter tujuan RS

        // Function untuk reload tab yang aktif
        function reloadActiveTab() {
            if ($('#senin-tab').hasClass('active')) {
                $('#senin').load("<?= base_url('operasi/Jadwal_operasi/senin') ?>");
            } else if ($('#selasa-tab').hasClass('active')) {
                $('#selasa').load("<?= base_url('operasi/Jadwal_operasi/selasa') ?>");
            } else if ($('#rabu-tab').hasClass('active')) {
                $('#rabu').load("<?= base_url('operasi/Jadwal_operasi/rabu') ?>");
            } else if ($('#kamis-tab').hasClass('active')) {
                $('#kamis').load("<?= base_url('operasi/Jadwal_operasi/kamis') ?>");
            } else if ($('#jumat-tab').hasClass('active')) {
                $('#jumat').load("<?= base_url('operasi/Jadwal_operasi/jumat') ?>");
            }
        }

        // mulai otomatis pilih hari
        //    $('.nav-link').eq(new Date().getDay()).addClass('active');
        $('.nav-link').eq(new Date().getDay() + 1).addClass('active');
        $('.tab-pane').eq(new Date().getDay() - 1).addClass('show active');
        // akhir otomatis pilih hari

        // mulai pilih hari
        $('#senin-tab').click(function() {
            $('#senin').load("<?= base_url('operasi/Jadwal_operasi/senin') ?>");
        });
        if ($('#senin-tab').hasClass('active')) {
            $('#senin').load("<?= base_url('operasi/Jadwal_operasi/senin') ?>");
        }

        $('#selasa-tab').click(function() {
            $('#selasa').load("<?= base_url('operasi/Jadwal_operasi/selasa') ?>");
        });
        if ($('#selasa-tab').hasClass("active")) {
            $('#selasa').load('<?= base_url() ?>operasi/Jadwal_operasi/selasa');
        }

        $('#rabu-tab').click(function() {
            $('#rabu').load("<?= base_url('operasi/Jadwal_operasi/rabu') ?>");
        });
        if ($('#rabu-tab').hasClass("active")) {
            $('#rabu').load('<?= base_url() ?>operasi/Jadwal_operasi/rabu');
        }

        $('#kamis-tab').click(function() {
            $('#kamis').load("<?= base_url('operasi/Jadwal_operasi/kamis') ?>");
        });
        if ($('#kamis-tab').hasClass("active")) {
            $('#kamis').load('<?= base_url() ?>operasi/Jadwal_operasi/kamis');
        }

        $('#jumat-tab').click(function() {
            $('#jumat').load("<?= base_url('operasi/Jadwal_operasi/jumat') ?>");
        });
        if ($('#jumat-tab').hasClass("active")) {
            $('#jumat').load('<?= base_url() ?>operasi/Jadwal_operasi/jumat');
        }
        // akhir pilih hari
    });
</script>