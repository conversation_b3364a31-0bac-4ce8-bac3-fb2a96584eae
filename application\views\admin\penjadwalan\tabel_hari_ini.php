<h5 class="card-title mb-3">Tanggal <?= date('d/m/Y') ?></h5>
<table class="table table-striped table-bordered table-hover w-100" id="tabelHariIniPenjadwalan">
    <thead>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col">Waktu Di<PERSON>at</th>
            <th class="text-center" scope="col"><PERSON><PERSON></th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col"><PERSON>gal <PERSON>hir (Umur)</th>
            <th class="text-center" scope="col">Ruang Kelas/Rawat</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col"><PERSON><PERSON><PERSON></th>
            <th class="text-center" scope="col">Catatan Khusus</th>
            <th class="text-center" scope="col">Status Operasi</th>
            <th class="text-center" scope="col">Aksi</th>
        </tr>
    </thead>
    <tbody></tbody>
    <tfoot>
        <tr>
            <th class="text-center" scope="col">No.</th>
            <th class="text-center" scope="col">Kamar Operasi</th>
            <th class="text-center" scope="col">Tujuan RS</th>
            <th class="text-center" scope="col">Waktu Dibuat</th>
            <th class="text-center" scope="col">Nama Pasien</th>
            <th class="text-center" scope="col">No. RM</th>
            <th class="text-center" scope="col">Tanggal Lahir (Umur)</th>
            <th class="text-center" scope="col">Ruang Kelas/Rawat</th>
            <th class="text-center" scope="col">Diagnosis</th>
            <th class="text-center" scope="col">Tindakan</th>
            <th class="text-center" scope="col">Operator</th>
            <th class="text-center" scope="col">Dokter Anestesi</th>
            <th class="text-center" scope="col">Catatan Khusus</th>
            <th class="text-center" scope="col">Status Operasi</th>
            <th class="text-center" scope="col">Aksi</th>
        </tr>
    </tfoot>
</table>

<script>
    $(document).ready(function () {
        // mulai tabel
        if ($.fn.DataTable.isDataTable('#tabelHariIniPenjadwalan')) {
            $('#tabelHariIniPenjadwalan').DataTable().destroy();
        }
        setTimeout(function () {
            $('#tabelHariIniPenjadwalan').DataTable({
                autoWidth: true,
                order: [
                    [1, 'asc'],
                    [2, 'asc']
                ],
                language: {
                    url: "<?= base_url('assets/plugins/datatable/id.json') ?>"
                },

                clear: true,
                destroy: true,
                processing: true,
                serverSide: true,
                iDisplayLength: 25,
                search: {
                    regex: true
                },

                // mulai ambil data
                ajax: {
                    url: "<?= base_url('admin/Penjadwalan/isi_tabel') ?>",
                    type: 'POST',
                    data: {
                        jenis: 'hari_ini',
                        id_ruangan: '<?= $id_ruangan?? null?>',
                        tujuanOperasi: '<?= $tujuanOperasi?? null?>',
                    }
                },
                // akhir ambil data

                // mulai pendefinisian kolom
                columnDefs: [{
                    targets: [0, 12, 13],
                    orderable: false,
                }],
                // akhir pendefinisian kolom
            });
        }, 10);
        // akhir tabel

        // mulai ubah status
        $(document).on('click', '.tombol-status-penjadwalan', function () {
            let status_baru;
            let id_penjadwalan = $(this).data('id');
            let status = $(this).data('status');
            let proses = $(this).data('proses');

            // mulai status baru
            if (proses === 'sebelumnya') {
                status_baru = --status;
            } else if (proses === 'selanjutnya') {
                status_baru = ++status;
            }
            // akhir status baru

            $.ajax({
                url: "<?= base_url('admin/Penjadwalan/ubah_status') ?>",
                dataType: 'json',
                type: 'post',
                data: {
                    id: id_penjadwalan,
                    status: status_baru,
                },
                success: function (data) {
                    if (data.status === 200) {
                        Lobibox.notify('success', {
                            icon: 'bx bx-check-circle',
                            msg: data.message,
                            sound: false,
                            title: 'Berhasil'
                        });

                        $('#tabelHariIniPenjadwalan').DataTable().ajax.reload();
                    } else {
                        Lobibox.notify('warning', {
                            icon: 'bx bx-error',
                            msg: data.message,
                            sound: false,
                            title: 'Peringatan'
                        });
                    }
                }
            });
        });
        // akhir ubah status
    });
</script>