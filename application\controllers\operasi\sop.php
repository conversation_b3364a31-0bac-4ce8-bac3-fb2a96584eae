<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Sop extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->db2 = $this->load->database('238', TRUE);
        $this->load->model(
            [
                'operasi/Sop_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Standard Operating Procedure',
            'isi' => 'operasi/sop/index',
            'session' => $this->session->get_userdata(),
            // 'dokter' => $this->Dokter_model->dokter_sop(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        // $id_dokter = $post['id_dokter'];
        $draw = intval($post['draw']);
        $tabel = $this->Sop_model->ambil();
        $no = $post['start'];
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            $ext = pathinfo($t->FILE_EDOC, PATHINFO_EXTENSION);
            $button = "<button type='button' class='btn btn-info lihatSOP' id='lihatSOP' data='".$t->FILE_EDOC."' tipe='".$ext."'>
							Lihat
						</button>";
            $data[] = [
                $t->NOMOR_EDOC,
                $t->NOMOR_REVISI,
                $t->NAMA_EDOC,
                $t->TGL_TERBIT,
                $button,
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Sop_model->hitung_semua(),
            'recordsFiltered' => $this->Sop_model->hitung_tersaring(),
            'data' => $data
        ];

        echo json_encode($output);
    }
}

// End of File Sop.php
// Location: ./application/controllers/sop/Sop.php