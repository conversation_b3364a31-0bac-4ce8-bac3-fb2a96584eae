if (window.requestPrint == undefined) {
    window.requestPrint = function (config, callback) {
        let host = location.hostname + (location.port == '' ? '' : ':' + location.port);
        $.ajax({
            url: 'http://***********/webservice/plugins/request-report',
            contentType: 'application/json',
            type: 'POST',
            data: JSON.stringify(config),
            success: function (result, status, xhr) {
                if (status) {
                    if (config.REQUEST_FOR_PRINT) {
                        window.open('printdoc:' + result.content, '_self');
                    } else {
                        window.open(result.url);
                    }
                }
            },
        });
    };
}