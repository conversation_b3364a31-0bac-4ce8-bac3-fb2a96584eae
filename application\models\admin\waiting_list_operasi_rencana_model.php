<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Waiting_list_operasi_rencana_model extends CI_Model
{
    protected $_table = 'medis.tb_waiting_list_operasi_rencana wlor';
    protected $_primary_key = 'wlor.id';
    protected $_order_by = 'wlor.id';
    protected $_order_by_type = 'desc';
    protected $_urutan_kolom = [null, 'id', 'tanggal_rencana', 'keterangan', 'status', null];
    protected $_pencarian_kolom = ['wlor.id', 'wlor.tanggal_rencana', 'wlor.keterangan', 'wlor.status'];

    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'tgl_rencana',
                'label' => 'Tanggal rencana operasi terbaru',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert('medis.tb_waiting_list_operasi_rencana', $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_waiting_list_operasi_rencana.id_wlo', $id);
        $this->db->update('medis.tb_waiting_list_operasi_rencana', $data);
    }

    public function ubah_dari_id($id, $data)
    {
        $this->db->where('medis.tb_waiting_list_operasi_rencana.id', $id);
        $this->db->update('medis.tb_waiting_list_operasi_rencana', $data);
    }

    public function tabel($id)
    {
        $this->db->select('wlor.id, wlor.tanggal_rencana, wlor.keterangan, wlor.created_at, wlor.status');
        $this->db->from($this->_table);
        $this->db->where('wlor.id_wlo', $id);
        $this->db->order_by('wlor.id', 'desc');

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil($id)
    {
        $this->tabel($id);
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring($id)
    {
        $this->tabel($id);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua($id)
    {
        $this->tabel($id);
        return $this->db->count_all_results();
    }
}

// End of File Waiting_list_operasi_rencana_model.php
// Location: ./application/models/admin/Waiting_list_operasi_rencana_model.php