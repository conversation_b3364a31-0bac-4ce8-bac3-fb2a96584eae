<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Smf extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'Pengguna_model',
                'Referensi_model',
                'admin/Akses_model',
                'admin/manajemen_pengguna/Smf_sippo_model',
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Manajemen Pengguna per SMF',
            'isi' => 'admin/manajemen_pengguna/smf/index',
            'session' => $this->session->get_userdata(),
            'hitung_akses' => $this->Akses_model->list('jumlah'),
            'akses' => $this->Akses_model->list()
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function form($id_smf = null)
    {
        $data = [
            'judul' => 'Form Manajemen Pengguna per SMF',
            'isi' => 'admin/manajemen_pengguna/smf/form',
            'session' => $this->session->get_userdata(),
            'smf' => $this->Referensi_model->smf(),
            'akses' => $this->Akses_model->list()
        ];

        // mulai periksa id
        if (isset($id_smf)) {
            $data['checked'] = null;
            $data['id_smf'] = $id_smf;
            $data['detail'] = $this->Smf_sippo_model->detail($id_smf);
        }
        // akhir periksa id

        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function simpan()
    {
        $session = $this->session->get_userdata();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->Smf_sippo_model->rules());

            if ($this->form_validation->run() == true) {
                // mulai data
                $data = [
                    'id_smf' => $post['smf'],
                    'akses_sippo' => json_encode($post['akses']),
                    'status' => 1,
                    'oleh' => $session['id']
                ];
                // echo '<pre>';print_r($data);exit();
                // akhir data
                $this->Smf_sippo_model->simpan($data);

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array(),
                ];
            }

            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $draw = intval($post['draw']);
        $tabel = $this->Smf_sippo_model->ambil();
        $no = $post['start'];
        $i = 0;
        $akses = $this->Akses_model->list();
        $isi_akses = [];
        $checked = null;
        $disabled = null;
        $disabled_link = null;
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            $j = 2; // tempat mulainya checkbox di tabel
            $isi_akses = json_decode($t->akses_sippo);
            $data[] = [
                ++$no . '.',
                $t->smf
            ];

            // mulai akses
            foreach ($akses as $a) {
                // mulai periksa isi
                if (in_array($a['id'], $isi_akses)) {
                    $checked = 'checked';
                } else {
                    $checked = null;
                }
                // akhir periksa isi

                $data[$i][$j++] = "<input type='checkbox' name='akses[]' class='form-check-input akses-manajemen-smf' id='aksesManajemenSMF" . $t->id_smf . '-' . $a['id'] . "' value='" . $a['id'] . "' aria-label='" . $a['nama'] . "' $checked disabled>";
            }
            // akhir akses

            // mulai aksi
            $data[$i][$j++] = "<div class='btn-group' role='group'>
                                    <a href='" . base_url('admin/manajemen_pengguna/smf/form/' . $t->id_smf) . "' role='button' class='btn btn-warning btn-sm tblUbahManajemenSMF $disabled' $disabled_link>
                                        Ubah
                                    </a>
                                    <button type='button' class='btn btn-danger btn-sm tbl-hapus-manajemen-smf' data-id='$t->id_smf' data-bs-toggle='modal' data-bs-target='#modalHapusManajemenSMF' $disabled>
                                        Hapus
                                    </button>
                                </div>";
            $i++;
            // akhir aksi
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Smf_sippo_model->hitung_semua(),
            'recordsFiltered' => $this->Smf_sippo_model->hitung_tersaring(),
            'data' => $data
        ];

        echo json_encode($output);
    }

    function hapus()
    {
        $session = $this->session->get_userdata();
        $this->db->trans_begin();
        $data = [
            'id_smf' => $this->input->post('id'),
            'akses_sippo' => null,
            'status' => 0,
            'oleh' => $session['id']
        ];
        // echo '<pre>';print_r($data);exit();
        $this->Smf_sippo_model->simpan($data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = [
                'status' => 'failed',
                'message' => 'Gagal menghapus',
            ];
        } else {
            $this->db->trans_commit();
            $result = [
                'status' => 200,
                'message' => 'Berhasil',
            ];
        }

        // echo '<pre>';print_r($result);exit();
        echo json_encode($result);
    }
}

// End of File Smf.php
// Location: ./application/controllers/admin/manajemen_pengguna/Smf.php