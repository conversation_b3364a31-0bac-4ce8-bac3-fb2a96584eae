<?php
defined('BASEPATH') or exit('No direct script access allowed');

class History_model extends CI_Model
{
    protected $_table = 'perjanjian.penjadwalan_operasi ppo';
    protected $_primary_key = 'ppo.id';
    protected $_order_by = 'ppo.id';
    protected $_order_by_type = 'desc';
    protected $_urutan = ['tgl_operasi' => 'desc', 'tgl_input' => 'desc', 'kamar_operasi' => 'asc'];

    protected $_urutan_kolom = [
        null,
        'tgl_operasi',
        'kamar_operasi',
        'tgl_dibuat',
        'nama',
        'norm',
        'tgl_lahir_umur',
        'ruang_rawat',
        'diagnosis',
        'tindakan',
        'dokter_operator',
        'dokter_anestesi',
        'catatan_khusus'
    ];

    protected $_pencarian_kolom_dengan_perjanjian = [
        'rmp.TANGGAL',
        'dk.nama',
        'IFNULL(ppo.created_at, rr.tgl_input)',
        'ps.NAMA',
        'wlo.norm',
        "CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>')",
        "IFNULL(
            (
                SELECT mr.DESKRIPSI ruangan
                FROM pendaftaran.pendaftaran pp
                    LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                    LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                    AND pp.NORM = wlo.norm
                    AND mr.JENIS_KUNJUNGAN = 3
                ORDER BY pk.MASUK ASC
                LIMIT 1
            ), mr.DESKRIPSI
        )", // ruang_rawat
        'wlo.diagnosis',
        'wlo.tindakan',
        'master.getNamaLengkapPegawai(d.NIP)',
        'master.getNamaLengkapPegawai(da.NIP)',
        'tpo.catatan_khusus',
        "CASE 
            WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
            WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
            ELSE 'Operasi Swasta'
        END"


    ];

    protected $_pencarian_kolom_tanpa_perjanjian = [
        'ppo.tgl_operasi',
        'dk.nama',
        'IFNULL(ppo.created_at, rr.tgl_input)',
        'ps.NAMA',
        'wlo.norm',
        "CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>')",
        "IFNULL(
            (
                SELECT mr.DESKRIPSI ruangan
                FROM pendaftaran.pendaftaran pp
                    LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                    LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                    AND pp.NORM = wlo.norm
                    AND mr.JENIS_KUNJUNGAN = 3
                ORDER BY pk.MASUK ASC
                LIMIT 1
            ), mr.DESKRIPSI
        )", // ruang_rawat
        'wlo.diagnosis',
        'wlo.tindakan',
        'master.getNamaLengkapPegawai(d.NIP)',
        'master.getNamaLengkapPegawai(da.NIP)',
        'tpo.catatan_khusus',
        "CASE 
            WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
            WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
            ELSE 'Operasi Swasta'
        END"

    ];

    public function dengan_perjanjian($mulai = null, $akhir = null, $id_ruangan = null, $tujuanOperasi = null)
    {
        $this->db->select(
            "rmp.ID, ppo.id id_penjadwalan, rr.id id_reservasi, IFNULL(rmp.TANGGAL, ppo.tgl_operasi) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(rr.tgl_input, ppo.created_at) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            master.getNamaLengkapPegawai(d.NIP) dokter_operator, master.getNamaLengkapPegawai(da.NIP) dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat, 
            CASE 
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        );
        $this->db->from('remun_medis.perjanjian rmp');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang IN ('105090101', '105090104')", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');
        
        $this->db->where('rmp.STATUS', 1);
        $this->db->like('rmp.ID_RUANGAN', '1050901', 'after');
        if (!empty($id_ruangan)) {
            $this->db->where('tpo.ruang_operasi', $id_ruangan); // sudah diganti jadi misal mau aktif cari waktu update ini 
        }
        // tujuan_rs filter
        if (!empty($tujuanOperasi)) {
            switch ((int)$tujuanOperasi) {
                case 2:
                    $this->db->group_start();

                    // ppo.id IS NULL → rr.id_cara_bayar = 2
                    $this->db->group_start();
                    $this->db->where('ppo.id IS NULL', null, false);
                    $this->db->where('rr.id_cara_bayar', 2);
                    $this->db->group_end();

                    // ppo.id IS NOT NULL → ppo.tujuan_rs = 2
                    $this->db->or_group_start();
                    $this->db->where('ppo.id IS NOT NULL', null, false);
                    $this->db->where('ppo.tujuan_rs', 2);
                    $this->db->group_end();

                    $this->db->group_end();
                    break;

                case 16:
                    $this->db->group_start();

                    // ppo.id IS NULL → rr.id_cara_bayar != 2
                    $this->db->group_start();
                    $this->db->where('ppo.id IS NULL', null, false);
                    $this->db->where('rr.id_cara_bayar !=', 2);
                    $this->db->group_end();

                    // ppo.id IS NOT NULL → ppo.tujuan_rs = 16
                    $this->db->or_group_start();
                    $this->db->where('ppo.id IS NOT NULL', null, false);
                    $this->db->where('ppo.tujuan_rs', 16);
                    $this->db->group_end();

                    $this->db->group_end();
                    break;

                default:
                    break;
            }
        }

        // mulai periksa tanggal
        if ($mulai != null) {
            $this->db->where('rmp.TANGGAL >=', $mulai);
        }
        if ($akhir != null) {
            $this->db->where('rmp.TANGGAL <=', $akhir);
        } else {
            $this->db->where('rmp.TANGGAL <= CURDATE()');
        }
        // akhir periksa tanggal

        // $this->db->where('(ppo.status = 1 OR ppo.id IS NULL)');
        $this->db->group_start();

        // Bagian 1: status = 1 atau id IS NULL dan tanggal operasi < hari ini
        $this->db->group_start();
            $this->db->group_start(); // (status = 1 OR id IS NULL)
                $this->db->where('ppo.status', 1);
                $this->db->or_where('ppo.id IS NULL', null, false);
            $this->db->group_end();
    
            $this->db->group_start(); // (ppo.tgl_operasi < hari ini OR rmp.TANGGAL < hari ini)
                $this->db->where('DATE(ppo.tgl_operasi) < CURDATE()', null, false);
                $this->db->or_where('DATE(rmp.TANGGAL) < CURDATE()', null, false);
            $this->db->group_end();
        $this->db->group_end();
    
        // Bagian 2: status = 4 dan tgl operasi = hari ini
        $this->db->or_group_start();
            $this->db->where('ppo.status', 4);
            $this->db->group_start();
                $this->db->where('DATE(ppo.tgl_operasi) = CURDATE()', null, false);
                $this->db->or_where('DATE(rmp.TANGGAL) = CURDATE()', null, false);
            $this->db->group_end();
        $this->db->group_end();
    
    $this->db->group_end();
    



        $i = 0;
        foreach ($this->_pencarian_kolom_dengan_perjanjian as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom_dengan_perjanjian) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }
        return $this;
    }

    public function tanpa_perjanjian($mulai = null, $akhir = null, $id_ruangan = null, $tujuanOperasi = null)
    {
        $this->db->select(
            "rmp.ID, ppo.id id_penjadwalan, rr.id id_reservasi, IFNULL(ppo.tgl_operasi, rmp.TANGGAL) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(ppo.created_at, rr.tgl_input) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            master.getNamaLengkapPegawai(d.NIP) dokter_operator, master.getNamaLengkapPegawai(da.NIP) dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
             CASE 
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        );
        $this->db->from($this->_table);
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = ppo.id_waiting_list_operasi', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang IN ('105090101', '105090104')", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'remun_medis.perjanjian rmp',
            'rmp.ID_WAITING_LIST_OPERASI = wlo.id AND rmp.STATUS = 1 AND rmp.TANGGAL = ppo.tgl_operasi',
            'left'
        );
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');

        // mulai periksa tanggal
        if ($mulai != null) {
            $this->db->where('ppo.tgl_operasi >=', $mulai);
        }
        if ($akhir != null) {
            $this->db->where('ppo.tgl_operasi <=', $akhir);
        } else {
            $this->db->where('ppo.tgl_operasi <= CURDATE()');
        }
        // akhir periksa tanggal

        // $this->db->where('ppo.status', 1);
        $this->db->group_start();

        // status = 1 dan tgl operasi < hari ini
        $this->db->group_start();
            $this->db->where('ppo.status', 1);
            $this->db->group_start();
                $this->db->where('DATE(ppo.tgl_operasi) < CURDATE()', null, false);
                $this->db->or_where('DATE(rmp.TANGGAL) < CURDATE()', null, false);
            $this->db->group_end();
        $this->db->group_end();
    
        // status = 4 atau 5 dan tgl operasi = hari ini
        $this->db->or_group_start();
            $this->db->group_start();
                $this->db->where('ppo.status', 4);
                $this->db->or_where('ppo.status', 5);
            $this->db->group_end();
            $this->db->group_start();
                $this->db->where('DATE(ppo.tgl_operasi) = CURDATE()', null, false);
                $this->db->or_where('DATE(rmp.TANGGAL) = CURDATE()', null, false);
            $this->db->group_end();
        $this->db->group_end();
    
    $this->db->group_end();
    


        if (!empty($id_ruangan)) {
            $this->db->where('tpo.ruang_operasi', $id_ruangan); // sudah diganti jadi misal mau aktif cari waktu update ini 
        }
        // tujuan_rs filter
        if (!empty($tujuanOperasi)) {
            switch ((int)$tujuanOperasi) {
                case 2:
                    $this->db->group_start();

                    // ppo.id IS NULL → rr.id_cara_bayar = 2
                    $this->db->group_start();
                    $this->db->where('ppo.id IS NULL', null, false);
                    $this->db->where('rr.id_cara_bayar', 2);
                    $this->db->group_end();

                    // ppo.id IS NOT NULL → ppo.tujuan_rs = 2
                    $this->db->or_group_start();
                    $this->db->where('ppo.id IS NOT NULL', null, false);
                    $this->db->where('ppo.tujuan_rs', 2);
                    $this->db->group_end();

                    $this->db->group_end();
                    break;

                case 16:
                    $this->db->group_start();

                    // ppo.id IS NULL → rr.id_cara_bayar != 2
                    $this->db->group_start();
                    $this->db->where('ppo.id IS NULL', null, false);
                    $this->db->where('rr.id_cara_bayar !=', 2);
                    $this->db->group_end();

                    // ppo.id IS NOT NULL → ppo.tujuan_rs = 16
                    $this->db->or_group_start();
                    $this->db->where('ppo.id IS NOT NULL', null, false);
                    $this->db->where('ppo.tujuan_rs', 16);
                    $this->db->group_end();

                    $this->db->group_end();
                    break;

                default:
                    break;
            }
        }

        $i = 0;
        foreach ($this->_pencarian_kolom_tanpa_perjanjian as $pk) { // Loop kolom
            if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
                $_POST['search']['value'] = $_POST['search']['value'];
            } else {
                $_POST['search']['value'] = '';
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom_tanpa_perjanjian) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }
        return $this;
    }

    public function ambil($mulai = null, $akhir = null, $id_ruangan = null, $tujuanOperasi = null)
    {
        // mulai dengan perjanjian
        $this->dengan_perjanjian($mulai, $akhir, $id_ruangan, $tujuanOperasi);
        $query_dp = $this->db->get_compiled_select();
        $this->db->reset_query(); // reset query builder
        // akhir dengan perjanjian

        // mulai tanpa perjanjian
        $this->tanpa_perjanjian($mulai, $akhir, $id_ruangan, $tujuanOperasi);
        $query_tp = $this->db->get_compiled_select();
        // akhir tanpa perjanjian

        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = isset($_POST['length']) ? $_POST['length'] : '10';
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = (int) $_POST['start']; // Cast to integer to ensure proper type
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $query = $this->db->query(
                $query_dp . " UNION " . $query_tp . "
                ORDER BY " . $this->_urutan_kolom[$_POST["order"]["0"]["column"]] . " " . $_POST["order"]["0"]["dir"] . "
                LIMIT " . $_POST["start"] . ", " . $_POST["length"]
            );
        } elseif (isset($this->_urutan)) {
            $urutan = $this->_urutan;
            $query = $this->db->query(
                $query_dp . " UNION " . $query_tp . "
                ORDER BY " . key($urutan) . " " . $urutan[key($urutan)] . "
                LIMIT " . $_POST["start"] . ", " . $_POST["length"]
            );
        }

        // print_r($query); exit;
        return $query->result();
    }

    public function hitung_tersaring($mulai = null, $akhir = null, $id_ruangan = null, $tujuanOperasi = null)
    {
        // mulai dengan perjanjian
        $this->dengan_perjanjian($mulai, $akhir, $id_ruangan, $tujuanOperasi);
        $query_dp = $this->db->get_compiled_select();
        $this->db->reset_query(); // reset query builder
        // akhir dengan perjanjian

        // mulai tanpa perjanjian
        $this->tanpa_perjanjian($mulai, $akhir, $id_ruangan, $tujuanOperasi);
        $query_tp = $this->db->get_compiled_select();
        // akhir tanpa perjanjian

        $query = $this->db->query($query_dp . " UNION " . $query_tp);
        return $query->num_rows();
    }

    public function hitung_semua($mulai = null, $akhir = null, $id_ruangan = null, $tujuanOperasi = null)
    {
        // mulai dengan perjanjian
        $this->dengan_perjanjian($mulai, $akhir, $id_ruangan, $tujuanOperasi);
        $query_dp = $this->db->get_compiled_select();
        $this->db->reset_query(); // reset query builder
        // akhir dengan perjanjian

        // mulai tanpa perjanjian
        $this->tanpa_perjanjian($mulai, $akhir, $id_ruangan, $tujuanOperasi);
        $query_tp = $this->db->get_compiled_select();
        // akhir tanpa perjanjian

        $query = $this->db->query($query_dp . " UNION " . $query_tp);
        return $query->num_rows();
    }
}

// End of File History_model.php
// Location: ./application/models/History_model.php