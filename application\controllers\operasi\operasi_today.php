<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Operasi_today extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'admin/Perjanjian_model',
                'operasi/Operasi_today_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Operasi Hari Ini',
            'isi' => 'operasi/operasi_today/index',
            'session' => $this->session->get_userdata(),
            // 'dokter' => $this->Dokter_model->dokter_operasi(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        $draw = intval($post['draw']);
        $PTUJUAN_RS = isset($post['PTUJUAN_RS']) ? $post['PTUJUAN_RS'] : '';

        $tabel = $this->Operasi_today_model->ambil($PTUJUAN_RS);
        $no = $post['start'];
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            switch ($t->test) {
                case 1:
                    $badgeClass = 'bg-primary';
                    break;
                case 2:
                    $badgeClass = 'bg-success';
                    break;
                case 3:
                    $badgeClass = 'bg-warning';
                    break;
                case 4:
                    $badgeClass = 'bg-info';
                    break;
                case 5:
                    $badgeClass = 'bg-secondary'; 
                    break;
                default:
                    $badgeClass = 'bg-dark'; 
                    break;
            }
            $status = '<span class="badge ' . $badgeClass . '">' . $t->STS . '</span>';

            $data[] = [
                ++$no . '.',
                $t->jam_operasi,
                $t->nama_pasien,
                $t->nomr,
                $t->ruang_operasi,
                $t->tgl_lahir_umur,
                $t->ruang_rawat,
                $t->tindakan,
                $t->operator,
                $t->dr_anastesi,
                $t->jenis_anastesi,
                // $t->STS,
                $status
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Operasi_today_model->hitung_semua($PTUJUAN_RS),
            'recordsFiltered' => $this->Operasi_today_model->hitung_tersaring($PTUJUAN_RS),
            'data' => $data
        ];

        echo json_encode($output);
    }
}

// End of File Operasi_today.php
// Location: ./application/controllers/operasi/Operasi_today.php