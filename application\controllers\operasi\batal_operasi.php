<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Batal_operasi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'admin/Perjanjian_model',
                'operasi/Batal_operasi_model'
            ]
        );
        $session = $this->session->get_userdata();

        if ($session['sudah_masuk'] == false) {
            redirect('/');
        }

        // Unset session penjadwalan ketika mengakses menu lain
        $this->session->unset_userdata(['penjadwalan_mulai', 'penjadwalan_akhir', 'penjadwalan_hari', 'tujuanOperasi']);
    }

    function index()
    {
        $data = [
            'judul' => 'Pembatalan Operasi Hari Ini',
            'isi' => 'operasi/batal_operasi/index',
            'session' => $this->session->get_userdata(),
            // 'dokter' => $this->Dokter_model->dokter_operasi(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('layouts/wrapper', $data);
    }

    function isi_tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        $draw = intval($post['draw']);
        $PTUJUAN_RS = isset($post['PTUJUAN_RS']) ? $post['PTUJUAN_RS'] : '';

        $tabel = $this->Batal_operasi_model->ambil($PTUJUAN_RS);
        $no = $post['start'];
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            $data[] = [
                ++$no . '.',
                $t->nama_pasien,
                $t->nomr,
                $t->DIAGNOSA,
                $t->TINDAKAN,
                $t->DPJP,
                $t->ruang_rawat,
                $t->alasan_batal,
                $t->tgl_batal,
                $t->jam_batal
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->Batal_operasi_model->hitung_semua($PTUJUAN_RS),
            'recordsFiltered' => $this->Batal_operasi_model->hitung_tersaring($PTUJUAN_RS),
            'data' => $data
        ];

        echo json_encode($output);
    }
}

// End of File Jadwal_operasi.php
// Location: ./application/controllers/operasi/Jadwal_operasi.php